{"name": "@types/node-fetch", "version": "2.6.12", "description": "TypeScript definitions for node-fetch", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-fetch", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/torstenwerner"}, {"name": "<PERSON><PERSON>", "githubUsername": "nikcorg", "url": "https://github.com/nikcorg"}, {"name": "<PERSON><PERSON>", "githubUsername": "v<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vinaybedre"}, {"name": "<PERSON>", "githubUsername": "kyranet", "url": "https://github.com/kyranet"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Andrew<PERSON><PERSON>ham"}, {"name": "<PERSON>", "githubUsername": "JasonLi914", "url": "https://github.com/JasonLi914"}, {"name": "<PERSON>", "githubUsername": "southpolesteve", "url": "https://github.com/southpolesteve"}, {"name": "ExE Boss", "githubUsername": "ExE-Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON>", "githubUsername": "alexa<PERSON><PERSON><PERSON>n", "url": "https://github.com/alexandrusavin"}, {"name": "<PERSON>", "githubUsername": "OmgImAlexis", "url": "https://github.com/OmgImAlexis"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "kbkk", "url": "https://github.com/kbkk"}, {"name": "<PERSON>", "githubUsername": "glasser", "url": "https://github.com/glasser"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-fetch"}, "scripts": {}, "dependencies": {"@types/node": "*", "form-data": "^4.0.0"}, "peerDependencies": {}, "typesPublisherContentHash": "ebdd1146f8abd6b712fd73289dda67142f5307fc11c3c5f321f8da964a43054e", "typeScriptVersion": "4.9"}