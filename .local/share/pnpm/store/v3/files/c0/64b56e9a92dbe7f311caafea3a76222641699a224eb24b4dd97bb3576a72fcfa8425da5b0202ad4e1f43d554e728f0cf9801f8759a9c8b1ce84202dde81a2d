/**
 * Fuse.js v7.1.0 - Lightweight fuzzy-search (http://fusejs.io)
 *
 * Copyright (c) 2025 Kiro Risk (http://kiro.me)
 * All Rights Reserved. Apache Software License 2.0
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 */
function t(t){return Array.isArray?Array.isArray(t):"[object Array]"===h(t)}const e=1/0;function n(t){return null==t?"":function(t){if("string"==typeof t)return t;let n=t+"";return"0"==n&&1/t==-e?"-0":n}(t)}function s(t){return"string"==typeof t}function i(t){return"number"==typeof t}function r(t){return!0===t||!1===t||function(t){return u(t)&&null!==t}(t)&&"[object Boolean]"==h(t)}function u(t){return"object"==typeof t}function c(t){return null!=t}function o(t){return!t.trim().length}function h(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}const a=t=>`Missing ${t} property in key`,l=t=>`Property 'weight' in key '${t}' must be a positive integer`,d=Object.prototype.hasOwnProperty;class g{constructor(t){this._keys=[],this._keyMap={};let e=0;t.forEach((t=>{let n=f(t);this._keys.push(n),this._keyMap[n.id]=n,e+=n.weight})),this._keys.forEach((t=>{t.weight/=e}))}get(t){return this._keyMap[t]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function f(e){let n=null,i=null,r=null,u=1,c=null;if(s(e)||t(e))r=e,n=A(e),i=p(e);else{if(!d.call(e,"name"))throw new Error(a("name"));const t=e.name;if(r=t,d.call(e,"weight")&&(u=e.weight,u<=0))throw new Error(l(t));n=A(t),i=p(t),c=e.getFn}return{path:n,id:i,weight:u,src:r,getFn:c}}function A(e){return t(e)?e:e.split(".")}function p(e){return t(e)?e.join("."):e}var C={isCaseSensitive:!1,ignoreDiacritics:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(t,e)=>t.score===e.score?t.idx<e.idx?-1:1:t.score<e.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,...{useExtendedSearch:!1,getFn:function(e,u){let o=[],h=!1;const a=(e,u,l)=>{if(c(e))if(u[l]){const d=e[u[l]];if(!c(d))return;if(l===u.length-1&&(s(d)||i(d)||r(d)))o.push(n(d));else if(t(d)){h=!0;for(let t=0,e=d.length;t<e;t+=1)a(d[t],u,l+1)}else u.length&&a(d,u,l+1)}else o.push(e)};return a(e,s(u)?u.split("."):u,0),h?o:o[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1}};const m=/[^ ]+/g;class F{constructor({getFn:t=C.getFn,fieldNormWeight:e=C.fieldNormWeight}={}){this.norm=function(t=1,e=3){const n=new Map,s=Math.pow(10,e);return{get(e){const i=e.match(m).length;if(n.has(i))return n.get(i);const r=1/Math.pow(i,.5*t),u=parseFloat(Math.round(r*s)/s);return n.set(i,u),u},clear(){n.clear()}}}(e,3),this.getFn=t,this.isCreated=!1,this.setIndexRecords()}setSources(t=[]){this.docs=t}setIndexRecords(t=[]){this.records=t}setKeys(t=[]){this.keys=t,this._keysMap={},t.forEach(((t,e)=>{this._keysMap[t.id]=e}))}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,s(this.docs[0])?this.docs.forEach(((t,e)=>{this._addString(t,e)})):this.docs.forEach(((t,e)=>{this._addObject(t,e)})),this.norm.clear())}add(t){const e=this.size();s(t)?this._addString(t,e):this._addObject(t,e)}removeAt(t){this.records.splice(t,1);for(let e=t,n=this.size();e<n;e+=1)this.records[e].i-=1}getValueForItemAtKeyId(t,e){return t[this._keysMap[e]]}size(){return this.records.length}_addString(t,e){if(!c(t)||o(t))return;let n={v:t,i:e,n:this.norm.get(t)};this.records.push(n)}_addObject(e,n){let i={i:n,$:{}};this.keys.forEach(((n,r)=>{let u=n.getFn?n.getFn(e):this.getFn(e,n.path);if(c(u))if(t(u)){let e=[];const n=[{nestedArrIndex:-1,value:u}];for(;n.length;){const{nestedArrIndex:i,value:r}=n.pop();if(c(r))if(s(r)&&!o(r)){let t={v:r,i:i,n:this.norm.get(r)};e.push(t)}else t(r)&&r.forEach(((t,e)=>{n.push({nestedArrIndex:e,value:t})}))}i.$[r]=e}else if(s(u)&&!o(u)){let t={v:u,n:this.norm.get(u)};i.$[r]=t}})),this.records.push(i)}toJSON(){return{keys:this.keys,records:this.records}}}function M(t,e,{getFn:n=C.getFn,fieldNormWeight:s=C.fieldNormWeight}={}){const i=new F({getFn:n,fieldNormWeight:s});return i.setKeys(t.map(f)),i.setSources(e),i.create(),i}function E(t,{errors:e=0,currentLocation:n=0,expectedLocation:s=0,distance:i=C.distance,ignoreLocation:r=C.ignoreLocation}={}){const u=e/t.length;if(r)return u;const c=Math.abs(s-n);return i?u+c/i:c?1:u}const D=32;function B(t,e,n,{location:s=C.location,distance:i=C.distance,threshold:r=C.threshold,findAllMatches:u=C.findAllMatches,minMatchCharLength:c=C.minMatchCharLength,includeMatches:o=C.includeMatches,ignoreLocation:h=C.ignoreLocation}={}){if(e.length>D)throw new Error(`Pattern length exceeds max of ${D}.`);const a=e.length,l=t.length,d=Math.max(0,Math.min(s,l));let g=r,f=d;const A=c>1||o,p=A?Array(l):[];let m;for(;(m=t.indexOf(e,f))>-1;){let t=E(e,{currentLocation:m,expectedLocation:d,distance:i,ignoreLocation:h});if(g=Math.min(t,g),f=m+a,A){let t=0;for(;t<a;)p[m+t]=1,t+=1}}f=-1;let F=[],M=1,B=a+l;const x=1<<a-1;for(let s=0;s<a;s+=1){let r=0,c=B;for(;r<c;){E(e,{errors:s,currentLocation:d+c,expectedLocation:d,distance:i,ignoreLocation:h})<=g?r=c:B=c,c=Math.floor((B-r)/2+r)}B=c;let o=Math.max(1,d-c+1),C=u?l:Math.min(d+c,l)+a,m=Array(C+2);m[C+1]=(1<<s)-1;for(let r=C;r>=o;r-=1){let u=r-1,c=n[t.charAt(u)];if(A&&(p[u]=+!!c),m[r]=(m[r+1]<<1|1)&c,s&&(m[r]|=(F[r+1]|F[r])<<1|1|F[r+1]),m[r]&x&&(M=E(e,{errors:s,currentLocation:u,expectedLocation:d,distance:i,ignoreLocation:h}),M<=g)){if(g=M,f=u,f<=d)break;o=Math.max(1,2*d-f)}}if(E(e,{errors:s+1,currentLocation:d,expectedLocation:d,distance:i,ignoreLocation:h})>g)break;F=m}const y={isMatch:f>=0,score:Math.max(.001,M)};if(A){const t=function(t=[],e=C.minMatchCharLength){let n=[],s=-1,i=-1,r=0;for(let u=t.length;r<u;r+=1){let u=t[r];u&&-1===s?s=r:u||-1===s||(i=r-1,i-s+1>=e&&n.push([s,i]),s=-1)}return t[r-1]&&r-s>=e&&n.push([s,r-1]),n}(p,c);t.length?o&&(y.indices=t):y.isMatch=!1}return y}function x(t){let e={};for(let n=0,s=t.length;n<s;n+=1){const i=t.charAt(n);e[i]=(e[i]||0)|1<<s-n-1}return e}const y=String.prototype.normalize?t=>t.normalize("NFD").replace(/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08D3-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C04\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u1885\u1886\u18A9\u1920-\u192B\u1930-\u193B\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF2-\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DF9\u1DFB-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F]/g,""):t=>t;class L{constructor(t,{location:e=C.location,threshold:n=C.threshold,distance:s=C.distance,includeMatches:i=C.includeMatches,findAllMatches:r=C.findAllMatches,minMatchCharLength:u=C.minMatchCharLength,isCaseSensitive:c=C.isCaseSensitive,ignoreDiacritics:o=C.ignoreDiacritics,ignoreLocation:h=C.ignoreLocation}={}){if(this.options={location:e,threshold:n,distance:s,includeMatches:i,findAllMatches:r,minMatchCharLength:u,isCaseSensitive:c,ignoreDiacritics:o,ignoreLocation:h},t=c?t:t.toLowerCase(),t=o?y(t):t,this.pattern=t,this.chunks=[],!this.pattern.length)return;const a=(t,e)=>{this.chunks.push({pattern:t,alphabet:x(t),startIndex:e})},l=this.pattern.length;if(l>D){let t=0;const e=l%D,n=l-e;for(;t<n;)a(this.pattern.substr(t,D),t),t+=D;if(e){const t=l-D;a(this.pattern.substr(t),t)}}else a(this.pattern,0)}searchIn(t){const{isCaseSensitive:e,ignoreDiacritics:n,includeMatches:s}=this.options;if(t=e?t:t.toLowerCase(),t=n?y(t):t,this.pattern===t){let e={isMatch:!0,score:0};return s&&(e.indices=[[0,t.length-1]]),e}const{location:i,distance:r,threshold:u,findAllMatches:c,minMatchCharLength:o,ignoreLocation:h}=this.options;let a=[],l=0,d=!1;this.chunks.forEach((({pattern:e,alphabet:n,startIndex:g})=>{const{isMatch:f,score:A,indices:p}=B(t,e,n,{location:i+g,distance:r,threshold:u,findAllMatches:c,minMatchCharLength:o,includeMatches:s,ignoreLocation:h});f&&(d=!0),l+=A,f&&p&&(a=[...a,...p])}));let g={isMatch:d,score:d?l/this.chunks.length:1};return d&&s&&(g.indices=a),g}}class k{constructor(t){this.pattern=t}static isMultiMatch(t){return _(t,this.multiRegex)}static isSingleMatch(t){return _(t,this.singleRegex)}search(){}}function _(t,e){const n=t.match(e);return n?n[1]:null}class v extends k{constructor(t,{location:e=C.location,threshold:n=C.threshold,distance:s=C.distance,includeMatches:i=C.includeMatches,findAllMatches:r=C.findAllMatches,minMatchCharLength:u=C.minMatchCharLength,isCaseSensitive:c=C.isCaseSensitive,ignoreDiacritics:o=C.ignoreDiacritics,ignoreLocation:h=C.ignoreLocation}={}){super(t),this._bitapSearch=new L(t,{location:e,threshold:n,distance:s,includeMatches:i,findAllMatches:r,minMatchCharLength:u,isCaseSensitive:c,ignoreDiacritics:o,ignoreLocation:h})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(t){return this._bitapSearch.searchIn(t)}}class S extends k{constructor(t){super(t)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(t){let e,n=0;const s=[],i=this.pattern.length;for(;(e=t.indexOf(this.pattern,n))>-1;)n=e+i,s.push([e,n-1]);const r=!!s.length;return{isMatch:r,score:r?0:1,indices:s}}}const I=[class extends k{constructor(t){super(t)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(t){const e=t===this.pattern;return{isMatch:e,score:e?0:1,indices:[0,this.pattern.length-1]}}},S,class extends k{constructor(t){super(t)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(t){const e=t.startsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,this.pattern.length-1]}}},class extends k{constructor(t){super(t)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(t){const e=!t.startsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,t.length-1]}}},class extends k{constructor(t){super(t)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(t){const e=!t.endsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,t.length-1]}}},class extends k{constructor(t){super(t)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(t){const e=t.endsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[t.length-this.pattern.length,t.length-1]}}},class extends k{constructor(t){super(t)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(t){const e=-1===t.indexOf(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,t.length-1]}}},v],w=I.length,$=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/;const b=new Set([v.type,S.type]);class N{constructor(t,{isCaseSensitive:e=C.isCaseSensitive,ignoreDiacritics:n=C.ignoreDiacritics,includeMatches:s=C.includeMatches,minMatchCharLength:i=C.minMatchCharLength,ignoreLocation:r=C.ignoreLocation,findAllMatches:u=C.findAllMatches,location:c=C.location,threshold:o=C.threshold,distance:h=C.distance}={}){this.query=null,this.options={isCaseSensitive:e,ignoreDiacritics:n,includeMatches:s,minMatchCharLength:i,findAllMatches:u,ignoreLocation:r,location:c,threshold:o,distance:h},t=e?t:t.toLowerCase(),t=n?y(t):t,this.pattern=t,this.query=function(t,e={}){return t.split("|").map((t=>{let n=t.trim().split($).filter((t=>t&&!!t.trim())),s=[];for(let t=0,i=n.length;t<i;t+=1){const i=n[t];let r=!1,u=-1;for(;!r&&++u<w;){const t=I[u];let n=t.isMultiMatch(i);n&&(s.push(new t(n,e)),r=!0)}if(!r)for(u=-1;++u<w;){const t=I[u];let n=t.isSingleMatch(i);if(n){s.push(new t(n,e));break}}}return s}))}(this.pattern,this.options)}static condition(t,e){return e.useExtendedSearch}searchIn(t){const e=this.query;if(!e)return{isMatch:!1,score:1};const{includeMatches:n,isCaseSensitive:s,ignoreDiacritics:i}=this.options;t=s?t:t.toLowerCase(),t=i?y(t):t;let r=0,u=[],c=0;for(let s=0,i=e.length;s<i;s+=1){const i=e[s];u.length=0,r=0;for(let e=0,s=i.length;e<s;e+=1){const s=i[e],{isMatch:o,indices:h,score:a}=s.search(t);if(!o){c=0,r=0,u.length=0;break}if(r+=1,c+=a,n){const t=s.constructor.type;b.has(t)?u=[...u,...h]:u.push(h)}}if(r){let t={isMatch:!0,score:c/r};return n&&(t.indices=u),t}}return{isMatch:!1,score:1}}}const R=[];function O(t,e){for(let n=0,s=R.length;n<s;n+=1){let s=R[n];if(s.condition(t,e))return new s(t,e)}return new L(t,e)}const j="$and",W="$or",z="$path",K="$val",P=t=>!(!t[j]&&!t[W]),q=t=>({[j]:Object.keys(t).map((e=>({[e]:t[e]})))});function J(e,n,{auto:i=!0}={}){const r=e=>{let c=Object.keys(e);const o=(t=>!!t[z])(e);if(!o&&c.length>1&&!P(e))return r(q(e));if((e=>!t(e)&&u(e)&&!P(e))(e)){const t=o?e[z]:c[0],r=o?e[K]:e[t];if(!s(r))throw new Error((t=>`Invalid value for key ${t}`)(t));const u={keyId:p(t),pattern:r};return i&&(u.searcher=O(r,n)),u}let h={children:[],operator:c[0]};return c.forEach((n=>{const s=e[n];t(s)&&s.forEach((t=>{h.children.push(r(t))}))})),h};return P(e)||(e=q(e)),r(e)}function V(t,e){const n=t.matches;e.matches=[],c(n)&&n.forEach((t=>{if(!c(t.indices)||!t.indices.length)return;const{indices:n,value:s}=t;let i={indices:n,value:s};t.key&&(i.key=t.key.src),t.idx>-1&&(i.refIndex=t.idx),e.matches.push(i)}))}function U(t,e){e.score=t.score}class G{constructor(t,e={},n){this.options={...C,...e},this.options.useExtendedSearch,this._keyStore=new g(this.options.keys),this.setCollection(t,n)}setCollection(t,e){if(this._docs=t,e&&!(e instanceof F))throw new Error("Incorrect 'index' type");this._myIndex=e||M(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(t){c(t)&&(this._docs.push(t),this._myIndex.add(t))}remove(t=(()=>!1)){const e=[];for(let n=0,s=this._docs.length;n<s;n+=1){const i=this._docs[n];t(i,n)&&(this.removeAt(n),n-=1,s-=1,e.push(i))}return e}removeAt(t){this._docs.splice(t,1),this._myIndex.removeAt(t)}getIndex(){return this._myIndex}search(t,{limit:e=-1}={}){const{includeMatches:n,includeScore:r,shouldSort:u,sortFn:c,ignoreFieldNorm:o}=this.options;let h=s(t)?s(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return function(t,{ignoreFieldNorm:e=C.ignoreFieldNorm}){t.forEach((t=>{let n=1;t.matches.forEach((({key:t,norm:s,score:i})=>{const r=t?t.weight:null;n*=Math.pow(0===i&&r?Number.EPSILON:i,(r||1)*(e?1:s))})),t.score=n}))}(h,{ignoreFieldNorm:o}),u&&h.sort(c),i(e)&&e>-1&&(h=h.slice(0,e)),function(t,e,{includeMatches:n=C.includeMatches,includeScore:s=C.includeScore}={}){const i=[];return n&&i.push(V),s&&i.push(U),t.map((t=>{const{idx:n}=t,s={item:e[n],refIndex:n};return i.length&&i.forEach((e=>{e(t,s)})),s}))}(h,this._docs,{includeMatches:n,includeScore:r})}_searchStringList(t){const e=O(t,this.options),{records:n}=this._myIndex,s=[];return n.forEach((({v:t,i:n,n:i})=>{if(!c(t))return;const{isMatch:r,score:u,indices:o}=e.searchIn(t);r&&s.push({item:t,idx:n,matches:[{score:u,value:t,norm:i,indices:o}]})})),s}_searchLogical(t){const e=J(t,this.options),n=(t,e,s)=>{if(!t.children){const{keyId:n,searcher:i}=t,r=this._findMatches({key:this._keyStore.get(n),value:this._myIndex.getValueForItemAtKeyId(e,n),searcher:i});return r&&r.length?[{idx:s,item:e,matches:r}]:[]}const i=[];for(let r=0,u=t.children.length;r<u;r+=1){const u=t.children[r],c=n(u,e,s);if(c.length)i.push(...c);else if(t.operator===j)return[]}return i},s=this._myIndex.records,i={},r=[];return s.forEach((({$:t,i:s})=>{if(c(t)){let u=n(e,t,s);u.length&&(i[s]||(i[s]={idx:s,item:t,matches:[]},r.push(i[s])),u.forEach((({matches:t})=>{i[s].matches.push(...t)})))}})),r}_searchObjectList(t){const e=O(t,this.options),{keys:n,records:s}=this._myIndex,i=[];return s.forEach((({$:t,i:s})=>{if(!c(t))return;let r=[];n.forEach(((n,s)=>{r.push(...this._findMatches({key:n,value:t[s],searcher:e}))})),r.length&&i.push({idx:s,item:t,matches:r})})),i}_findMatches({key:e,value:n,searcher:s}){if(!c(n))return[];let i=[];if(t(n))n.forEach((({v:t,i:n,n:r})=>{if(!c(t))return;const{isMatch:u,score:o,indices:h}=s.searchIn(t);u&&i.push({score:o,key:e,value:t,idx:n,norm:r,indices:h})}));else{const{v:t,n:r}=n,{isMatch:u,score:c,indices:o}=s.searchIn(t);u&&i.push({score:c,key:e,value:t,norm:r,indices:o})}return i}}G.version="7.1.0",G.createIndex=M,G.parseIndex=function(t,{getFn:e=C.getFn,fieldNormWeight:n=C.fieldNormWeight}={}){const{keys:s,records:i}=t,r=new F({getFn:e,fieldNormWeight:n});return r.setKeys(s),r.setIndexRecords(i),r},G.config=C,function(...t){R.push(...t)}(N);export{G as default};