!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).TimeAgo=e()}(this,(function(){"use strict";var t="en",e={},r={};function n(){return t}function o(t){return e[t]?t:r[t.toLowerCase()]?r[t.toLowerCase()]:void 0}function a(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.localeMatcher||"lookup";switch(r){case"lookup":case"best fit":return i(t);default:throw new RangeError('Invalid "localeMatcher" option: '.concat(r))}}function i(t){var e=o(t);if(e)return e;for(var r=t.split("-");t.length>1;){r.pop();var n=o(t=r.join("-"));if(n)return n}}var u={af:function(t){return 1==t?"one":"other"},am:function(t){return t>=0&&t<=1?"one":"other"},ar:function(t){var e=String(t).split("."),r=Number(e[0])==t&&e[0].slice(-2);return 0==t?"zero":1==t?"one":2==t?"two":r>=3&&r<=10?"few":r>=11&&r<=99?"many":"other"},ast:function(t){var e=!String(t).split(".")[1];return 1==t&&e?"one":"other"},be:function(t){var e=String(t).split("."),r=Number(e[0])==t,n=r&&e[0].slice(-1),o=r&&e[0].slice(-2);return 1==n&&11!=o?"one":n>=2&&n<=4&&(o<12||o>14)?"few":r&&0==n||n>=5&&n<=9||o>=11&&o<=14?"many":"other"},br:function(t){var e=String(t).split("."),r=Number(e[0])==t,n=r&&e[0].slice(-1),o=r&&e[0].slice(-2),a=r&&e[0].slice(-6);return 1==n&&11!=o&&71!=o&&91!=o?"one":2==n&&12!=o&&72!=o&&92!=o?"two":(3==n||4==n||9==n)&&(o<10||o>19)&&(o<70||o>79)&&(o<90||o>99)?"few":0!=t&&r&&0==a?"many":"other"},bs:function(t){var e=String(t).split("."),r=e[0],n=e[1]||"",o=!e[1],a=r.slice(-1),i=r.slice(-2),u=n.slice(-1),c=n.slice(-2);return o&&1==a&&11!=i||1==u&&11!=c?"one":o&&a>=2&&a<=4&&(i<12||i>14)||u>=2&&u<=4&&(c<12||c>14)?"few":"other"},ca:function(t){var e=String(t).split("."),r=e[0],n=!e[1],o=r.slice(-6);return 1==t&&n?"one":0!=r&&0==o&&n?"many":"other"},ceb:function(t){var e=String(t).split("."),r=e[0],n=e[1]||"",o=!e[1],a=r.slice(-1),i=n.slice(-1);return o&&(1==r||2==r||3==r)||o&&4!=a&&6!=a&&9!=a||!o&&4!=i&&6!=i&&9!=i?"one":"other"},cs:function(t){var e=String(t).split("."),r=e[0],n=!e[1];return 1==t&&n?"one":r>=2&&r<=4&&n?"few":n?"other":"many"},cy:function(t){return 0==t?"zero":1==t?"one":2==t?"two":3==t?"few":6==t?"many":"other"},da:function(t){var e=String(t).split("."),r=e[0],n=Number(e[0])==t;return 1!=t&&(n||0!=r&&1!=r)?"other":"one"},dsb:function(t){var e=String(t).split("."),r=e[0],n=e[1]||"",o=!e[1],a=r.slice(-2),i=n.slice(-2);return o&&1==a||1==i?"one":o&&2==a||2==i?"two":o&&(3==a||4==a)||3==i||4==i?"few":"other"},dz:function(t){return"other"},es:function(t){var e=String(t).split("."),r=e[0],n=!e[1],o=r.slice(-6);return 1==t?"one":0!=r&&0==o&&n?"many":"other"},ff:function(t){return t>=0&&t<2?"one":"other"},fr:function(t){var e=String(t).split("."),r=e[0],n=!e[1],o=r.slice(-6);return t>=0&&t<2?"one":0!=r&&0==o&&n?"many":"other"},ga:function(t){var e=String(t).split("."),r=Number(e[0])==t;return 1==t?"one":2==t?"two":r&&t>=3&&t<=6?"few":r&&t>=7&&t<=10?"many":"other"},gd:function(t){var e=String(t).split("."),r=Number(e[0])==t;return 1==t||11==t?"one":2==t||12==t?"two":r&&t>=3&&t<=10||r&&t>=13&&t<=19?"few":"other"},he:function(t){var e=String(t).split("."),r=e[0],n=!e[1];return 1==r&&n||0==r&&!n?"one":2==r&&n?"two":"other"},is:function(t){var e=String(t).split("."),r=e[0],n=(e[1]||"").replace(/0+$/,""),o=Number(e[0])==t,a=r.slice(-1),i=r.slice(-2);return o&&1==a&&11!=i||n%10==1&&n%100!=11?"one":"other"},ksh:function(t){return 0==t?"zero":1==t?"one":"other"},lt:function(t){var e=String(t).split("."),r=e[1]||"",n=Number(e[0])==t,o=n&&e[0].slice(-1),a=n&&e[0].slice(-2);return 1==o&&(a<11||a>19)?"one":o>=2&&o<=9&&(a<11||a>19)?"few":0!=r?"many":"other"},lv:function(t){var e=String(t).split("."),r=e[1]||"",n=r.length,o=Number(e[0])==t,a=o&&e[0].slice(-1),i=o&&e[0].slice(-2),u=r.slice(-2),c=r.slice(-1);return o&&0==a||i>=11&&i<=19||2==n&&u>=11&&u<=19?"zero":1==a&&11!=i||2==n&&1==c&&11!=u||2!=n&&1==c?"one":"other"},mk:function(t){var e=String(t).split("."),r=e[0],n=e[1]||"",o=!e[1],a=r.slice(-1),i=r.slice(-2),u=n.slice(-1),c=n.slice(-2);return o&&1==a&&11!=i||1==u&&11!=c?"one":"other"},mt:function(t){var e=String(t).split("."),r=Number(e[0])==t&&e[0].slice(-2);return 1==t?"one":2==t?"two":0==t||r>=3&&r<=10?"few":r>=11&&r<=19?"many":"other"},pa:function(t){return 0==t||1==t?"one":"other"},pl:function(t){var e=String(t).split("."),r=e[0],n=!e[1],o=r.slice(-1),a=r.slice(-2);return 1==t&&n?"one":n&&o>=2&&o<=4&&(a<12||a>14)?"few":n&&1!=r&&(0==o||1==o)||n&&o>=5&&o<=9||n&&a>=12&&a<=14?"many":"other"},pt:function(t){var e=String(t).split("."),r=e[0],n=!e[1],o=r.slice(-6);return 0==r||1==r?"one":0!=r&&0==o&&n?"many":"other"},ro:function(t){var e=String(t).split("."),r=!e[1],n=Number(e[0])==t&&e[0].slice(-2);return 1==t&&r?"one":!r||0==t||1!=t&&n>=1&&n<=19?"few":"other"},ru:function(t){var e=String(t).split("."),r=e[0],n=!e[1],o=r.slice(-1),a=r.slice(-2);return n&&1==o&&11!=a?"one":n&&o>=2&&o<=4&&(a<12||a>14)?"few":n&&0==o||n&&o>=5&&o<=9||n&&a>=11&&a<=14?"many":"other"},se:function(t){return 1==t?"one":2==t?"two":"other"},si:function(t){var e=String(t).split("."),r=e[0],n=e[1]||"";return 0==t||1==t||0==r&&1==n?"one":"other"},sl:function(t){var e=String(t).split("."),r=e[0],n=!e[1],o=r.slice(-2);return n&&1==o?"one":n&&2==o?"two":n&&(3==o||4==o)||!n?"few":"other"}};u.as=u.am,u.az=u.af,u.bg=u.af,u.bn=u.am,u.brx=u.af,u.ce=u.af,u.chr=u.af,u.de=u.ast,u.ee=u.af,u.el=u.af,u.en=u.ast,u.et=u.ast,u.eu=u.af,u.fa=u.am,u.fi=u.ast,u.fil=u.ceb,u.fo=u.af,u.fur=u.af,u.fy=u.ast,u.gl=u.ast,u.gu=u.am,u.ha=u.af,u.hi=u.am,u.hr=u.bs,u.hsb=u.dsb,u.hu=u.af,u.hy=u.ff,u.ia=u.ast,u.id=u.dz,u.ig=u.dz,u.it=u.ca,u.ja=u.dz,u.jgo=u.af,u.jv=u.dz,u.ka=u.af,u.kea=u.dz,u.kk=u.af,u.kl=u.af,u.km=u.dz,u.kn=u.am,u.ko=u.dz,u.ks=u.af,u.ku=u.af,u.ky=u.af,u.lb=u.af,u.lkt=u.dz,u.lo=u.dz,u.ml=u.af,u.mn=u.af,u.mr=u.af,u.ms=u.dz,u.my=u.dz,u.nb=u.af,u.ne=u.af,u.nl=u.ast,u.nn=u.af,u.no=u.af,u.or=u.af,u.pcm=u.am,u.ps=u.af,u.rm=u.af,u.sah=u.dz,u.sc=u.ast,u.sd=u.af,u.sk=u.cs,u.so=u.af,u.sq=u.af,u.sr=u.bs,u.su=u.dz,u.sv=u.ast,u.sw=u.ast,u.ta=u.af,u.te=u.af,u.th=u.dz,u.ti=u.pa,u.tk=u.af,u.to=u.dz,u.tr=u.af,u.ug=u.af,u.uk=u.ru,u.ur=u.ast,u.uz=u.af,u.vi=u.dz,u.wae=u.af,u.wo=u.dz,u.xh=u.af,u.yi=u.ast,u.yo=u.dz,u.yue=u.dz,u.zh=u.dz,u.zu=u.am;var c=u;function l(t){return"pt-PT"===t?t:function(t){var e=t.match(f);if(!e)throw new TypeError("Invalid locale: ".concat(t));return e[1]}(t)}var f=/^([a-z0-9]+)/i;function s(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var p=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);var n=t.supportedLocalesOf(e);if(0===n.length)throw new RangeError("Unsupported locale: "+e);if(r&&"cardinal"!==r.type)throw new RangeError('Only "cardinal" "type" is supported');this.$=c[l(n[0])]}var e,r,n;return e=t,n=[{key:"supportedLocalesOf",value:function(t){return"string"==typeof t&&(t=[t]),t.filter((function(t){return c[l(t)]}))}}],(r=[{key:"select",value:function(t){return this.$(t)}}])&&s(e.prototype,r),n&&s(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();function y(t){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(t)}function m(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?m(Object(r),!0).forEach((function(e){h(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function h(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,u=!1;try{for(r=r.call(t);!(i=(n=r.next()).done)&&(a.push(n.value),!e||a.length!==e);i=!0);}catch(t){u=!0,o=t}finally{try{i||null==r.return||r.return()}finally{if(u)throw o}}return a}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return v(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return v(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function w(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var O=["second","minute","hour","day","week","month","quarter","year"],j=["auto","always"],S=["long","short","narrow"],P=["lookup","best fit"],A=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};g(this,t);var o=r.numeric,i=r.style,u=r.localeMatcher;if(this.numeric="always",this.style="long",this.localeMatcher="lookup",void 0!==o){if(j.indexOf(o)<0)throw new RangeError('Invalid "numeric" option: '.concat(o));this.numeric=o}if(void 0!==i){if(S.indexOf(i)<0)throw new RangeError('Invalid "style" option: '.concat(i));this.style=i}if(void 0!==u){if(P.indexOf(u)<0)throw new RangeError('Invalid "localeMatcher" option: '.concat(u));this.localeMatcher=u}if("string"==typeof e&&(e=[e]),e.push(n()),this.locale=t.supportedLocalesOf(e,{localeMatcher:this.localeMatcher})[0],!this.locale)throw new Error("No supported locale was found");p.supportedLocalesOf(this.locale).length>0?this.pluralRules=new p(this.locale):console.warn('"'.concat(this.locale,'" locale is not supported')),"undefined"!=typeof Intl&&Intl.NumberFormat?(this.numberFormat=new Intl.NumberFormat(this.locale),this.numberingSystem=this.numberFormat.resolvedOptions().numberingSystem):this.numberingSystem="latn",this.locale=a(this.locale,{localeMatcher:this.localeMatcher})}var r,o,i;return r=t,o=[{key:"format",value:function(){var t=E(arguments),e=d(t,2),r=e[0],n=e[1];return this.getRule(r,n).replace("{0}",this.formatNumber(Math.abs(r)))}},{key:"formatToParts",value:function(){var t=E(arguments),e=d(t,2),r=e[0],n=e[1],o=this.getRule(r,n),a=o.indexOf("{0}");if(a<0)return[{type:"literal",value:o}];var i=[];return a>0&&i.push({type:"literal",value:o.slice(0,a)}),i=i.concat(this.formatNumberToParts(Math.abs(r)).map((function(t){return b(b({},t),{},{unit:n})}))),a+"{0}".length<o.length-1&&i.push({type:"literal",value:o.slice(a+"{0}".length)}),i}},{key:"getRule",value:function(t,r){var n,o=(n=this.locale,e[n])[this.style][r];if("string"==typeof o)return o;if("auto"===this.numeric)if(-2===t||-1===t){var a=o["previous".concat(-1===t?"":"-"+Math.abs(t))];if(a)return a}else if(1===t||2===t){var i=o["next".concat(1===t?"":"-"+Math.abs(t))];if(i)return i}else if(0===t&&o.current)return o.current;var u,c=o[(u=t,u<0||0===u&&function(t){return 1/t==-1/0}(u)?"past":"future")];return"string"==typeof c?c:c[this.pluralRules&&this.pluralRules.select(Math.abs(t))||"other"]||c.other}},{key:"formatNumber",value:function(t){return this.numberFormat?this.numberFormat.format(t):String(t)}},{key:"formatNumberToParts",value:function(t){return this.numberFormat&&this.numberFormat.formatToParts?this.numberFormat.formatToParts(t):[{type:"integer",value:this.formatNumber(t)}]}},{key:"resolvedOptions",value:function(){return{locale:this.locale,style:this.style,numeric:this.numeric,numberingSystem:this.numberingSystem}}}],o&&w(r.prototype,o),i&&w(r,i),Object.defineProperty(r,"prototype",{writable:!1}),t}();A.supportedLocalesOf=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("string"==typeof t)t=[t];else if(!Array.isArray(t))throw new TypeError('Invalid "locales" argument');return t.filter((function(t){return a(t,e)}))},A.addLocale=function(t){if(!t)throw new Error("No locale data passed");e[t.locale]=t,r[t.locale.toLowerCase()]=t.locale},A.setDefaultLocale=function(e){t=e},A.getDefaultLocale=n,A.PluralRules=p;var D='Invalid "unit" argument';function k(t){if("symbol"===y(t))throw new TypeError(D);if("string"!=typeof t)throw new RangeError("".concat(D,": ").concat(t));if("s"===t[t.length-1]&&(t=t.slice(0,t.length-1)),O.indexOf(t)<0)throw new RangeError("".concat(D,": ").concat(t));return t}function T(t){if(t=Number(t),Number.isFinite&&!Number.isFinite(t))throw new RangeError("".concat('Invalid "number" argument',": ").concat(t));return t}function E(t){if(t.length<2)throw new TypeError('"unit" argument is required');return[T(t[0]),k(t[1])]}function F(t){return F="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},F(t)}function I(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var N=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.cache={}}var e,r,n;return e=t,r=[{key:"get",value:function(){for(var t=this.cache,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];for(var o=0,a=r;o<a.length;o++){var i=a[o];if("object"!==F(t))return;t=t[i]}return t}},{key:"put",value:function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];for(var n=e.pop(),o=e.pop(),a=this.cache,i=0,u=e;i<u.length;i++){var c=u[i];"object"!==F(a[c])&&(a[c]={}),a=a[c]}return a[o]=n}}],r&&I(e.prototype,r),n&&I(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();function M(t){return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M(t)}function R(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,e){if(!t)return;if("string"==typeof t)return z(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return z(t,e)}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function z(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function x(t,e){for(var r,n=R(t);!(r=n()).done;){var o=r.value;if(e(o))return o;for(var a=o.split("-");a.length>1;)if(a.pop(),e(o=a.join("-")))return o}throw new Error("No locale data has been registered for any of the locales: ".concat(t.join(", ")))}function L(t){return L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},L(t)}function C(t){return function(t){return void 0!==L(t)&&null!==t&&t.constructor===U}(t)&&(Array.isArray(t.steps)||Array.isArray(t.gradation)||Array.isArray(t.flavour)||"string"==typeof t.flavour||Array.isArray(t.labels)||"string"==typeof t.labels||Array.isArray(t.units)||"function"==typeof t.custom)}var U={}.constructor;var $=60,_=3600,Y=86400,q=7*Y,V=2630016,J=31556952;function B(t){switch(t){case"second":return 1;case"minute":return $;case"hour":return _;case"day":return Y;case"week":return q;case"month":return V;case"year":return J}}function G(t){return void 0!==t.factor?t.factor:B(t.unit||t.formatAs)||1}function H(t){return"floor"===t?Math.floor:Math.round}function K(t){return"floor"===t?1:.5}function Q(t){return Q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Q(t)}function W(t,e){var r,n=e.prevStep,o=e.timestamp,a=e.now,i=e.future,u=e.round;return n&&(n.id||n.unit)&&(r=t["threshold_for_".concat(n.id||n.unit)]),void 0===r&&void 0!==t.threshold&&"function"==typeof(r=t.threshold)&&(r=r(a,i)),void 0===r&&(r=t.minTime),"object"===Q(r)&&(r=n&&n.id&&void 0!==r[n.id]?r[n.id]:r.default),"function"==typeof r&&(r=r(o,{future:i,getMinTimeForUnit:function(t,e){return X(t,e||n&&n.formatAs,{round:u})}})),void 0===r&&t.test&&(r=t.test(o,{now:a,future:i})?0:9007199254740991),void 0===r&&(n?t.formatAs&&n.formatAs&&(r=X(t.formatAs,n.formatAs,{round:u})):r=0),void 0===r&&console.warn("[javascript-time-ago] A step should specify `minTime`:\n"+JSON.stringify(t,null,2)),r}function X(t,e,r){var n,o=r.round,a=B(t);if(n=B("now"===e?t:e),void 0!==a&&void 0!==n)return a-n*(1-K(o))}function Z(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function tt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Z(Object(r),!0).forEach((function(e){et(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Z(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function et(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rt(t,e,r){var n=r.now,o=r.future,a=r.round,i=r.units,u=r.getNextStep;t=function(t,e){return t.filter((function(t){var r=t.unit,n=t.formatAs;return!(r=r||n)||e.indexOf(r)>=0}))}(t,i);var c=function(t,e,r){var n=r.now,o=r.future,a=r.round;if(0===t.length)return;var i=nt(t,e,{now:n,future:o||e<0,round:a});if(-1===i)return;var u=t[i];if(u.granularity){if(0===H(a)(Math.abs(e)/G(u)/u.granularity)*u.granularity&&i>0)return t[i-1]}return u}(t,e,{now:n,future:o,round:a});return u?c?[t[t.indexOf(c)-1],c,t[t.indexOf(c)+1]]:[void 0,void 0,t[0]]:c}function nt(t,e,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=W(t[n],tt({prevStep:t[n-1],timestamp:r.now-1e3*e},r));return void 0===o||Math.abs(e)<o?n-1:n===t.length-1?n:nt(t,e,r,n+1)}function ot(t,e,r){var n=r.now,o=r.round;if(B(t)){var a=1e3*B(t),i=e>n,u=Math.abs(e-n),c=H(o)(u/a)*a;return i?c>0?u-c+function(t,e){return(1-K(t))*e+1}(o,a):u-c+1:-(u-c)+function(t,e){return K(t)*e}(o,a)}}var at=31536e9;function it(t,e,r){var n=r.prevStep,o=r.nextStep,a=r.now,i=r.future,u=r.round,c=t.getTime?t.getTime():t,l=function(t){return ot(t,c,{now:a,round:u})},f=function(t,e,r){var n=r.now,o=r.future,a=r.round,i=r.prevStep;if(t){var u=function(t,e,r){var n=r.now,o=r.future,a=r.round,i=r.prevStep,u=W(t,{timestamp:e,now:n,future:o,round:a,prevStep:i});if(void 0===u)return;return o?e-1e3*u+1:0===u&&e===n?at:e+1e3*u}(t,e,{now:n,future:o,round:a,prevStep:i});if(void 0===u)return;return u-n}return o?e-n+1:at}(i?e:o,c,{future:i,now:a,round:u,prevStep:i?n:e});if(void 0!==f){var s;if(e&&(e.getTimeToNextUpdate&&(s=e.getTimeToNextUpdate(c,{getTimeToNextUpdateForUnit:l,getRoundFunction:H,now:a,future:i,round:u})),void 0===s)){var p=e.unit||e.formatAs;p&&(s=l(p))}return void 0===s?f:Math.min(s,f)}}var ut={};function ct(t){return ut[t]}function lt(t){if(!t)throw new Error("[javascript-time-ago] No locale data passed.");ut[t.locale]=t}var ft={steps:[{formatAs:"now"},{formatAs:"second"},{formatAs:"minute"},{formatAs:"hour"},{formatAs:"day"},{formatAs:"week"},{formatAs:"month"},{formatAs:"year"}],labels:"long"};function st(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function pt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?st(Object(r),!0).forEach((function(e){yt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):st(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function yt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var mt=pt(pt({},ft),{},{steps:ft.steps.filter((function(t){return"second"!==t.formatAs}))}),bt=[{factor:1,unit:"now"},{threshold:1,threshold_for_now:45.5,factor:1,unit:"second"},{threshold:45.5,factor:$,unit:"minute"},{threshold:150,granularity:5,factor:$,unit:"minute"},{threshold:1350,factor:1800,unit:"half-hour"},{threshold:2550,threshold_for_minute:3150,factor:_,unit:"hour"},{threshold:73800,factor:Y,unit:"day"},{threshold:475200,factor:q,unit:"week"},{threshold:2116800,factor:V,unit:"month"},{threshold:27615168,factor:J,unit:"year"}],ht={gradation:bt,flavour:"long",units:["now","minute","hour","day","week","month","year"]},dt={gradation:bt,flavour:"long-time",units:["now","minute","hour","day","week","month","year"]};function vt(t){return t instanceof Date?t:new Date(t)}var gt=[{formatAs:"second"},{formatAs:"minute"},{formatAs:"hour"}],wt={},Ot={minTime:function(t,e){return e.future,(0,e.getMinTimeForUnit)("day")},format:function(t,e){return wt[e]||(wt[e]={}),wt[e].dayMonth||(wt[e].dayMonth=new Intl.DateTimeFormat(e,{month:"short",day:"numeric"})),wt[e].dayMonth.format(vt(t))}},jt={minTime:function(t,e){return e.future?(t-(new Date(new Date(t).getFullYear(),0).getTime()-1))/1e3:(new Date(new Date(t).getFullYear()+1,0).getTime()-t)/1e3},format:function(t,e){return wt[e]||(wt[e]={}),wt[e].dayMonthYear||(wt[e].dayMonthYear=new Intl.DateTimeFormat(e,{year:"numeric",month:"short",day:"numeric"})),wt[e].dayMonthYear.format(vt(t))}};"object"===("undefined"==typeof Intl?"undefined":M(Intl))&&"function"==typeof Intl.DateTimeFormat?gt.push(Ot,jt):gt.push({formatAs:"day"},{formatAs:"week"},{formatAs:"month"},{formatAs:"year"});var St={steps:gt,labels:["mini","short-time","narrow","short"]};function Pt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function At(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Pt(Object(r),!0).forEach((function(e){Dt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Pt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Dt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var kt=At(At({},St),{},{steps:[{formatAs:"now"}].concat(St.steps)});function Tt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Et(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Tt(Object(r),!0).forEach((function(e){Ft(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Tt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ft(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var It=Et(Et({},St),{},{steps:St.steps.filter((function(t){return"second"!==t.formatAs}))});function Nt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Mt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Nt(Object(r),!0).forEach((function(e){Rt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Nt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Rt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var zt=Mt(Mt({},It),{},{steps:[{formatAs:"now"}].concat(It.steps)});function xt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Lt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?xt(Object(r),!0).forEach((function(e){Ct(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ct(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ut=Lt(Lt({},St),{},{steps:St.steps.filter((function(t){return"second"!==t.formatAs})).map((function(t){return"minute"===t.formatAs?Lt(Lt({},t),{},{minTime:$}):t}))}),$t={steps:[{formatAs:"second"},{formatAs:"minute"},{formatAs:"hour"},{formatAs:"day"},{formatAs:"month"},{formatAs:"year"}],labels:["mini","short-time","narrow","short"]};function _t(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Yt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_t(Object(r),!0).forEach((function(e){qt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_t(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function qt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Vt=Yt(Yt({},$t),{},{steps:[{formatAs:"now"}].concat($t.steps)});function Jt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Bt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Jt(Object(r),!0).forEach((function(e){Gt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Jt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Gt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ht=Bt(Bt({},$t),{},{steps:$t.steps.filter((function(t){return"second"!==t.formatAs}))});function Kt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Qt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Kt(Object(r),!0).forEach((function(e){Wt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Kt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Wt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Xt=Qt(Qt({},Ht),{},{steps:[{formatAs:"now"}].concat(Ht.steps)});function Zt(t){switch(t){case"default":case"round":return ft;case"round-minute":return mt;case"approximate":default:return ht;case"time":case"approximate-time":return dt;case"mini":return $t;case"mini-now":return Vt;case"mini-minute":return Ht;case"mini-minute-now":return Xt;case"twitter":return St;case"twitter-now":return kt;case"twitter-minute":return It;case"twitter-minute-now":return zt;case"twitter-first-minute":return Ut}}function te(t){return te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},te(t)}function ee(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=ne(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function re(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,u=!1;try{for(r=r.call(t);!(i=(n=r.next()).done)&&(a.push(n.value),!e||a.length!==e);i=!0);}catch(t){u=!0,o=t}finally{try{i||null==r.return||r.return()}finally{if(u)throw o}}return a}(t,e)||ne(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ne(t,e){if(t){if("string"==typeof t)return oe(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?oe(t,e):void 0}}function oe(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ae(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ie(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var ue,ce=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.polyfill;ae(this,t),"string"==typeof e&&(e=[e]),this.locale=x(e.concat(t.getDefaultLocale()),ct),"undefined"!=typeof Intl&&Intl.NumberFormat&&(this.numberFormat=new Intl.NumberFormat(this.locale)),!1===n?(this.IntlRelativeTimeFormat=Intl.RelativeTimeFormat,this.IntlPluralRules=Intl.PluralRules):(this.IntlRelativeTimeFormat=A,this.IntlPluralRules=A.PluralRules),this.relativeTimeFormatCache=new N,this.pluralRulesCache=new N}var e,r,n;return e=t,r=[{key:"format",value:function(t,e,r){var n;r||(e&&"string"!=typeof(n=e)&&!C(n)?(r=e,e=void 0):r={}),e||(e=mt),"string"==typeof e&&(e=Zt(e));var o,a=function(t){if(t.constructor===Date||"object"===te(e=t)&&"function"==typeof e.getTime)return t.getTime();var e;if("number"==typeof t)return t;throw new Error("Unsupported relative time formatter input: ".concat(te(t),", ").concat(t))}(t),i=this.getLabels(e.flavour||e.labels),u=i.labels,c=i.labelsType;void 0!==e.now&&(o=e.now),void 0===o&&void 0!==r.now&&(o=r.now),void 0===o&&(o=Date.now());var l=(o-a)/1e3,f=r.future||l<0,s=function(t,e,r,n){var o=t.now||e&&e.now;return o?"string"==typeof o?o:n?o.future:o.past:r&&r.second&&r.second.current?r.second.current:void 0}(u,ct(this.locale).now,ct(this.locale).long,f);if(e.custom){var p=e.custom({now:o,date:new Date(a),time:a,elapsed:l,locale:this.locale});if(void 0!==p)return p}var y=function(t,e,r){var n=Object.keys(e);return r&&n.push("now"),t&&(n=t.filter((function(t){return"now"===t||n.indexOf(t)>=0}))),n}(e.units,u,s),m=r.round||e.round,b=re(rt(e.gradation||e.steps||mt.steps,l,{now:o,units:y,round:m,future:f,getNextStep:!0}),3),h=b[0],d=b[1],v=b[2],g=this.formatDateForStep(a,d,l,{labels:u,labelsType:c,nowLabel:s,now:o,future:f,round:m})||"";return r.getTimeToNextUpdate?[g,it(a,d,{nextStep:v,prevStep:h,now:o,future:f,round:m})]:g}},{key:"formatDateForStep",value:function(t,e,r,n){var o=this,a=n.labels,i=n.labelsType,u=n.nowLabel,c=n.now,l=n.future,f=n.round;if(e){if(e.format)return e.format(t,this.locale,{formatAs:function(t,e){return o.formatValue(e,t,{labels:a,future:l})},now:c,future:l});var s=e.unit||e.formatAs;if(!s)throw new Error("[javascript-time-ago] Each step must define either `formatAs` or `format()`. Step: ".concat(JSON.stringify(e)));if("now"===s)return u;var p=Math.abs(r)/G(e);e.granularity&&(p=H(f)(p/e.granularity)*e.granularity);var y=-1*Math.sign(r)*H(f)(p);switch(0===y&&(y=l?0:-0),i){case"long":case"short":case"narrow":return this.getFormatter(i).format(y,s);default:return this.formatValue(y,s,{labels:a,future:l})}}}},{key:"formatValue",value:function(t,e,r){var n=r.labels,o=r.future;return this.getFormattingRule(n,e,t,{future:o}).replace("{0}",this.formatNumber(Math.abs(t)))}},{key:"getFormattingRule",value:function(t,e,r,n){var o=n.future;if(this.locale,"string"==typeof(t=t[e]))return t;var a=t[0===r?o?"future":"past":r<0?"past":"future"]||t;return"string"==typeof a?a:a[this.getPluralRules().select(Math.abs(r))]||a.other}},{key:"formatNumber",value:function(t){return this.numberFormat?this.numberFormat.format(t):String(t)}},{key:"getFormatter",value:function(t){return this.relativeTimeFormatCache.get(this.locale,t)||this.relativeTimeFormatCache.put(this.locale,t,new this.IntlRelativeTimeFormat(this.locale,{style:t}))}},{key:"getPluralRules",value:function(){return this.pluralRulesCache.get(this.locale)||this.pluralRulesCache.put(this.locale,new this.IntlPluralRules(this.locale))}},{key:"getLabels",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];"string"==typeof t&&(t=[t]),t=(t=t.map((function(t){switch(t){case"tiny":case"mini-time":return"mini";default:return t}}))).concat("long");for(var e,r=ct(this.locale),n=ee(t);!(e=n()).done;){var o=e.value;if(r[o])return{labelsType:o,labels:r[o]}}}}],r&&ie(e.prototype,r),n&&ie(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),le="en";return ce.getDefaultLocale=function(){return le},ce.setDefaultLocale=function(t){return le=t},ce.addDefaultLocale=function(t){if(ue)return console.error("[javascript-time-ago] `TimeAgo.addDefaultLocale()` can only be called once. To add other locales, use `TimeAgo.addLocale()`.");ue=!0,ce.setDefaultLocale(t.locale),ce.addLocale(t)},ce.addLocale=function(t){lt(t),A.addLocale(t)},ce.locale=ce.addLocale,ce.addLabels=function(t,e,r){var n=ct(t);n||(lt({locale:t}),n=ct(t)),n[e]=r},ce}));
//# sourceMappingURL=javascript-time-ago.js.map
