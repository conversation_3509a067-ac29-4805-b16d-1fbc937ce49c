"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = getStyleByName;

var _round = _interopRequireDefault(require("./round.js"));

var _roundMinute = _interopRequireDefault(require("./roundMinute.js"));

var _approximate = _interopRequireDefault(require("./approximate.js"));

var _approximateTime = _interopRequireDefault(require("./approximateTime.js"));

var _twitter = _interopRequireDefault(require("./twitter.js"));

var _twitterNow = _interopRequireDefault(require("./twitterNow.js"));

var _twitterMinute = _interopRequireDefault(require("./twitterMinute.js"));

var _twitterMinuteNow = _interopRequireDefault(require("./twitterMinuteNow.js"));

var _twitterFirstMinute = _interopRequireDefault(require("./twitterFirstMinute.js"));

var _mini = _interopRequireDefault(require("./mini.js"));

var _miniNow = _interopRequireDefault(require("./miniNow.js"));

var _miniMinute = _interopRequireDefault(require("./miniMinute.js"));

var _miniMinuteNow = _interopRequireDefault(require("./miniMinuteNow.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

// `approximate` style is deprecated.
// `approximateTime` style is deprecated.
function getStyleByName(style) {
  switch (style) {
    // "default" style name is deprecated.
    case 'default':
    case 'round':
      return _round["default"];

    case 'round-minute':
      return _roundMinute["default"];

    case 'approximate':
      return _approximate["default"];
    // "time" style name is deprecated.

    case 'time':
    case 'approximate-time':
      return _approximateTime["default"];

    case 'mini':
      return _mini["default"];

    case 'mini-now':
      return _miniNow["default"];

    case 'mini-minute':
      return _miniMinute["default"];

    case 'mini-minute-now':
      return _miniMinuteNow["default"];

    case 'twitter':
      return _twitter["default"];

    case 'twitter-now':
      return _twitterNow["default"];

    case 'twitter-minute':
      return _twitterMinute["default"];

    case 'twitter-minute-now':
      return _twitterMinuteNow["default"];

    case 'twitter-first-minute':
      return _twitterFirstMinute["default"];

    default:
      // For historical reasons, the default style is "approximate".
      return _approximate["default"];
  }
}
//# sourceMappingURL=getStyleByName.js.map