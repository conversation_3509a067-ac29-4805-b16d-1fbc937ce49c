{"version": 3, "file": "RunnableFunction.d.ts", "sourceRoot": "", "sources": ["../src/lib/RunnableFunction.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AACnE,OAAO,EAAE,KAAK,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AACrF,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,KAAK,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAExC,MAAM,MAAM,yBAAyB,CAAC,IAAI,SAAS,MAAM,IAAI;IAC3D;;;;OAIG;IACH,QAAQ,EAAE,CACR,IAAI,EAAE,IAAI,EACV,MAAM,EAAE,oBAAoB,CAAC,OAAO,CAAC,GAAG,6BAA6B,CAAC,OAAO,CAAC,KAC3E,cAAc,CAAC,OAAO,CAAC,CAAC;IAC7B;;;OAGG;IACH,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,cAAc,CAAC,IAAI,CAAC,CAAC;IAC/C;;OAEG;IACH,UAAU,EAAE,UAAU,CAAC;IACvB;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;IACpB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B,MAAM,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;CAC9B,CAAC;AAEF,MAAM,MAAM,4BAA4B,GAAG;IACzC;;;OAGG;IACH,QAAQ,EAAE,CACR,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE,oBAAoB,CAAC,OAAO,CAAC,GAAG,6BAA6B,CAAC,OAAO,CAAC,KAC3E,cAAc,CAAC,OAAO,CAAC,CAAC;IAC7B;;OAEG;IACH,UAAU,EAAE,UAAU,CAAC;IACvB;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;IACpB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B,MAAM,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;CAC9B,CAAC;AAEF,MAAM,MAAM,gBAAgB,CAAC,IAAI,SAAS,MAAM,GAAG,MAAM,IACvD,IAAI,SAAS,MAAM,GAAG,4BAA4B,GAChD,IAAI,SAAS,MAAM,GAAG,yBAAyB,CAAC,IAAI,CAAC,GACrD,KAAK,CAAC;AAEV,MAAM,MAAM,oBAAoB,CAAC,IAAI,SAAS,MAAM,GAAG,MAAM,IAC3D,IAAI,SAAS,MAAM,GAAG,gCAAgC,GACpD,IAAI,SAAS,MAAM,GAAG,6BAA6B,CAAC,IAAI,CAAC,GACzD,KAAK,CAAC;AAEV,MAAM,MAAM,gCAAgC,GAAG;IAC7C,IAAI,EAAE,UAAU,CAAC;IACjB,QAAQ,EAAE,4BAA4B,CAAC;CACxC,CAAC;AACF,MAAM,MAAM,6BAA6B,CAAC,IAAI,SAAS,MAAM,IAAI;IAC/D,IAAI,EAAE,UAAU,CAAC;IACjB,QAAQ,EAAE,yBAAyB,CAAC,IAAI,CAAC,CAAC;CAC3C,CAAC;AAEF,wBAAgB,2BAA2B,CAAC,IAAI,SAAS,MAAM,EAC7D,EAAE,EAAE,GAAG,GACN,EAAE,IAAI,yBAAyB,CAAC,IAAI,CAAC,CAEvC;AAED,MAAM,MAAM,iBAAiB,GAAG,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC;AAE7D,MAAM,MAAM,iBAAiB,CAAC,aAAa,SAAS,iBAAiB,IACnE;IAAC,GAAG,EAAE;CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,SAAS,gBAAgB,CAAC,GAAG,CAAC,EAAE,GAChE;KACG,KAAK,IAAI,MAAM,aAAa,GAAG,KAAK,SAAS,MAAM,GAAG,gBAAgB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,GAC3F,aAAa,CAAC,KAAK,CAAC;CACvB,CAAC;AAEN,MAAM,MAAM,aAAa,CAAC,aAAa,SAAS,iBAAiB,IAC/D;IAAC,GAAG,EAAE;CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,SAAS,oBAAoB,CAAC,GAAG,CAAC,EAAE,GACpE;KACG,KAAK,IAAI,MAAM,aAAa,GAAG,KAAK,SAAS,MAAM,GAAG,oBAAoB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,GAC/F,aAAa,CAAC,KAAK,CAAC;CACvB,CAAC;AAEN;;;;;GAKG;AACH,qBAAa,eAAe,CAAC,IAAI,SAAS,MAAM;IAC9C,QAAQ,EAAE,yBAAyB,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC;IACtD,KAAK,EAAE,yBAAyB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;IAChD,UAAU,EAAE,yBAAyB,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC;IAC1D,WAAW,EAAE,yBAAyB,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;IAC5D,IAAI,CAAC,EAAE,yBAAyB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;gBAEnC,KAAK,EAAE,yBAAyB,CAAC,IAAI,CAAC;CAOnD;AAED;;;GAGG;AACH,qBAAa,mBAAmB,CAAC,IAAI,SAAS,MAAM;IAClD,IAAI,EAAE,UAAU,CAAC;IACjB,QAAQ,EAAE,yBAAyB,CAAC,IAAI,CAAC,CAAC;gBAE9B,KAAK,EAAE,yBAAyB,CAAC,IAAI,CAAC;CAInD"}