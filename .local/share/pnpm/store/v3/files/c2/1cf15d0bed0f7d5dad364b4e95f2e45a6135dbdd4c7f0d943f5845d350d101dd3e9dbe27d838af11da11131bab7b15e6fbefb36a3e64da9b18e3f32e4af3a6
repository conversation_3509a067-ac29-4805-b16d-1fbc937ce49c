{"version": 3, "file": "browser.js", "sources": ["index.js"], "sourcesContent": ["const tinycolor = require('tinycolor2');\n\n/**\n * @typedef {Object} TinyGradient.StopInput\n * @property {ColorInput} color\n * @property {number} pos\n */\n\n/**\n * @typedef {Object} TinyGradient.StepValue\n * @type {number} [r]\n * @type {number} [g]\n * @type {number} [b]\n * @type {number} [h]\n * @type {number} [s]\n * @type {number} [v]\n * @type {number} [a]\n */\n\n/**\n * @type {StepValue}\n */\nconst RGBA_MAX = { r: 256, g: 256, b: 256, a: 1 };\n\n/**\n * @type {StepValue}\n */\nconst HSVA_MAX = { h: 360, s: 1, v: 1, a: 1 };\n\n/**\n * Linearly compute the step size between start and end (not normalized)\n * @param {StepValue} start\n * @param {StepValue} end\n * @param {number} steps - number of desired steps\n * @return {StepValue}\n */\nfunction stepize(start, end, steps) {\n    let step = {};\n\n    for (let k in start) {\n        if (start.hasOwnProperty(k)) {\n            step[k] = steps === 0 ? 0 : (end[k] - start[k]) / steps;\n        }\n    }\n\n    return step;\n}\n\n/**\n * Compute the final step color\n * @param {StepValue} step - from `stepize`\n * @param {StepValue} start\n * @param {number} i - color index\n * @param {StepValue} max - rgba or hsva of maximum values for each channel\n * @return {StepValue}\n */\nfunction interpolate(step, start, i, max) {\n    let color = {};\n\n    for (let k in start) {\n        if (start.hasOwnProperty(k)) {\n            color[k] = step[k] * i + start[k];\n            color[k] = color[k] < 0 ? color[k] + max[k] : (max[k] !== 1 ? color[k] % max[k] : color[k]);\n        }\n    }\n\n    return color;\n}\n\n/**\n * Generate gradient with RGBa interpolation\n * @param {StopInput} stop1\n * @param {StopInput} stop2\n * @param {number} steps\n * @return {tinycolor[]} color1 included, color2 excluded\n */\nfunction interpolateRgb(stop1, stop2, steps) {\n    const start = stop1.color.toRgb();\n    const end = stop2.color.toRgb();\n    const step = stepize(start, end, steps);\n    let gradient = [stop1.color];\n\n    for (let i = 1; i < steps; i++) {\n        const color = interpolate(step, start, i, RGBA_MAX);\n        gradient.push(tinycolor(color));\n    }\n\n    return gradient;\n}\n\n/**\n * Generate gradient with HSVa interpolation\n * @param {StopInput} stop1\n * @param {StopInput} stop2\n * @param {number} steps\n * @param {boolean|'long'|'short'} mode\n * @return {tinycolor[]} color1 included, color2 excluded\n */\nfunction interpolateHsv(stop1, stop2, steps, mode) {\n    const start = stop1.color.toHsv();\n    const end = stop2.color.toHsv();\n\n    // rgb interpolation if one of the steps in grayscale\n    if (start.s === 0 || end.s === 0) {\n        return interpolateRgb(stop1, stop2, steps);\n    }\n\n    let trigonometric;\n    if (typeof mode === 'boolean') {\n        trigonometric = mode;\n    }\n    else {\n        const trigShortest = (start.h < end.h && end.h - start.h < 180) || (start.h > end.h && start.h - end.h > 180);\n        trigonometric = (mode === 'long' && trigShortest) || (mode === 'short' && !trigShortest);\n    }\n\n    const step = stepize(start, end, steps);\n    let gradient = [stop1.color];\n\n    // recompute hue\n    let diff;\n    if ((start.h <= end.h && !trigonometric) || (start.h >= end.h && trigonometric)) {\n        diff = end.h - start.h;\n    }\n    else if (trigonometric) {\n        diff = 360 - end.h + start.h;\n    }\n    else {\n        diff = 360 - start.h + end.h;\n    }\n    step.h = Math.pow(-1, trigonometric ? 1 : 0) * Math.abs(diff) / steps;\n\n    for (let i = 1; i < steps; i++) {\n        const color = interpolate(step, start, i, HSVA_MAX);\n        gradient.push(tinycolor(color));\n    }\n\n    return gradient;\n}\n\n/**\n * Compute substeps between each stops\n * @param {StopInput[]} stops\n * @param {number} steps\n * @return {number[]}\n */\nfunction computeSubsteps(stops, steps) {\n    const l = stops.length;\n\n    // validation\n    steps = parseInt(steps, 10);\n\n    if (isNaN(steps) || steps < 2) {\n        throw new Error('Invalid number of steps (< 2)');\n    }\n    if (steps < l) {\n        throw new Error('Number of steps cannot be inferior to number of stops');\n    }\n\n    // compute substeps from stop positions\n    let substeps = [];\n\n    for (let i = 1; i < l; i++) {\n        const step = (steps - 1) * (stops[i].pos - stops[i - 1].pos);\n        substeps.push(Math.max(1, Math.round(step)));\n    }\n\n    // adjust number of steps\n    let totalSubsteps = 1;\n    for (let n = l - 1; n--;) totalSubsteps += substeps[n];\n\n    while (totalSubsteps !== steps) {\n        if (totalSubsteps < steps) {\n            const min = Math.min.apply(null, substeps);\n            substeps[substeps.indexOf(min)]++;\n            totalSubsteps++;\n        }\n        else {\n            const max = Math.max.apply(null, substeps);\n            substeps[substeps.indexOf(max)]--;\n            totalSubsteps--;\n        }\n    }\n\n    return substeps;\n}\n\n/**\n * Compute the color at a specific position\n * @param {StopInput[]} stops\n * @param {number} pos\n * @param {string} method\n * @param {StepValue} max\n * @returns {tinycolor}\n */\nfunction computeAt(stops, pos, method, max) {\n    if (pos < 0 || pos > 1) {\n        throw new Error('Position must be between 0 and 1');\n    }\n\n    let start, end;\n    for (let i = 0, l = stops.length; i < l - 1; i++) {\n        if (pos >= stops[i].pos && pos < stops[i + 1].pos) {\n            start = stops[i];\n            end = stops[i + 1];\n            break;\n        }\n    }\n\n    if (!start) {\n        start = end = stops[stops.length - 1];\n    }\n\n    const step = stepize(start.color[method](), end.color[method](), (end.pos - start.pos) * 100);\n    const color = interpolate(step, start.color[method](), (pos - start.pos) * 100, max);\n    return tinycolor(color);\n}\n\nclass TinyGradient {\n    /**\n     * @param {StopInput[]|ColorInput[]} stops\n     * @returns {TinyGradient}\n     */\n    constructor(stops) {\n        // validation\n        if (stops.length < 2) {\n            throw new Error('Invalid number of stops (< 2)');\n        }\n\n        const havingPositions = stops[0].pos !== undefined;\n        let l = stops.length;\n        let p = -1;\n        let lastColorLess = false;\n        // create tinycolor objects and clean positions\n        this.stops = stops.map((stop, i) => {\n            const hasPosition = stop.pos !== undefined;\n            if (havingPositions ^ hasPosition) {\n                throw new Error('Cannot mix positionned and not posionned color stops');\n            }\n\n            if (hasPosition) {\n                const hasColor = stop.color !== undefined;\n                if (!hasColor && (lastColorLess || i === 0 || i === l - 1)) {\n                    throw new Error('Cannot define two consecutive position-only stops');\n                }\n                lastColorLess = !hasColor;\n\n                stop = {\n                    color    : hasColor ? tinycolor(stop.color) : null,\n                    colorLess: !hasColor,\n                    pos      : stop.pos\n                };\n\n                if (stop.pos < 0 || stop.pos > 1) {\n                    throw new Error('Color stops positions must be between 0 and 1');\n                }\n                else if (stop.pos < p) {\n                    throw new Error('Color stops positions are not ordered');\n                }\n                p = stop.pos;\n            }\n            else {\n                stop = {\n                    color: tinycolor(stop.color !== undefined ? stop.color : stop),\n                    pos  : i / (l - 1)\n                };\n            }\n\n            return stop;\n        });\n\n        if (this.stops[0].pos !== 0) {\n            this.stops.unshift({\n                color: this.stops[0].color,\n                pos  : 0\n            });\n            l++;\n        }\n        if (this.stops[l - 1].pos !== 1) {\n            this.stops.push({\n                color: this.stops[l - 1].color,\n                pos  : 1\n            });\n        }\n    }\n\n    /**\n     * Return new instance with reversed stops\n     * @return {TinyGradient}\n     */\n    reverse() {\n        let stops = [];\n\n        this.stops.forEach(function (stop) {\n            stops.push({\n                color: stop.color,\n                pos  : 1 - stop.pos\n            });\n        });\n\n        return new TinyGradient(stops.reverse());\n    }\n\n    /**\n     * Return new instance with looped stops\n     * @return {TinyGradient}\n     */\n    loop() {\n        let stops1 = [];\n        let stops2 = [];\n\n        this.stops.forEach((stop) => {\n            stops1.push({\n                color: stop.color,\n                pos  : stop.pos / 2\n            });\n        });\n\n        this.stops.slice(0, -1).forEach((stop) => {\n            stops2.push({\n                color: stop.color,\n                pos  : 1 - stop.pos / 2\n            });\n        });\n\n        return new TinyGradient(stops1.concat(stops2.reverse()));\n    }\n\n    /**\n     * Generate gradient with RGBa interpolation\n     * @param {number} steps\n     * @return {tinycolor[]}\n     */\n    rgb(steps) {\n        const substeps = computeSubsteps(this.stops, steps);\n        let gradient = [];\n\n        this.stops.forEach((stop, i) => {\n            if (stop.colorLess) {\n                stop.color = interpolateRgb(this.stops[i - 1], this.stops[i + 1], 2)[1];\n            }\n        });\n\n        for (let i = 0, l = this.stops.length; i < l - 1; i++) {\n            const rgb = interpolateRgb(this.stops[i], this.stops[i + 1], substeps[i]);\n            gradient.splice(gradient.length, 0, ...rgb);\n        }\n\n        gradient.push(this.stops[this.stops.length - 1].color);\n\n        return gradient;\n    }\n\n    /**\n     * Generate gradient with HSVa interpolation\n     * @param {number} steps\n     * @param {boolean|'long'|'short'} [mode=false]\n     *    - false to step in clockwise\n     *    - true to step in trigonometric order\n     *    - 'short' to use the shortest way\n     *    - 'long' to use the longest way\n     * @return {tinycolor[]}\n     */\n    hsv(steps, mode) {\n        const substeps = computeSubsteps(this.stops, steps);\n        let gradient = [];\n\n        this.stops.forEach((stop, i) => {\n            if (stop.colorLess) {\n                stop.color = interpolateHsv(this.stops[i - 1], this.stops[i + 1], 2, mode)[1];\n            }\n        });\n\n        for (let i = 0, l = this.stops.length; i < l - 1; i++) {\n            const hsv = interpolateHsv(this.stops[i], this.stops[i + 1], substeps[i], mode);\n            gradient.splice(gradient.length, 0, ...hsv);\n        }\n\n        gradient.push(this.stops[this.stops.length - 1].color);\n\n        return gradient;\n    }\n\n    /**\n     * Generate CSS3 command (no prefix) for this gradient\n     * @param {String} [mode=linear] - 'linear' or 'radial'\n     * @param {String} [direction] - default is 'to right' or 'ellipse at center'\n     * @return {String}\n     */\n    css(mode, direction) {\n        mode = mode || 'linear';\n        direction = direction || (mode === 'linear' ? 'to right' : 'ellipse at center');\n\n        let css = mode + '-gradient(' + direction;\n        this.stops.forEach(function (stop) {\n            css += ', ' + (stop.colorLess ? '' : stop.color.toRgbString() + ' ') + (stop.pos * 100) + '%';\n        });\n        css += ')';\n        return css;\n    }\n\n    /**\n     * Returns the color at specific position with RGBa interpolation\n     * @param {number} pos, between 0 and 1\n     * @return {tinycolor}\n     */\n    rgbAt(pos) {\n        return computeAt(this.stops, pos, 'toRgb', RGBA_MAX);\n    }\n\n    /**\n     * Returns the color at specific position with HSVa interpolation\n     * @param {number} pos, between 0 and 1\n     * @return {tinycolor}\n     */\n    hsvAt(pos) {\n        return computeAt(this.stops, pos, 'toHsv', HSVA_MAX);\n    }\n}\n\n/**\n * @param {StopInput[]|ColorInput[]|StopInput...|ColorInput...} stops\n * @returns {TinyGradient}\n */\nmodule.exports = function (stops) {\n    // varargs\n    if (arguments.length === 1) {\n        if (!Array.isArray(arguments[0])) {\n            throw new Error('\"stops\" is not an array');\n        }\n        stops = arguments[0];\n    }\n    else {\n        stops = Array.prototype.slice.call(arguments);\n    }\n\n    return new TinyGradient(stops);\n};\n"], "names": ["RGBA_MAX", "r", "g", "b", "a", "HSVA_MAX", "h", "s", "v", "stepize", "start", "end", "steps", "step", "k", "hasOwnProperty", "interpolate", "i", "max", "color", "interpolateRgb", "stop1", "stop2", "toRgb", "gradient", "push", "tinycolor", "interpolateHsv", "mode", "toHsv", "trigonometric", "trigShortest", "diff", "Math", "pow", "abs", "computeSubsteps", "stops", "l", "length", "parseInt", "isNaN", "Error", "substeps", "pos", "round", "totalSubsteps", "n", "min", "apply", "indexOf", "computeAt", "method", "TinyGradient", "havingPositions", "undefined", "p", "lastColorLess", "map", "stop", "hasPosition", "hasColor", "colorLess", "unshift", "reverse", "for<PERSON>ach", "loop", "stops1", "stops2", "slice", "concat", "rgb", "splice", "hsv", "css", "direction", "toRgbString", "rgbAt", "hsvAt", "arguments", "Array", "isArray", "prototype", "call"], "mappings": ";;;;;;;;;;;;;;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;;IACA,IAAMA,QAAQ,GAAG;IAAEC,EAAAA,CAAC,EAAE,GAAL;IAAUC,EAAAA,CAAC,EAAE,GAAb;IAAkBC,EAAAA,CAAC,EAAE,GAArB;IAA0BC,EAAAA,CAAC,EAAE;IAA7B,CAAjB;IAEA;IACA;IACA;;IACA,IAAMC,QAAQ,GAAG;IAAEC,EAAAA,CAAC,EAAE,GAAL;IAAUC,EAAAA,CAAC,EAAE,CAAb;IAAgBC,EAAAA,CAAC,EAAE,CAAnB;IAAsBJ,EAAAA,CAAC,EAAE;IAAzB,CAAjB;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASK,OAAT,CAAiBC,KAAjB,EAAwBC,GAAxB,EAA6BC,KAA7B,EAAoC;IAChC,MAAIC,IAAI,GAAG,EAAX;;IAEA,OAAK,IAAIC,CAAT,IAAcJ,KAAd,EAAqB;IACjB,QAAIA,KAAK,CAACK,cAAN,CAAqBD,CAArB,CAAJ,EAA6B;IACzBD,MAAAA,IAAI,CAACC,CAAD,CAAJ,GAAUF,KAAK,KAAK,CAAV,GAAc,CAAd,GAAkB,CAACD,GAAG,CAACG,CAAD,CAAH,GAASJ,KAAK,CAACI,CAAD,CAAf,IAAsBF,KAAlD;IACH;IACJ;;IAED,SAAOC,IAAP;IACH;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,SAASG,WAAT,CAAqBH,IAArB,EAA2BH,KAA3B,EAAkCO,CAAlC,EAAqCC,GAArC,EAA0C;IACtC,MAAIC,KAAK,GAAG,EAAZ;;IAEA,OAAK,IAAIL,CAAT,IAAcJ,KAAd,EAAqB;IACjB,QAAIA,KAAK,CAACK,cAAN,CAAqBD,CAArB,CAAJ,EAA6B;IACzBK,MAAAA,KAAK,CAACL,CAAD,CAAL,GAAWD,IAAI,CAACC,CAAD,CAAJ,GAAUG,CAAV,GAAcP,KAAK,CAACI,CAAD,CAA9B;IACAK,MAAAA,KAAK,CAACL,CAAD,CAAL,GAAWK,KAAK,CAACL,CAAD,CAAL,GAAW,CAAX,GAAeK,KAAK,CAACL,CAAD,CAAL,GAAWI,GAAG,CAACJ,CAAD,CAA7B,GAAoCI,GAAG,CAACJ,CAAD,CAAH,KAAW,CAAX,GAAeK,KAAK,CAACL,CAAD,CAAL,GAAWI,GAAG,CAACJ,CAAD,CAA7B,GAAmCK,KAAK,CAACL,CAAD,CAAvF;IACH;IACJ;;IAED,SAAOK,KAAP;IACH;IAED;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,SAASC,cAAT,CAAwBC,KAAxB,EAA+BC,KAA/B,EAAsCV,KAAtC,EAA6C;IACzC,MAAMF,KAAK,GAAGW,KAAK,CAACF,KAAN,CAAYI,KAAZ,EAAd;IACA,MAAMZ,GAAG,GAAGW,KAAK,CAACH,KAAN,CAAYI,KAAZ,EAAZ;IACA,MAAMV,IAAI,GAAGJ,OAAO,CAACC,KAAD,EAAQC,GAAR,EAAaC,KAAb,CAApB;IACA,MAAIY,QAAQ,GAAG,CAACH,KAAK,CAACF,KAAP,CAAf;;IAEA,OAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,KAApB,EAA2BK,CAAC,EAA5B,EAAgC;IAC5B,QAAME,KAAK,GAAGH,WAAW,CAACH,IAAD,EAAOH,KAAP,EAAcO,CAAd,EAAiBjB,QAAjB,CAAzB;IACAwB,IAAAA,QAAQ,CAACC,IAAT,CAAcC,8BAAS,CAACP,KAAD,CAAvB;IACH;;IAED,SAAOK,QAAP;IACH;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,SAASG,cAAT,CAAwBN,KAAxB,EAA+BC,KAA/B,EAAsCV,KAAtC,EAA6CgB,IAA7C,EAAmD;IAC/C,MAAMlB,KAAK,GAAGW,KAAK,CAACF,KAAN,CAAYU,KAAZ,EAAd;IACA,MAAMlB,GAAG,GAAGW,KAAK,CAACH,KAAN,CAAYU,KAAZ,EAAZ,CAF+C;;IAK/C,MAAInB,KAAK,CAACH,CAAN,KAAY,CAAZ,IAAiBI,GAAG,CAACJ,CAAJ,KAAU,CAA/B,EAAkC;IAC9B,WAAOa,cAAc,CAACC,KAAD,EAAQC,KAAR,EAAeV,KAAf,CAArB;IACH;;IAED,MAAIkB,aAAJ;;IACA,MAAI,OAAOF,IAAP,KAAgB,SAApB,EAA+B;IAC3BE,IAAAA,aAAa,GAAGF,IAAhB;IACH,GAFD,MAGK;IACD,QAAMG,YAAY,GAAIrB,KAAK,CAACJ,CAAN,GAAUK,GAAG,CAACL,CAAd,IAAmBK,GAAG,CAACL,CAAJ,GAAQI,KAAK,CAACJ,CAAd,GAAkB,GAAtC,IAA+CI,KAAK,CAACJ,CAAN,GAAUK,GAAG,CAACL,CAAd,IAAmBI,KAAK,CAACJ,CAAN,GAAUK,GAAG,CAACL,CAAd,GAAkB,GAAzG;IACAwB,IAAAA,aAAa,GAAIF,IAAI,KAAK,MAAT,IAAmBG,YAApB,IAAsCH,IAAI,KAAK,OAAT,IAAoB,CAACG,YAA3E;IACH;;IAED,MAAMlB,IAAI,GAAGJ,OAAO,CAACC,KAAD,EAAQC,GAAR,EAAaC,KAAb,CAApB;IACA,MAAIY,QAAQ,GAAG,CAACH,KAAK,CAACF,KAAP,CAAf,CAnB+C;;IAsB/C,MAAIa,IAAJ;;IACA,MAAKtB,KAAK,CAACJ,CAAN,IAAWK,GAAG,CAACL,CAAf,IAAoB,CAACwB,aAAtB,IAAyCpB,KAAK,CAACJ,CAAN,IAAWK,GAAG,CAACL,CAAf,IAAoBwB,aAAjE,EAAiF;IAC7EE,IAAAA,IAAI,GAAGrB,GAAG,CAACL,CAAJ,GAAQI,KAAK,CAACJ,CAArB;IACH,GAFD,MAGK,IAAIwB,aAAJ,EAAmB;IACpBE,IAAAA,IAAI,GAAG,MAAMrB,GAAG,CAACL,CAAV,GAAcI,KAAK,CAACJ,CAA3B;IACH,GAFI,MAGA;IACD0B,IAAAA,IAAI,GAAG,MAAMtB,KAAK,CAACJ,CAAZ,GAAgBK,GAAG,CAACL,CAA3B;IACH;;IACDO,EAAAA,IAAI,CAACP,CAAL,GAAS2B,IAAI,CAACC,GAAL,CAAS,CAAC,CAAV,EAAaJ,aAAa,GAAG,CAAH,GAAO,CAAjC,IAAsCG,IAAI,CAACE,GAAL,CAASH,IAAT,CAAtC,GAAuDpB,KAAhE;;IAEA,OAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,KAApB,EAA2BK,CAAC,EAA5B,EAAgC;IAC5B,QAAME,KAAK,GAAGH,WAAW,CAACH,IAAD,EAAOH,KAAP,EAAcO,CAAd,EAAiBZ,QAAjB,CAAzB;IACAmB,IAAAA,QAAQ,CAACC,IAAT,CAAcC,8BAAS,CAACP,KAAD,CAAvB;IACH;;IAED,SAAOK,QAAP;IACH;IAED;IACA;IACA;IACA;IACA;IACA;;;IACA,SAASY,eAAT,CAAyBC,KAAzB,EAAgCzB,KAAhC,EAAuC;IACnC,MAAM0B,CAAC,GAAGD,KAAK,CAACE,MAAhB,CADmC;;IAInC3B,EAAAA,KAAK,GAAG4B,QAAQ,CAAC5B,KAAD,EAAQ,EAAR,CAAhB;;IAEA,MAAI6B,KAAK,CAAC7B,KAAD,CAAL,IAAgBA,KAAK,GAAG,CAA5B,EAA+B;IAC3B,UAAM,IAAI8B,KAAJ,CAAU,+BAAV,CAAN;IACH;;IACD,MAAI9B,KAAK,GAAG0B,CAAZ,EAAe;IACX,UAAM,IAAII,KAAJ,CAAU,uDAAV,CAAN;IACH,GAXkC;;;IAcnC,MAAIC,QAAQ,GAAG,EAAf;;IAEA,OAAK,IAAI1B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqB,CAApB,EAAuBrB,CAAC,EAAxB,EAA4B;IACxB,QAAMJ,IAAI,GAAG,CAACD,KAAK,GAAG,CAAT,KAAeyB,KAAK,CAACpB,CAAD,CAAL,CAAS2B,GAAT,GAAeP,KAAK,CAACpB,CAAC,GAAG,CAAL,CAAL,CAAa2B,GAA3C,CAAb;IACAD,IAAAA,QAAQ,CAAClB,IAAT,CAAcQ,IAAI,CAACf,GAAL,CAAS,CAAT,EAAYe,IAAI,CAACY,KAAL,CAAWhC,IAAX,CAAZ,CAAd;IACH,GAnBkC;;;IAsBnC,MAAIiC,aAAa,GAAG,CAApB;;IACA,OAAK,IAAIC,CAAC,GAAGT,CAAC,GAAG,CAAjB,EAAoBS,CAAC,EAArB;IAA0BD,IAAAA,aAAa,IAAIH,QAAQ,CAACI,CAAD,CAAzB;IAA1B;;IAEA,SAAOD,aAAa,KAAKlC,KAAzB,EAAgC;IAC5B,QAAIkC,aAAa,GAAGlC,KAApB,EAA2B;IACvB,UAAMoC,GAAG,GAAGf,IAAI,CAACe,GAAL,CAASC,KAAT,CAAe,IAAf,EAAqBN,QAArB,CAAZ;IACAA,MAAAA,QAAQ,CAACA,QAAQ,CAACO,OAAT,CAAiBF,GAAjB,CAAD,CAAR;IACAF,MAAAA,aAAa;IAChB,KAJD,MAKK;IACD,UAAM5B,GAAG,GAAGe,IAAI,CAACf,GAAL,CAAS+B,KAAT,CAAe,IAAf,EAAqBN,QAArB,CAAZ;IACAA,MAAAA,QAAQ,CAACA,QAAQ,CAACO,OAAT,CAAiBhC,GAAjB,CAAD,CAAR;IACA4B,MAAAA,aAAa;IAChB;IACJ;;IAED,SAAOH,QAAP;IACH;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,SAASQ,SAAT,CAAmBd,KAAnB,EAA0BO,GAA1B,EAA+BQ,MAA/B,EAAuClC,GAAvC,EAA4C;IACxC,MAAI0B,GAAG,GAAG,CAAN,IAAWA,GAAG,GAAG,CAArB,EAAwB;IACpB,UAAM,IAAIF,KAAJ,CAAU,kCAAV,CAAN;IACH;;IAED,MAAIhC,KAAJ,EAAWC,GAAX;;IACA,OAAK,IAAIM,CAAC,GAAG,CAAR,EAAWqB,CAAC,GAAGD,KAAK,CAACE,MAA1B,EAAkCtB,CAAC,GAAGqB,CAAC,GAAG,CAA1C,EAA6CrB,CAAC,EAA9C,EAAkD;IAC9C,QAAI2B,GAAG,IAAIP,KAAK,CAACpB,CAAD,CAAL,CAAS2B,GAAhB,IAAuBA,GAAG,GAAGP,KAAK,CAACpB,CAAC,GAAG,CAAL,CAAL,CAAa2B,GAA9C,EAAmD;IAC/ClC,MAAAA,KAAK,GAAG2B,KAAK,CAACpB,CAAD,CAAb;IACAN,MAAAA,GAAG,GAAG0B,KAAK,CAACpB,CAAC,GAAG,CAAL,CAAX;IACA;IACH;IACJ;;IAED,MAAI,CAACP,KAAL,EAAY;IACRA,IAAAA,KAAK,GAAGC,GAAG,GAAG0B,KAAK,CAACA,KAAK,CAACE,MAAN,GAAe,CAAhB,CAAnB;IACH;;IAED,MAAM1B,IAAI,GAAGJ,OAAO,CAACC,KAAK,CAACS,KAAN,CAAYiC,MAAZ,GAAD,EAAwBzC,GAAG,CAACQ,KAAJ,CAAUiC,MAAV,GAAxB,EAA6C,CAACzC,GAAG,CAACiC,GAAJ,GAAUlC,KAAK,CAACkC,GAAjB,IAAwB,GAArE,CAApB;IACA,MAAMzB,KAAK,GAAGH,WAAW,CAACH,IAAD,EAAOH,KAAK,CAACS,KAAN,CAAYiC,MAAZ,GAAP,EAA8B,CAACR,GAAG,GAAGlC,KAAK,CAACkC,GAAb,IAAoB,GAAlD,EAAuD1B,GAAvD,CAAzB;IACA,SAAOQ,8BAAS,CAACP,KAAD,CAAhB;IACH;;QAEKkC;;IAEN;IACA;IACA;IACI,wBAAYhB,KAAZ,EAAmB;;IAEf,QAAIA,KAAK,CAACE,MAAN,GAAe,CAAnB,EAAsB;IAClB,YAAM,IAAIG,KAAJ,CAAU,+BAAV,CAAN;IACH;;IAED,QAAMY,eAAe,GAAGjB,KAAK,CAAC,CAAD,CAAL,CAASO,GAAT,KAAiBW,SAAzC;IACA,QAAIjB,CAAC,GAAGD,KAAK,CAACE,MAAd;IACA,QAAIiB,CAAC,GAAG,CAAC,CAAT;IACA,QAAIC,aAAa,GAAG,KAApB,CATe;;IAWf,SAAKpB,KAAL,GAAaA,KAAK,CAACqB,GAAN,CAAU,UAACC,IAAD,EAAO1C,CAAP,EAAa;IAChC,UAAM2C,WAAW,GAAGD,IAAI,CAACf,GAAL,KAAaW,SAAjC;;IACA,UAAID,eAAe,GAAGM,WAAtB,EAAmC;IAC/B,cAAM,IAAIlB,KAAJ,CAAU,sDAAV,CAAN;IACH;;IAED,UAAIkB,WAAJ,EAAiB;IACb,YAAMC,QAAQ,GAAGF,IAAI,CAACxC,KAAL,KAAeoC,SAAhC;;IACA,YAAI,CAACM,QAAD,KAAcJ,aAAa,IAAIxC,CAAC,KAAK,CAAvB,IAA4BA,CAAC,KAAKqB,CAAC,GAAG,CAApD,CAAJ,EAA4D;IACxD,gBAAM,IAAII,KAAJ,CAAU,mDAAV,CAAN;IACH;;IACDe,QAAAA,aAAa,GAAG,CAACI,QAAjB;IAEAF,QAAAA,IAAI,GAAG;IACHxC,UAAAA,KAAK,EAAM0C,QAAQ,GAAGnC,8BAAS,CAACiC,IAAI,CAACxC,KAAN,CAAZ,GAA2B,IAD3C;IAEH2C,UAAAA,SAAS,EAAE,CAACD,QAFT;IAGHjB,UAAAA,GAAG,EAAQe,IAAI,CAACf;IAHb,SAAP;;IAMA,YAAIe,IAAI,CAACf,GAAL,GAAW,CAAX,IAAgBe,IAAI,CAACf,GAAL,GAAW,CAA/B,EAAkC;IAC9B,gBAAM,IAAIF,KAAJ,CAAU,+CAAV,CAAN;IACH,SAFD,MAGK,IAAIiB,IAAI,CAACf,GAAL,GAAWY,CAAf,EAAkB;IACnB,gBAAM,IAAId,KAAJ,CAAU,uCAAV,CAAN;IACH;;IACDc,QAAAA,CAAC,GAAGG,IAAI,CAACf,GAAT;IACH,OApBD,MAqBK;IACDe,QAAAA,IAAI,GAAG;IACHxC,UAAAA,KAAK,EAAEO,8BAAS,CAACiC,IAAI,CAACxC,KAAL,KAAeoC,SAAf,GAA2BI,IAAI,CAACxC,KAAhC,GAAwCwC,IAAzC,CADb;IAEHf,UAAAA,GAAG,EAAI3B,CAAC,IAAIqB,CAAC,GAAG,CAAR;IAFL,SAAP;IAIH;;IAED,aAAOqB,IAAP;IACH,KAnCY,CAAb;;IAqCA,QAAI,KAAKtB,KAAL,CAAW,CAAX,EAAcO,GAAd,KAAsB,CAA1B,EAA6B;IACzB,WAAKP,KAAL,CAAW0B,OAAX,CAAmB;IACf5C,QAAAA,KAAK,EAAE,KAAKkB,KAAL,CAAW,CAAX,EAAclB,KADN;IAEfyB,QAAAA,GAAG,EAAI;IAFQ,OAAnB;IAIAN,MAAAA,CAAC;IACJ;;IACD,QAAI,KAAKD,KAAL,CAAWC,CAAC,GAAG,CAAf,EAAkBM,GAAlB,KAA0B,CAA9B,EAAiC;IAC7B,WAAKP,KAAL,CAAWZ,IAAX,CAAgB;IACZN,QAAAA,KAAK,EAAE,KAAKkB,KAAL,CAAWC,CAAC,GAAG,CAAf,EAAkBnB,KADb;IAEZyB,QAAAA,GAAG,EAAI;IAFK,OAAhB;IAIH;IACJ;;IAGL;IACA;IACA;;;;;aACIoB,UAAA,mBAAU;IACN,QAAI3B,KAAK,GAAG,EAAZ;IAEA,SAAKA,KAAL,CAAW4B,OAAX,CAAmB,UAAUN,IAAV,EAAgB;IAC/BtB,MAAAA,KAAK,CAACZ,IAAN,CAAW;IACPN,QAAAA,KAAK,EAAEwC,IAAI,CAACxC,KADL;IAEPyB,QAAAA,GAAG,EAAI,IAAIe,IAAI,CAACf;IAFT,OAAX;IAIH,KALD;IAOA,WAAO,IAAIS,YAAJ,CAAiBhB,KAAK,CAAC2B,OAAN,EAAjB,CAAP;IACH;;IAGL;IACA;IACA;;;aACIE,OAAA,gBAAO;IACH,QAAIC,MAAM,GAAG,EAAb;IACA,QAAIC,MAAM,GAAG,EAAb;IAEA,SAAK/B,KAAL,CAAW4B,OAAX,CAAmB,UAACN,IAAD,EAAU;IACzBQ,MAAAA,MAAM,CAAC1C,IAAP,CAAY;IACRN,QAAAA,KAAK,EAAEwC,IAAI,CAACxC,KADJ;IAERyB,QAAAA,GAAG,EAAIe,IAAI,CAACf,GAAL,GAAW;IAFV,OAAZ;IAIH,KALD;IAOA,SAAKP,KAAL,CAAWgC,KAAX,CAAiB,CAAjB,EAAoB,CAAC,CAArB,EAAwBJ,OAAxB,CAAgC,UAACN,IAAD,EAAU;IACtCS,MAAAA,MAAM,CAAC3C,IAAP,CAAY;IACRN,QAAAA,KAAK,EAAEwC,IAAI,CAACxC,KADJ;IAERyB,QAAAA,GAAG,EAAI,IAAIe,IAAI,CAACf,GAAL,GAAW;IAFd,OAAZ;IAIH,KALD;IAOA,WAAO,IAAIS,YAAJ,CAAiBc,MAAM,CAACG,MAAP,CAAcF,MAAM,CAACJ,OAAP,EAAd,CAAjB,CAAP;IACH;;IAGL;IACA;IACA;IACA;;;aACIO,MAAA,aAAI3D,KAAJ,EAAW;IAAA;;IACP,QAAM+B,QAAQ,GAAGP,eAAe,CAAC,KAAKC,KAAN,EAAazB,KAAb,CAAhC;IACA,QAAIY,QAAQ,GAAG,EAAf;IAEA,SAAKa,KAAL,CAAW4B,OAAX,CAAmB,UAACN,IAAD,EAAO1C,CAAP,EAAa;IAC5B,UAAI0C,IAAI,CAACG,SAAT,EAAoB;IAChBH,QAAAA,IAAI,CAACxC,KAAL,GAAaC,cAAc,CAAC,KAAI,CAACiB,KAAL,CAAWpB,CAAC,GAAG,CAAf,CAAD,EAAoB,KAAI,CAACoB,KAAL,CAAWpB,CAAC,GAAG,CAAf,CAApB,EAAuC,CAAvC,CAAd,CAAwD,CAAxD,CAAb;IACH;IACJ,KAJD;;IAMA,SAAK,IAAIA,CAAC,GAAG,CAAR,EAAWqB,CAAC,GAAG,KAAKD,KAAL,CAAWE,MAA/B,EAAuCtB,CAAC,GAAGqB,CAAC,GAAG,CAA/C,EAAkDrB,CAAC,EAAnD,EAAuD;IACnD,UAAMsD,GAAG,GAAGnD,cAAc,CAAC,KAAKiB,KAAL,CAAWpB,CAAX,CAAD,EAAgB,KAAKoB,KAAL,CAAWpB,CAAC,GAAG,CAAf,CAAhB,EAAmC0B,QAAQ,CAAC1B,CAAD,CAA3C,CAA1B;IACAO,MAAAA,QAAQ,CAACgD,MAAT,OAAAhD,QAAQ,GAAQA,QAAQ,CAACe,MAAjB,EAAyB,CAAzB,SAA+BgC,GAA/B,EAAR;IACH;;IAED/C,IAAAA,QAAQ,CAACC,IAAT,CAAc,KAAKY,KAAL,CAAW,KAAKA,KAAL,CAAWE,MAAX,GAAoB,CAA/B,EAAkCpB,KAAhD;IAEA,WAAOK,QAAP;IACH;;IAGL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;aACIiD,MAAA,aAAI7D,KAAJ,EAAWgB,IAAX,EAAiB;IAAA;;IACb,QAAMe,QAAQ,GAAGP,eAAe,CAAC,KAAKC,KAAN,EAAazB,KAAb,CAAhC;IACA,QAAIY,QAAQ,GAAG,EAAf;IAEA,SAAKa,KAAL,CAAW4B,OAAX,CAAmB,UAACN,IAAD,EAAO1C,CAAP,EAAa;IAC5B,UAAI0C,IAAI,CAACG,SAAT,EAAoB;IAChBH,QAAAA,IAAI,CAACxC,KAAL,GAAaQ,cAAc,CAAC,MAAI,CAACU,KAAL,CAAWpB,CAAC,GAAG,CAAf,CAAD,EAAoB,MAAI,CAACoB,KAAL,CAAWpB,CAAC,GAAG,CAAf,CAApB,EAAuC,CAAvC,EAA0CW,IAA1C,CAAd,CAA8D,CAA9D,CAAb;IACH;IACJ,KAJD;;IAMA,SAAK,IAAIX,CAAC,GAAG,CAAR,EAAWqB,CAAC,GAAG,KAAKD,KAAL,CAAWE,MAA/B,EAAuCtB,CAAC,GAAGqB,CAAC,GAAG,CAA/C,EAAkDrB,CAAC,EAAnD,EAAuD;IACnD,UAAMwD,GAAG,GAAG9C,cAAc,CAAC,KAAKU,KAAL,CAAWpB,CAAX,CAAD,EAAgB,KAAKoB,KAAL,CAAWpB,CAAC,GAAG,CAAf,CAAhB,EAAmC0B,QAAQ,CAAC1B,CAAD,CAA3C,EAAgDW,IAAhD,CAA1B;IACAJ,MAAAA,QAAQ,CAACgD,MAAT,OAAAhD,QAAQ,GAAQA,QAAQ,CAACe,MAAjB,EAAyB,CAAzB,SAA+BkC,GAA/B,EAAR;IACH;;IAEDjD,IAAAA,QAAQ,CAACC,IAAT,CAAc,KAAKY,KAAL,CAAW,KAAKA,KAAL,CAAWE,MAAX,GAAoB,CAA/B,EAAkCpB,KAAhD;IAEA,WAAOK,QAAP;IACH;;IAGL;IACA;IACA;IACA;IACA;;;aACIkD,MAAA,aAAI9C,IAAJ,EAAU+C,SAAV,EAAqB;IACjB/C,IAAAA,IAAI,GAAGA,IAAI,IAAI,QAAf;IACA+C,IAAAA,SAAS,GAAGA,SAAS,KAAK/C,IAAI,KAAK,QAAT,GAAoB,UAApB,GAAiC,mBAAtC,CAArB;IAEA,QAAI8C,GAAG,GAAG9C,IAAI,GAAG,YAAP,GAAsB+C,SAAhC;IACA,SAAKtC,KAAL,CAAW4B,OAAX,CAAmB,UAAUN,IAAV,EAAgB;IAC/Be,MAAAA,GAAG,IAAI,QAAQf,IAAI,CAACG,SAAL,GAAiB,EAAjB,GAAsBH,IAAI,CAACxC,KAAL,CAAWyD,WAAX,KAA2B,GAAzD,IAAiEjB,IAAI,CAACf,GAAL,GAAW,GAA5E,GAAmF,GAA1F;IACH,KAFD;IAGA8B,IAAAA,GAAG,IAAI,GAAP;IACA,WAAOA,GAAP;IACH;;IAGL;IACA;IACA;IACA;;;aACIG,QAAA,eAAMjC,GAAN,EAAW;IACP,WAAOO,SAAS,CAAC,KAAKd,KAAN,EAAaO,GAAb,EAAkB,OAAlB,EAA2B5C,QAA3B,CAAhB;IACH;;IAGL;IACA;IACA;IACA;;;aACI8E,QAAA,eAAMlC,GAAN,EAAW;IACP,WAAOO,SAAS,CAAC,KAAKd,KAAN,EAAaO,GAAb,EAAkB,OAAlB,EAA2BvC,QAA3B,CAAhB;IACH;;;;IAGL;IACA;IACA;IACA;;;oBACc,GAAG,qBAAA,CAAUgC,KAAV,EAAiB;;IAE9B,MAAI0C,SAAS,CAACxC,MAAV,KAAqB,CAAzB,EAA4B;IACxB,QAAI,CAACyC,KAAK,CAACC,OAAN,CAAcF,SAAS,CAAC,CAAD,CAAvB,CAAL,EAAkC;IAC9B,YAAM,IAAIrC,KAAJ,CAAU,yBAAV,CAAN;IACH;;IACDL,IAAAA,KAAK,GAAG0C,SAAS,CAAC,CAAD,CAAjB;IACH,GALD,MAMK;IACD1C,IAAAA,KAAK,GAAG2C,KAAK,CAACE,SAAN,CAAgBb,KAAhB,CAAsBc,IAAtB,CAA2BJ,SAA3B,CAAR;IACH;;IAED,SAAO,IAAI1B,YAAJ,CAAiBhB,KAAjB,CAAP;IACH;;;;;;;;"}