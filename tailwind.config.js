/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./templates/**/*.html",
    "./static/js/**/*.js",
    "./static/js/**/*.vue",
    "./static/js/**/*.ts",
    "./static/css/**/*.css"
  ],
  darkMode: 'class',
  theme: {
    extend: {
      // Brand Colors usando CSS Variables
      colors: {
        // Primary Brand Colors
        'brand-primary': {
          50: 'var(--brand-primary-50)',
          100: 'var(--brand-primary-100)',
          200: 'var(--brand-primary-200)',
          300: 'var(--brand-primary-300)',
          400: 'var(--brand-primary-400)',
          500: 'var(--brand-primary-500)',
          600: 'var(--brand-primary-600)',
          700: 'var(--brand-primary-700)',
          800: 'var(--brand-primary-800)',
          900: 'var(--brand-primary-900)',
        },
        // Secondary Brand Colors
        'brand-secondary': {
          50: 'var(--brand-secondary-50)',
          100: 'var(--brand-secondary-100)',
          200: 'var(--brand-secondary-200)',
          300: 'var(--brand-secondary-300)',
          400: 'var(--brand-secondary-400)',
          500: 'var(--brand-secondary-500)',
          600: 'var(--brand-secondary-600)',
          700: 'var(--brand-secondary-700)',
          800: 'var(--brand-secondary-800)',
          900: 'var(--brand-secondary-900)',
        },
        // Accent Brand Colors
        'brand-accent': {
          50: 'var(--brand-accent-50)',
          100: 'var(--brand-accent-100)',
          200: 'var(--brand-accent-200)',
          300: 'var(--brand-accent-300)',
          400: 'var(--brand-accent-400)',
          500: 'var(--brand-accent-500)',
          600: 'var(--brand-accent-600)',
          700: 'var(--brand-accent-700)',
          800: 'var(--brand-accent-800)',
          900: 'var(--brand-accent-900)',
        },
        // Semantic Colors
        'brand-success': {
          50: 'var(--brand-success-50)',
          500: 'var(--brand-success-500)',
          600: 'var(--brand-success-600)',
          700: 'var(--brand-success-700)',
        },
        'brand-warning': {
          50: 'var(--brand-warning-50)',
          500: 'var(--brand-warning-500)',
          600: 'var(--brand-warning-600)',
          700: 'var(--brand-warning-700)',
        },
        'brand-error': {
          50: 'var(--brand-error-50)',
          500: 'var(--brand-error-500)',
          600: 'var(--brand-error-600)',
          700: 'var(--brand-error-700)',
        },
        // Context Colors
        'brand-bg': {
          primary: 'var(--brand-bg-primary)',
          secondary: 'var(--brand-bg-secondary)',
          tertiary: 'var(--brand-bg-tertiary)',
        },
        'brand-text': {
          primary: 'var(--brand-text-primary)',
          secondary: 'var(--brand-text-secondary)',
          tertiary: 'var(--brand-text-tertiary)',
        },
        'brand-border': {
          primary: 'var(--brand-border-primary)',
          secondary: 'var(--brand-border-secondary)',
        },
        // Backward compatibility (manteniamo i vecchi nomi)
        primary: {
          50: 'var(--brand-primary-50)',
          100: 'var(--brand-primary-100)',
          200: 'var(--brand-primary-200)',
          300: 'var(--brand-primary-300)',
          400: 'var(--brand-primary-400)',
          500: 'var(--brand-primary-500)',
          600: 'var(--brand-primary-600)',
          700: 'var(--brand-primary-700)',
          800: 'var(--brand-primary-800)',
          900: 'var(--brand-primary-900)',
        },
        secondary: {
          50: 'var(--brand-secondary-50)',
          100: 'var(--brand-secondary-100)',
          200: 'var(--brand-secondary-200)',
          300: 'var(--brand-secondary-300)',
          400: 'var(--brand-secondary-400)',
          500: 'var(--brand-secondary-500)',
          600: 'var(--brand-secondary-600)',
          700: 'var(--brand-secondary-700)',
          800: 'var(--brand-secondary-800)',
          900: 'var(--brand-secondary-900)',
        }
      },
      
      // Brand Typography
      fontFamily: {
        'brand-heading': 'var(--brand-font-heading)',
        'brand-body': 'var(--brand-font-body)',
        'brand-mono': 'var(--brand-font-mono)',
        // Backward compatibility
        'sans': 'var(--brand-font-body)',
        'mono': 'var(--brand-font-mono)',
      },
      
      // Brand Font Weights
      fontWeight: {
        'brand-light': 'var(--brand-font-weight-light)',
        'brand-normal': 'var(--brand-font-weight-normal)',
        'brand-medium': 'var(--brand-font-weight-medium)',
        'brand-semibold': 'var(--brand-font-weight-semibold)',
        'brand-bold': 'var(--brand-font-weight-bold)',
        'brand-extrabold': 'var(--brand-font-weight-extrabold)',
      },
      
      // Brand Font Sizes
      fontSize: {
        'brand-xs': 'var(--brand-text-xs)',
        'brand-sm': 'var(--brand-text-sm)',
        'brand-base': 'var(--brand-text-base)',
        'brand-lg': 'var(--brand-text-lg)',
        'brand-xl': 'var(--brand-text-xl)',
        'brand-2xl': 'var(--brand-text-2xl)',
        'brand-3xl': 'var(--brand-text-3xl)',
        'brand-4xl': 'var(--brand-text-4xl)',
      },
      
      // Brand Spacing
      spacing: {
        'brand-xs': 'var(--brand-spacing-xs)',
        'brand-sm': 'var(--brand-spacing-sm)',
        'brand-md': 'var(--brand-spacing-md)',
        'brand-lg': 'var(--brand-spacing-lg)',
        'brand-xl': 'var(--brand-spacing-xl)',
        'brand-2xl': 'var(--brand-spacing-2xl)',
        'brand-3xl': 'var(--brand-spacing-3xl)',
        'sidebar': 'var(--brand-sidebar-width)',
        'sidebar-collapsed': 'var(--brand-sidebar-collapsed-width)',
        'header': 'var(--brand-header-height)',
      },
      
      // Brand Border Radius
      borderRadius: {
        'brand-none': 'var(--brand-radius-none)',
        'brand-sm': 'var(--brand-radius-sm)',
        'brand-md': 'var(--brand-radius-md)',
        'brand-lg': 'var(--brand-radius-lg)',
        'brand-xl': 'var(--brand-radius-xl)',
        'brand-2xl': 'var(--brand-radius-2xl)',
        'brand-full': 'var(--brand-radius-full)',
      },
      
      // Brand Shadows
      boxShadow: {
        'brand-sm': 'var(--brand-shadow-sm)',
        'brand-md': 'var(--brand-shadow-md)',
        'brand-lg': 'var(--brand-shadow-lg)',
        'brand-xl': 'var(--brand-shadow-xl)',
      },
      
      // Brand Transitions
      transitionDuration: {
        'brand-fast': 'var(--brand-transition-fast)',
        'brand-normal': 'var(--brand-transition-normal)',
        'brand-slow': 'var(--brand-transition-slow)',
      },
      
      // Brand Layout
      maxWidth: {
        'brand-container': 'var(--brand-container-max-width)',
      },
      
      // Brand Background Images (for logos)
      backgroundImage: {
        'brand-logo-main': 'var(--brand-logo-main)',
        'brand-logo-compact': 'var(--brand-logo-compact)',
        'brand-logo-white': 'var(--brand-logo-white)',
        'brand-logo-dark': 'var(--brand-logo-dark)',
      },
      
      // Animation & Keyframes
      animation: {
        'brand-fade-in': 'brandFadeIn var(--brand-transition-normal) ease-out',
        'brand-slide-up': 'brandSlideUp var(--brand-transition-normal) ease-out',
        'brand-scale-in': 'brandScaleIn var(--brand-transition-fast) ease-out',
        'brand-bounce-in': 'brandBounceIn var(--brand-transition-slow) ease-out',
      },
      
      keyframes: {
        brandFadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        brandSlideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        brandScaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        brandBounceIn: {
          '0%': { transform: 'scale(0.3)', opacity: '0' },
          '50%': { transform: 'scale(1.05)' },
          '70%': { transform: 'scale(0.9)' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    // Plugin per componenti brand personalizzati
    function({ addComponents, theme }) {
      addComponents({
        '.btn-brand-primary': {
          backgroundColor: theme('colors.brand-primary.500'),
          color: theme('colors.white'),
          padding: `${theme('spacing.brand-sm')} ${theme('spacing.brand-md')}`,
          borderRadius: theme('borderRadius.brand-md'),
          fontFamily: theme('fontFamily.brand-body'),
          fontWeight: theme('fontWeight.brand-medium'),
          transition: `all ${theme('transitionDuration.brand-fast')} ease-in-out`,
          boxShadow: theme('boxShadow.brand-sm'),
          '&:hover': {
            backgroundColor: theme('colors.brand-primary.600'),
            boxShadow: theme('boxShadow.brand-md'),
            transform: 'translateY(-1px)',
          },
          '&:active': {
            transform: 'translateY(0)',
          },
        },
        '.btn-brand-secondary': {
          backgroundColor: theme('colors.brand-secondary.500'),
          color: theme('colors.white'),
          padding: `${theme('spacing.brand-sm')} ${theme('spacing.brand-md')}`,
          borderRadius: theme('borderRadius.brand-md'),
          fontFamily: theme('fontFamily.brand-body'),
          fontWeight: theme('fontWeight.brand-medium'),
          transition: `all ${theme('transitionDuration.brand-fast')} ease-in-out`,
          boxShadow: theme('boxShadow.brand-sm'),
          '&:hover': {
            backgroundColor: theme('colors.brand-secondary.600'),
            boxShadow: theme('boxShadow.brand-md'),
            transform: 'translateY(-1px)',
          },
        },
        '.card-brand': {
          backgroundColor: theme('colors.brand-bg.primary'),
          border: `1px solid ${theme('colors.brand-border.primary')}`,
          borderRadius: theme('borderRadius.brand-lg'),
          boxShadow: theme('boxShadow.brand-sm'),
          transition: `all ${theme('transitionDuration.brand-normal')} ease-in-out`,
          '&:hover': {
            boxShadow: theme('boxShadow.brand-md'),
            transform: 'translateY(-2px)',
          },
        },
        '.input-brand': {
          backgroundColor: theme('colors.brand-bg.primary'),
          border: `1px solid ${theme('colors.brand-border.secondary')}`,
          borderRadius: theme('borderRadius.brand-md'),
          padding: `${theme('spacing.brand-sm')} ${theme('spacing.brand-md')}`,
          fontFamily: theme('fontFamily.brand-body'),
          color: theme('colors.brand-text.primary'),
          transition: `all ${theme('transitionDuration.brand-fast')} ease-in-out`,
          '&:focus': {
            outline: 'none',
            borderColor: theme('colors.brand-primary.500'),
            boxShadow: `0 0 0 3px ${theme('colors.brand-primary.500')}20`,
          },
        },
      })
    },
  ],
}
