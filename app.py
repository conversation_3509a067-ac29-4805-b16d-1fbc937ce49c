import os
import logging
from flask import Flask, session, redirect, url_for, request, flash
from flask_login import logout_user, current_user
from werkzeug.middleware.proxy_fix import ProxyFix
from datetime import datetime, timedelta
from config import Config
import time
from extensions import db, login_manager, migrate, csrf

# Configure logging
logging.basicConfig(level=logging.INFO) # Modificato INFO per produzione, DEBUG per sviluppo
logger = logging.getLogger(__name__)

PUBLIC_ENDPOINTS = [
    'auth.login', 'auth.register', 'auth.forgot_password', 'auth.reset_password_with_token',
    'landing.spa_routes', 'static', 'password_reset.forgot_password', 'password_reset.reset_token',
    'public_api.get_public_config', 'public_api.get_featured_services',
    'public_api.get_services', 'public_api.get_service_detail',
    'spa'  # SPA catch-all route
]

def create_app(config_object='config.Config', config_overrides=None):
    """Factory function to create and configure the Flask app."""
    app = Flask(__name__)
    app.secret_key = os.environ.get("SESSION_SECRET", os.urandom(24))
    app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)

    # Fix MIME types for ES6 modules
    import mimetypes
    mimetypes.add_type('application/javascript', '.js')
    mimetypes.add_type('application/javascript', '.mjs')

    # Load configuration
    app.config.from_object(config_object)
    if config_overrides:
        app.config.from_mapping(config_overrides)

    # Initialize extensions with app
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'  # Assicurati che 'auth.login' sia il nome corretto della tua route di login
    migrate.init_app(app, db)

    # Configure CSRF protection
    csrf.init_app(app)
    # Make csrf_token available in templates without function call
    app.jinja_env.globals['csrf_token'] = lambda: csrf.generate_csrf()

    with app.app_context():
        # Import models first to ensure they're registered
        from models import User # Spostato import qui per evitare importazioni circolari

        # Add datetime utility for templates
        @app.context_processor
        def utility_processor():
            return {'current_year': datetime.now().year}

        # Import blueprints
        from blueprints.auth import auth_bp
        from blueprints.dashboard import dashboard_bp
        from blueprints.personnel import personnel_bp
        from blueprints.projects import projects_bp
        from blueprints.crm import crm_bp
        from blueprints.products import products_bp
        from blueprints.performance import performance_bp
        from blueprints.communications import communications_bp
        from blueprints.funding import funding_bp
        from blueprints.reporting import reporting_bp
        from blueprints.startup import startup_bp
        from blueprints.landing import landing_bp
        from blueprints.admin import admin_bp
        from blueprints.api import api_bp
        from blueprints.api.public import public_api_bp
        from blueprints.swagger import register_swagger_blueprints

        # Register blueprints
        app.register_blueprint(auth_bp, url_prefix='/auth')
        app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
        app.register_blueprint(personnel_bp)
        app.register_blueprint(projects_bp)
        app.register_blueprint(crm_bp)
        app.register_blueprint(products_bp)
        app.register_blueprint(performance_bp)
        app.register_blueprint(communications_bp)
        app.register_blueprint(funding_bp)
        app.register_blueprint(reporting_bp)
        app.register_blueprint(startup_bp)
        app.register_blueprint(landing_bp)
        app.register_blueprint(admin_bp)
        app.register_blueprint(api_bp, url_prefix='/api')
        app.register_blueprint(public_api_bp)

        # Register Swagger blueprints
        register_swagger_blueprints(app)

        # Custom route for serving JS modules with correct MIME type
        @app.route('/static/js/<path:filename>')
        def serve_js_modules(filename):
            """Serve JavaScript files with correct MIME type for ES6 modules"""
            from flask import send_from_directory, Response
            import os

            js_path = os.path.join(app.static_folder, 'js', filename)
            if os.path.exists(js_path):
                with open(js_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return Response(content, mimetype='application/javascript')
            else:
                from flask import abort
                abort(404)

        # SPA Route - Catch-all for Vue.js routing
        @app.route('/')
        @app.route('/<path:path>')
        def spa(path=''):
            """
            Serve the Vue.js SPA for all routes except API and auth routes.
            This allows Vue Router to handle client-side routing.
            """
            # Don't serve SPA for API routes
            if path.startswith('api/'):
                from flask import abort
                abort(404)

            # Don't serve SPA for auth routes (keep traditional auth)
            if path.startswith('auth/'):
                from flask import abort
                abort(404)

            # Don't serve SPA for static files (except JS modules handled above)
            if path.startswith('static/') and not path.startswith('static/js/'):
                from flask import abort
                abort(404)

            # Don't serve SPA for swagger routes
            if path.startswith('swagger/') or path.startswith('docs/'):
                from flask import abort
                abort(404)

            # Serve the SPA template for all other routes
            from flask import render_template
            return render_template('spa.html')

        # Setup user loader for Flask-Login
        @login_manager.user_loader
        def load_user(user_id):
            return User.query.get(int(user_id))

        # Create database tables if they don't exist
        # db.create_all() # Spostato in conftest.py per i test, e in un comando di init per produzione

        logger.info("Flask app created and configured.")

        @app.before_request
        def session_management():
            if not current_user.is_authenticated:
                return
            now = time.time()
            # Idle timeout
            last_activity = session.get('last_activity')
            if last_activity and now - last_activity > Config.PERMANENT_SESSION_LIFETIME.total_seconds():
                logout_user()
                session.clear()
                flash('Sessione scaduta per inattività. Effettua nuovamente il login.', 'warning')
                return redirect(url_for('auth.login'))
            session['last_activity'] = now
            # Absolute timeout
            login_time = session.get('login_time')
            if login_time and now - login_time > Config.ABSOLUTE_SESSION_LIFETIME:
                logout_user()
                session.clear()
                flash('Sessione scaduta. Effettua nuovamente il login.', 'warning')
                return redirect(url_for('auth.login'))

        @app.before_request
        def global_auth_enforcement():
            endpoint = request.endpoint
            # Log ogni accesso
            user = getattr(current_user, 'username', 'anonymous')
            logger.info(f"Accesso: user={user}, endpoint={endpoint}, ip={request.remote_addr}")
            # Enforcement autenticazione globale
            if endpoint and not endpoint.startswith('static') and endpoint not in PUBLIC_ENDPOINTS:
                if not current_user.is_authenticated:
                    logger.warning(f"Tentativo accesso non autenticato a {endpoint} da IP {request.remote_addr}")

                    # Per le API, restituisci JSON 401 invece di redirect
                    if endpoint and endpoint.startswith('api.'):
                        from flask import jsonify
                        return jsonify({
                            'success': False,
                            'message': 'Autenticazione richiesta'
                        }), 401

                    # Per le pagine web, fai redirect
                    flash('Devi essere autenticato per accedere a questa pagina.', 'warning')
                    return redirect(url_for('auth.login', next=request.url))

        @app.errorhandler(403)
        def forbidden(e):
            user = getattr(current_user, 'username', 'anonymous')
            logger.warning(f"403 Forbidden: user={user}, endpoint={request.endpoint}, ip={request.remote_addr}")
            flash('Accesso negato: non hai i permessi necessari.', 'danger')
            return redirect(url_for('dashboard.index'))

        # Registra i filtri personalizzati
        from utils.filters import register_filters
        register_filters(app)

    return app

# Rimosso:
# app = Flask(__name__)
# app.secret_key = os.environ.get("SESSION_SECRET", os.urandom(24))
# app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)
# app.config.from_object('config.Config')
# db.init_app(app)
# login_manager.init_app(app)
# login_manager.login_view = 'auth.login'
# migrate.init_app(app, db)
# ... e tutta la logica di registrazione blueprint e user_loader che è ora in create_app()
