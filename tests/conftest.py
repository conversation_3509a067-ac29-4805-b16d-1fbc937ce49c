import pytest
import sys
import os
from datetime import datetime, timedelta
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__) + '/../'))
from app import create_app
from extensions import db
from models import User, Project, Client, Task, KPI, ProjectKPI, Department, Skill, UserSkill, UserProfile
# Importa altri modelli se necessario per i test

@pytest.fixture(scope='session')
def app():
    """Crea e configura una nuova istanza dell'app per ogni sessione di test."""
    app_instance = create_app(config_overrides={
        'TESTING': True,
        'SQLALCHEMY_DATABASE_URI': 'sqlite:///:memory:',
        'WTF_CSRF_ENABLED': False,
        'LOGIN_DISABLED': False,
        'SERVER_NAME': 'localhost.localdomain',
        'PASSWORD_RESET_TOKEN_EXPIRATION_SECONDS': 60 # Valore basso per testare la scadenza
    })

    with app_instance.app_context():
        db.create_all()
        yield app_instance
        db.session.remove()
        db.drop_all()

@pytest.fixture(scope='function')
def client(app):
    """Un client di test Flask per l'app."""
    return app.test_client()

@pytest.fixture(scope='function')
def runner(app):
    """Un test runner per i comandi CLI dell'app."""
    return app.test_cli_runner()

@pytest.fixture(scope='function')
def init_database(app):
    """Pulisce i dati dalla tabella User prima di ogni test per evitare conflitti di unicità."""
    with app.app_context():
        # Importa User qui per evitare import circolari e assicurarti che sia nel contesto app
        from models import User
        # Cancella tutti gli utenti
        # Questo è un approccio semplice. Per app più complesse potresti dover
        # gestire l'ordine di cancellazione o disabilitare temporaneamente i vincoli FK.
        db.session.query(User).delete()
        db.session.commit()
    # Il resto della creazione/distruzione del DB è gestito dalla fixture 'app' session-scoped.

@pytest.fixture(scope='function')
def new_user_data():
    """Dati per un nuovo utente di test."""
    return {
        'username': 'testuser',
        'email': '<EMAIL>',
        'password': 'testpassword',
        'first_name': 'Test',
        'last_name': 'User'
    }

@pytest.fixture(scope='function')
def created_user(app, new_user_data, init_database):
    """Fixture per creare e salvare un utente di test nel db. Restituisce l'ID dell'utente."""
    with app.app_context():
        user = User(
            username=new_user_data['username'],
            email=new_user_data['email'],
            first_name=new_user_data['first_name'],
            last_name=new_user_data['last_name'],
            is_active=True
        )
        user.set_password(new_user_data['password'])
        db.session.add(user)
        db.session.commit()
        user_id = user.id # Salva l'ID prima che la sessione potenziale venga chiusa
        return user_id

@pytest.fixture(scope='function')
def test_user_id(created_user):
    """Alias per created_user, per compatibilità con i test esistenti."""
    return created_user

@pytest.fixture(scope='function')
def logged_in_client(client, app, created_user_id, new_user_data): # Rinominato created_user in created_user_id
    """Un client di test Flask con un utente loggato."""
    with app.app_context(): # Assicurati di essere in un contesto app per query DB
        user = db.session.get(User, created_user_id) # Recupera l'utente dal DB
        if user:
            client.post('/auth/login', data=dict(
                username=user.username, # Usa l'username dell'oggetto recuperato
                password=new_user_data['password']
            ), follow_redirects=True)
        else:
            pytest.fail(f"User with id {created_user_id} not found for login in fixture.")
    return client

@pytest.fixture(scope='function')
def db_session(app):
    """Restituisce la sessione del db per i test che la richiedono."""
    from extensions import db
    yield db.session
    db.session.rollback()

@pytest.fixture(scope='function')
def test_user(app, created_user): # Usa created_user invece di created_user_id
    """Alias per compatibilità con i test che richiedono test_user."""
    with app.app_context():
        user = db.session.get(User, created_user)
        if not user:
            pytest.fail(f"test_user (id: {created_user}) not found in fixture.")
        return user

@pytest.fixture(scope='function')
def admin_user(app, init_database):
    """Crea e restituisce un utente admin per i test. Restituisce l'ID dell'utente."""
    from models import User # Assicurati che User sia importato
    with app.app_context():
        admin = User(
            username='adminuser',
            email='<EMAIL>',
            first_name='Admin',
            last_name='User',
            is_active=True,
            role='admin'
        )
        admin.set_password('adminpassword')
        # from app import db # db dovrebbe essere già importato a livello di modulo
        db.session.add(admin)
        db.session.commit()
        admin_id = admin.id # Salva l'ID
        return admin_id

@pytest.fixture(scope='function')
def admin_user_id(admin_user):
    """Alias per admin_user."""
    return admin_user

@pytest.fixture(scope='function')
def auth(client, new_user_data, created_user, sample_users):
    """Helper per autenticare un utente nei test."""
    class AuthActions:
        def login(self, username=None, password=None):
            # Se non specificato, usa l'utente admin di default per i test API
            default_username = 'admin' if sample_users else new_user_data['username']
            default_password = 'password' if sample_users else new_user_data['password']

            return client.post('/auth/login', data={
                'username': username or default_username,
                'password': password or default_password
            }, follow_redirects=True)

        def logout(self):
            return client.get('/auth/logout', follow_redirects=True)

    return AuthActions()

@pytest.fixture(scope='function')
def test_client(app, init_database):
    """Fixture per creare un client di test nel db."""
    with app.app_context():
        client = Client(
            name='Test Client',
            industry='Technology',
            description='A test client for API testing',
            website='https://example.com'
        )
        db.session.add(client)
        db.session.commit()
        client_id = client.id
        return client_id

@pytest.fixture(scope='function')
def test_project(app, init_database, test_client, test_user):
    """Fixture per creare un progetto di test nel db."""
    with app.app_context():
        project = Project(
            name='Test Project',
            description='A test project for API testing',
            client_id=test_client,  # test_client ora è un ID
            start_date=datetime.utcnow().date(),
            end_date=(datetime.utcnow() + timedelta(days=30)).date(),
            status='active',
            budget=10000.0,
            expenses=2000.0
        )
        db.session.add(project)
        db.session.commit()
        project_id = project.id
        return project_id

@pytest.fixture(scope='function')
def test_task(app, init_database, test_project, test_user):
    """Fixture per creare un task di test nel db."""
    with app.app_context():
        task = Task(
            name='Test Task',
            description='A test task for API testing',
            project_id=test_project.id,
            assignee_id=test_user.id,
            status='todo',
            priority='medium',
            due_date=(datetime.utcnow() + timedelta(days=7)).date()
        )
        db.session.add(task)
        db.session.commit()
        return task

@pytest.fixture(scope='function')
def test_kpi(app, init_database):
    """Fixture per creare un KPI di test nel db."""
    with app.app_context():
        kpi = KPI(
            name='Test KPI',
            description='A test KPI for API testing',
            category='Test',
            target_value=100.0,
            current_value=50.0,
            unit='%',
            frequency='monthly'
        )
        db.session.add(kpi)
        db.session.commit()
        kpi_id = kpi.id
        return kpi_id

@pytest.fixture(scope='function')
def test_project_kpi(app, init_database, test_project, test_kpi):
    """Fixture per creare un ProjectKPI di test nel db."""
    with app.app_context():
        project_kpi = ProjectKPI(
            project_id=test_project,  # test_project ora è un ID
            kpi_id=test_kpi,  # test_kpi ora è un ID
            target_value=200.0,
            current_value=100.0
        )
        db.session.add(project_kpi)
        db.session.commit()
        project_kpi_id = project_kpi.id
        return project_kpi_id

@pytest.fixture(scope='function')
def admin_auth(client, admin_user):
    """Helper per autenticare un admin nei test."""
    class AdminAuthActions:
        def login(self):
            # Prima fai logout se c'è una sessione attiva
            client.get('/auth/logout', follow_redirects=True)
            # Poi fai login come admin
            return client.post('/auth/login', data={
                'username': 'adminuser',
                'password': 'adminpassword'
            }, follow_redirects=True)

        def logout(self):
            return client.get('/auth/logout', follow_redirects=True)

    return AdminAuthActions()

# Personnel-related fixtures

@pytest.fixture(scope='function')
def sample_departments(app, init_database):
    """Fixture per creare dipartimenti di test nel db."""
    with app.app_context():
        # Check if departments already exist
        dept_names = ['Engineering', 'Marketing', 'Human Resources', 'Sales']
        departments = []

        for name in dept_names:
            dept = Department.query.filter_by(name=name).first()
            if not dept:
                if name == 'Engineering':
                    dept = Department(name=name, description='Software development and engineering')
                elif name == 'Marketing':
                    dept = Department(name=name, description='Marketing and communications')
                elif name == 'Human Resources':
                    dept = Department(name=name, description='HR and people operations')
                elif name == 'Sales':
                    dept = Department(name=name, description='Sales and business development')
                db.session.add(dept)
            departments.append(dept)

        db.session.commit()
        return departments

@pytest.fixture(scope='function')
def sample_skills(app, init_database):
    """Fixture per creare competenze di test nel db."""
    with app.app_context():
        # Check if skills already exist
        skill_data = [
            ('Python', 'Programming', 'Python programming language'),
            ('JavaScript', 'Programming', 'JavaScript programming language'),
            ('Project Management', 'Management', 'Project planning and execution'),
            ('Digital Marketing', 'Marketing', 'Online marketing and social media'),
            ('Data Analysis', 'Analytics', 'Data analysis and visualization')
        ]

        skills = []
        for name, category, description in skill_data:
            skill = Skill.query.filter_by(name=name).first()
            if not skill:
                skill = Skill(name=name, category=category, description=description)
                db.session.add(skill)
            skills.append(skill)

        db.session.commit()
        return skills

@pytest.fixture(scope='function')
def sample_users(app, init_database):
    """Fixture per creare utenti di test nel db con dipartimenti."""
    with app.app_context():
        # Create departments first (check if they exist)
        dept_names = ['Engineering', 'Marketing', 'Human Resources', 'Sales']
        departments = []

        for name in dept_names:
            dept = Department.query.filter_by(name=name).first()
            if not dept:
                if name == 'Engineering':
                    dept = Department(name=name, description='Software development and engineering')
                elif name == 'Marketing':
                    dept = Department(name=name, description='Marketing and communications')
                elif name == 'Human Resources':
                    dept = Department(name=name, description='HR and people operations')
                elif name == 'Sales':
                    dept = Department(name=name, description='Sales and business development')
                db.session.add(dept)
            departments.append(dept)

        db.session.flush()  # Get IDs without committing

        users = [
            User(
                username='admin',
                email='<EMAIL>',
                first_name='Admin',
                last_name='User',
                role='admin',
                department_id=departments[0].id,  # Engineering
                position='System Administrator',
                hire_date=datetime.utcnow().date() - timedelta(days=365),
                phone='+1234567890',
                is_active=True
            ),
            User(
                username='manager',
                email='<EMAIL>',
                first_name='Manager',
                last_name='User',
                role='manager',
                department_id=departments[0].id,  # Engineering
                position='Engineering Manager',
                hire_date=datetime.utcnow().date() - timedelta(days=200),
                phone='+1234567891',
                is_active=True
            ),
            User(
                username='employee',
                email='<EMAIL>',
                first_name='Employee',
                last_name='User',
                role='employee',
                department_id=departments[1].id,  # Marketing
                position='Marketing Specialist',
                hire_date=datetime.utcnow().date() - timedelta(days=100),
                phone='+1234567892',
                is_active=True
            ),
            User(
                username='hr_user',
                email='<EMAIL>',
                first_name='HR',
                last_name='User',
                role='human_resources',
                department_id=departments[2].id,  # HR
                position='HR Specialist',
                hire_date=datetime.utcnow().date() - timedelta(days=150),
                phone='+1234567893',
                is_active=True
            )
        ]

        for user in users:
            user.set_password('password')
            db.session.add(user)

        db.session.commit()
        return users

@pytest.fixture(scope='function')
def sample_user_skills(app, sample_users, sample_skills):
    """Fixture per creare competenze utente di test nel db."""
    with app.app_context():
        # Get fresh instances from database
        users = User.query.order_by(User.id).all()
        skills = Skill.query.order_by(Skill.id).all()

        if len(users) >= 3 and len(skills) >= 4:
            user_skills = [
                UserSkill(
                    user_id=users[0].id,  # Admin
                    skill_id=skills[0].id,  # Python
                    proficiency_level=5,
                    years_experience=5,
                    is_certified=True
                ),
                UserSkill(
                    user_id=users[0].id,  # Admin
                    skill_id=skills[2].id,  # Project Management
                    proficiency_level=4,
                    years_experience=3,
                    is_certified=False
                ),
                UserSkill(
                    user_id=users[1].id,  # Manager
                    skill_id=skills[1].id,  # JavaScript
                    proficiency_level=4,
                    years_experience=4,
                    is_certified=True
                ),
                UserSkill(
                    user_id=users[2].id,  # Employee
                    skill_id=skills[3].id,  # Digital Marketing
                    proficiency_level=3,
                    years_experience=2,
                    is_certified=False
                )
            ]

            for user_skill in user_skills:
                db.session.add(user_skill)

            db.session.commit()
            return user_skills

        return []

@pytest.fixture(scope='function')
def sample_user_profiles(app, sample_users):
    """Fixture per creare profili utente di test nel db."""
    with app.app_context():
        # Get fresh instances from database
        users = User.query.order_by(User.id).all()

        if len(users) >= 2:
            profiles = [
                UserProfile(
                    user_id=users[0].id,  # Admin
                    emergency_contact_name='Emergency Contact 1',
                    emergency_contact_phone='+1234567800',
                    address='123 Admin Street',
                    profile_completion=95.0,
                    notes='Admin user profile'
                ),
                UserProfile(
                    user_id=users[1].id,  # Manager
                    emergency_contact_name='Emergency Contact 2',
                    emergency_contact_phone='+1234567801',
                    address='456 Manager Avenue',
                    profile_completion=80.0,
                    notes='Manager user profile'
                )
            ]

            for profile in profiles:
                db.session.add(profile)

            db.session.commit()
            return profiles

        return []