"""
Test suite for Auth API endpoints.
"""
import json
import pytest
from datetime import datetime


class TestAuthAPI:
    """Test authentication API endpoints."""

    def test_get_current_user_success(self, client, auth, sample_users):
        """Test successful current user retrieval."""
        auth.login()
        
        response = client.get('/api/auth/me')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert 'user' in data['data']
        
        user = data['data']['user']
        assert 'id' in user
        assert 'username' in user
        assert 'email' in user
        assert 'full_name' in user
        assert 'role' in user
        assert 'permissions' in user
        assert 'profile_completion' in user
        assert 'preferences' in user
        
        # Check permissions structure
        assert isinstance(user['permissions'], list)
        
        # Check preferences structure
        preferences = user['preferences']
        assert 'dark_mode' in preferences
        assert 'language' in preferences
        assert 'timezone' in preferences

    def test_get_current_user_unauthorized(self, client, auth):
        """Test current user access without authentication."""
        auth.logout()
        
        response = client.get('/api/auth/me')
        assert response.status_code == 401

    def test_check_session_success(self, client, auth, sample_users):
        """Test successful session check."""
        auth.login()
        
        response = client.get('/api/auth/check-session')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        
        session_data = data['data']
        assert session_data['valid'] is True
        assert 'user_id' in session_data
        assert 'username' in session_data
        assert 'role' in session_data
        assert 'last_activity' in session_data

    def test_check_session_unauthorized(self, client, auth):
        """Test session check without authentication."""
        auth.logout()
        
        response = client.get('/api/auth/check-session')
        assert response.status_code == 401

    def test_get_preferences_success(self, client, auth, sample_users):
        """Test successful preferences retrieval."""
        auth.login()
        
        response = client.get('/api/auth/preferences')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert 'preferences' in data['data']
        
        preferences = data['data']['preferences']
        assert 'dark_mode' in preferences
        assert 'language' in preferences
        assert 'timezone' in preferences
        assert 'notifications' in preferences

    def test_update_preferences_success(self, client, auth, sample_users):
        """Test successful preferences update."""
        auth.login()
        
        update_data = {
            'dark_mode': True
        }
        
        response = client.put(
            '/api/auth/preferences',
            data=json.dumps(update_data),
            content_type='application/json'
        )
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert 'preferences' in data['data']
        
        preferences = data['data']['preferences']
        assert preferences['dark_mode'] is True

    def test_update_preferences_invalid_data(self, client, auth, sample_users):
        """Test preferences update with invalid data."""
        auth.login()
        
        response = client.put(
            '/api/auth/preferences',
            data='',
            content_type='application/json'
        )
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['success'] is False

    def test_update_preferences_unauthorized(self, client, auth):
        """Test preferences update without authentication."""
        auth.logout()
        
        update_data = {
            'dark_mode': True
        }
        
        response = client.put(
            '/api/auth/preferences',
            data=json.dumps(update_data),
            content_type='application/json'
        )
        assert response.status_code == 401

    def test_update_profile_success(self, client, auth, sample_users):
        """Test successful profile update."""
        auth.login()
        
        update_data = {
            'first_name': 'Mario',
            'last_name': 'Rossi',
            'phone': '+39 ************',
            'bio': 'Software Developer',
            'address': 'Via Roma 123, Milano',
            'emergency_contact_name': 'Giulia Rossi',
            'emergency_contact_phone': '+39 ************'
        }
        
        response = client.put(
            '/api/auth/profile',
            data=json.dumps(update_data),
            content_type='application/json'
        )
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert 'user' in data['data']
        
        user = data['data']['user']
        assert user['first_name'] == 'Mario'
        assert user['last_name'] == 'Rossi'
        assert user['full_name'] == 'Mario Rossi'
        assert user['phone'] == '+39 ************'
        assert user['bio'] == 'Software Developer'

    def test_update_profile_partial_data(self, client, auth, sample_users):
        """Test profile update with partial data."""
        auth.login()
        
        update_data = {
            'first_name': 'Giovanni'
        }
        
        response = client.put(
            '/api/auth/profile',
            data=json.dumps(update_data),
            content_type='application/json'
        )
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['data']['user']['first_name'] == 'Giovanni'

    def test_update_profile_invalid_data(self, client, auth, sample_users):
        """Test profile update with invalid data."""
        auth.login()
        
        response = client.put(
            '/api/auth/profile',
            data='',
            content_type='application/json'
        )
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['success'] is False

    def test_update_profile_unauthorized(self, client, auth):
        """Test profile update without authentication."""
        auth.logout()
        
        update_data = {
            'first_name': 'Mario'
        }
        
        response = client.put(
            '/api/auth/profile',
            data=json.dumps(update_data),
            content_type='application/json'
        )
        assert response.status_code == 401


class TestUserPermissions:
    """Test user permissions functionality."""

    def test_admin_permissions(self, client, auth, sample_users):
        """Test admin user has all permissions."""
        # Login as admin (assuming first user is admin)
        auth.login()
        
        response = client.get('/api/auth/me')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        user = data['data']['user']
        
        if user['role'] == 'admin':
            permissions = user['permissions']
            assert len(permissions) > 0
            # Admin should have admin permission
            assert 'admin' in permissions

    def test_employee_permissions(self, client, auth, sample_users):
        """Test employee user has limited permissions."""
        auth.login()
        
        response = client.get('/api/auth/me')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        user = data['data']['user']
        
        if user['role'] == 'employee':
            permissions = user['permissions']
            # Employee should have basic permissions
            assert 'view_dashboard' in permissions
            assert 'view_own_timesheets' in permissions
            assert 'submit_timesheet' in permissions
            # Employee should not have admin permissions
            assert 'admin' not in permissions


class TestProfileCompletion:
    """Test profile completion calculation."""

    def test_profile_completion_calculation(self, client, auth, sample_users):
        """Test profile completion percentage calculation."""
        auth.login()
        
        response = client.get('/api/auth/me')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        user = data['data']['user']
        
        # Profile completion should be a number between 0 and 100
        assert 'profile_completion' in user
        assert isinstance(user['profile_completion'], (int, float))
        assert 0 <= user['profile_completion'] <= 100
