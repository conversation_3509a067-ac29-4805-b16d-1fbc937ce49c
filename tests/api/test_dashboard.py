"""
Test suite for Dashboard API endpoints.
"""
import json
import pytest
from datetime import datetime, timedelta


class TestDashboardStatsAPI:
    """Test dashboard statistics API endpoints."""

    def test_get_dashboard_stats_success(self, client, auth, sample_users):
        """Test successful dashboard stats retrieval."""
        auth.login()
        
        response = client.get('/api/dashboard/stats')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        
        stats = data['data']
        assert 'projects' in stats
        assert 'tasks' in stats
        assert 'team' in stats
        assert 'activities' in stats
        
        # Check projects stats structure
        assert 'active' in stats['projects']
        assert 'total' in stats['projects']
        
        # Check tasks stats structure
        assert 'total' in stats['tasks']
        assert 'pending' in stats['tasks']
        assert 'completed' in stats['tasks']
        assert 'overdue' in stats['tasks']

    def test_get_dashboard_stats_unauthorized(self, client, auth):
        """Test dashboard stats access without authentication."""
        auth.logout()
        
        response = client.get('/api/dashboard/stats')
        assert response.status_code == 401

    def test_get_recent_activities_success(self, client, auth, sample_users):
        """Test successful recent activities retrieval."""
        auth.login()
        
        response = client.get('/api/dashboard/recent-activities')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert 'activities' in data['data']
        
        activities = data['data']['activities']
        assert isinstance(activities, list)
        
        # Check activity structure if any exist
        if activities:
            activity = activities[0]
            assert 'type' in activity
            assert 'id' in activity
            assert 'title' in activity
            assert 'timestamp' in activity

    def test_get_recent_activities_with_limit(self, client, auth, sample_users):
        """Test recent activities with custom limit."""
        auth.login()
        
        response = client.get('/api/dashboard/recent-activities?limit=5')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        activities = data['data']['activities']
        assert len(activities) <= 5

    def test_get_upcoming_tasks_success(self, client, auth, sample_users):
        """Test successful upcoming tasks retrieval."""
        auth.login()
        
        response = client.get('/api/dashboard/upcoming-tasks')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert 'tasks' in data['data']
        
        tasks = data['data']['tasks']
        assert isinstance(tasks, list)
        
        # Check task structure if any exist
        if tasks:
            task = tasks[0]
            assert 'id' in task
            assert 'name' in task
            assert 'project_name' in task
            assert 'due_date' in task
            assert 'days_until_due' in task

    def test_get_upcoming_tasks_with_params(self, client, auth, sample_users):
        """Test upcoming tasks with custom parameters."""
        auth.login()
        
        response = client.get('/api/dashboard/upcoming-tasks?days=14&limit=3')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        tasks = data['data']['tasks']
        assert len(tasks) <= 3


class TestDashboardKPIsAPI:
    """Test dashboard KPIs API endpoints."""

    def test_get_dashboard_kpis_success(self, client, auth, sample_users):
        """Test successful KPIs retrieval."""
        auth.login()
        
        response = client.get('/api/dashboard/kpis')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert 'kpis' in data['data']
        
        kpis = data['data']['kpis']
        assert isinstance(kpis, list)
        
        # Check KPI structure if any exist
        if kpis:
            kpi = kpis[0]
            assert 'id' in kpi
            assert 'name' in kpi
            assert 'category' in kpi
            assert 'current_value' in kpi
            assert 'target_value' in kpi
            assert 'performance_percentage' in kpi

    def test_get_dashboard_kpis_with_category(self, client, auth, sample_users):
        """Test KPIs retrieval with category filter."""
        auth.login()
        
        response = client.get('/api/dashboard/kpis?category=performance')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True

    def test_get_dashboard_kpis_unauthorized(self, client, auth):
        """Test KPIs access without authentication."""
        auth.logout()
        
        response = client.get('/api/dashboard/kpis')
        assert response.status_code == 401


class TestDashboardChartsAPI:
    """Test dashboard charts API endpoints."""

    def test_get_project_status_chart_success(self, client, auth, sample_users):
        """Test successful project status chart data retrieval."""
        auth.login()
        
        response = client.get('/api/dashboard/charts/project-status')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert 'chart' in data['data']
        
        chart = data['data']['chart']
        assert 'labels' in chart
        assert 'data' in chart
        assert 'total' in chart
        assert isinstance(chart['labels'], list)
        assert isinstance(chart['data'], list)

    def test_get_task_status_chart_success(self, client, auth, sample_users):
        """Test successful task status chart data retrieval."""
        auth.login()
        
        response = client.get('/api/dashboard/charts/task-status')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert 'chart' in data['data']
        
        chart = data['data']['chart']
        assert 'labels' in chart
        assert 'data' in chart
        assert 'total' in chart

    def test_get_charts_unauthorized(self, client, auth):
        """Test charts access without authentication."""
        auth.logout()
        
        response = client.get('/api/dashboard/charts/project-status')
        assert response.status_code == 401
        
        response = client.get('/api/dashboard/charts/task-status')
        assert response.status_code == 401


class TestDashboardQuickActionsAPI:
    """Test dashboard quick actions API endpoints."""

    def test_get_quick_actions_success(self, client, auth, sample_users):
        """Test successful quick actions retrieval."""
        auth.login()
        
        response = client.get('/api/dashboard/quick-actions')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert 'actions' in data['data']
        
        actions = data['data']['actions']
        assert isinstance(actions, list)
        assert len(actions) > 0  # Should have at least basic actions
        
        # Check action structure
        action = actions[0]
        assert 'id' in action
        assert 'title' in action
        assert 'description' in action
        assert 'icon' in action
        assert 'url' in action
        assert 'category' in action

    def test_get_quick_actions_unauthorized(self, client, auth):
        """Test quick actions access without authentication."""
        auth.logout()
        
        response = client.get('/api/dashboard/quick-actions')
        assert response.status_code == 401


class TestDashboardNewsAPI:
    """Test dashboard news API endpoints."""

    def test_get_dashboard_news_success(self, client, auth, sample_users):
        """Test successful news retrieval."""
        auth.login()
        
        response = client.get('/api/dashboard/news')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert 'news' in data['data']
        
        news = data['data']['news']
        assert isinstance(news, list)
        
        # Check news structure if any exist
        if news:
            news_item = news[0]
            assert 'id' in news_item
            assert 'title' in news_item
            assert 'content' in news_item
            assert 'author_name' in news_item
            assert 'created_at' in news_item

    def test_get_dashboard_news_with_limit(self, client, auth, sample_users):
        """Test news retrieval with custom limit."""
        auth.login()
        
        response = client.get('/api/dashboard/news?limit=3')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        news = data['data']['news']
        assert len(news) <= 3

    def test_get_dashboard_news_unauthorized(self, client, auth):
        """Test news access without authentication."""
        auth.logout()
        
        response = client.get('/api/dashboard/news')
        assert response.status_code == 401
