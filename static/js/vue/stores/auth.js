/**
 * Auth Store - Pinia Store per gestione autenticazione
 * Gestisce stato utente, permessi e sessione
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  // === STATE ===
  const user = ref(null)
  const isAuthenticated = ref(false)
  const permissions = ref([])
  const sessionValid = ref(true)
  const lastActivity = ref(null)

  // === COMPUTED ===
  const userFullName = computed(() => {
    if (!user.value) return ''
    return user.value.full_name || `${user.value.first_name || ''} ${user.value.last_name || ''}`.trim()
  })

  const userInitials = computed(() => {
    if (!user.value) return ''
    const firstName = user.value.first_name || ''
    const lastName = user.value.last_name || ''
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
  })

  const userRole = computed(() => user.value?.role || 'guest')

  const isAdmin = computed(() => userRole.value === 'admin')
  const isManager = computed(() => ['admin', 'manager'].includes(userRole.value))
  const isHR = computed(() => ['admin', 'human_resources'].includes(userRole.value))

  // === ACTIONS ===

  /**
   * Carica i dati dell'utente corrente
   */
  async function loadCurrentUser() {
    try {
      const response = await fetch('/api/auth/me', {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        user.value = data.data.user
        permissions.value = data.data.user.permissions || []
        isAuthenticated.value = true
        lastActivity.value = new Date().toISOString()
        
        console.log('✅ User loaded:', userFullName.value)
        return true
      } else {
        throw new Error('Failed to load user data')
      }
    } catch (error) {
      console.error('Failed to load current user:', error)
      logout()
      return false
    }
  }

  /**
   * Verifica se l'utente ha uno o più permessi
   * @param {string|Array} requiredPermissions - Permesso/i richiesti
   * @returns {boolean}
   */
  function hasPermission(requiredPermissions) {
    if (!isAuthenticated.value) return false
    
    const required = Array.isArray(requiredPermissions) 
      ? requiredPermissions 
      : [requiredPermissions]
    
    return required.some(permission => permissions.value.includes(permission))
  }

  /**
   * Verifica se l'utente ha tutti i permessi richiesti
   * @param {Array} requiredPermissions - Array di permessi richiesti
   * @returns {boolean}
   */
  function hasAllPermissions(requiredPermissions) {
    if (!isAuthenticated.value) return false
    
    return requiredPermissions.every(permission => 
      permissions.value.includes(permission)
    )
  }

  /**
   * Verifica se l'utente ha almeno uno dei permessi richiesti
   * @param {Array} requiredPermissions - Array di permessi richiesti
   * @returns {boolean}
   */
  function hasAnyPermission(requiredPermissions) {
    if (!isAuthenticated.value) return false
    
    return requiredPermissions.some(permission => 
      permissions.value.includes(permission)
    )
  }

  /**
   * Verifica la validità della sessione
   */
  async function checkSession() {
    try {
      const response = await fetch('/api/auth/check-session', {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        sessionValid.value = data.data.valid
        lastActivity.value = data.data.last_activity
        return true
      } else {
        sessionValid.value = false
        return false
      }
    } catch (error) {
      console.error('Session check failed:', error)
      sessionValid.value = false
      return false
    }
  }

  /**
   * Aggiorna le preferenze utente
   * @param {Object} preferences - Nuove preferenze
   */
  async function updatePreferences(preferences) {
    try {
      const response = await fetch('/api/auth/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': window.APP_CONFIG.csrfToken
        },
        credentials: 'include',
        body: JSON.stringify(preferences)
      })

      if (response.ok) {
        const data = await response.json()
        
        // Aggiorna i dati utente locali
        if (user.value) {
          user.value.preferences = { ...user.value.preferences, ...preferences }
        }
        
        return data.data.preferences
      } else {
        throw new Error('Failed to update preferences')
      }
    } catch (error) {
      console.error('Failed to update preferences:', error)
      throw error
    }
  }

  /**
   * Aggiorna il profilo utente
   * @param {Object} profileData - Dati del profilo da aggiornare
   */
  async function updateProfile(profileData) {
    try {
      const response = await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': window.APP_CONFIG.csrfToken
        },
        credentials: 'include',
        body: JSON.stringify(profileData)
      })

      if (response.ok) {
        const data = await response.json()
        
        // Aggiorna i dati utente locali
        if (user.value) {
          Object.assign(user.value, data.data.user)
        }
        
        return data.data.user
      } else {
        throw new Error('Failed to update profile')
      }
    } catch (error) {
      console.error('Failed to update profile:', error)
      throw error
    }
  }

  /**
   * Logout dell'utente
   */
  function logout() {
    user.value = null
    isAuthenticated.value = false
    permissions.value = []
    sessionValid.value = false
    lastActivity.value = null
    
    // Redirect to login page
    window.location.href = '/auth/logout'
  }

  /**
   * Inizializza lo store con i dati dell'utente se autenticato
   */
  function initialize() {
    // Se l'utente è già autenticato (da configurazione globale)
    if (window.APP_CONFIG.isAuthenticated && window.APP_CONFIG.user) {
      user.value = window.APP_CONFIG.user
      isAuthenticated.value = true
      permissions.value = window.APP_CONFIG.user.permissions || []
      lastActivity.value = new Date().toISOString()
    }
  }

  /**
   * Aggiorna l'ultima attività
   */
  function updateLastActivity() {
    lastActivity.value = new Date().toISOString()
  }

  /**
   * Verifica se l'utente può accedere a una risorsa
   * @param {string} resource - Nome della risorsa
   * @param {string} action - Azione da verificare (view, create, edit, delete)
   * @returns {boolean}
   */
  function canAccess(resource, action = 'view') {
    if (!isAuthenticated.value) return false
    
    // Admin può fare tutto
    if (isAdmin.value) return true
    
    // Costruisci il nome del permesso
    const permissionName = `${action}_${resource}`
    
    return hasPermission(permissionName)
  }

  // Inizializza lo store
  initialize()

  return {
    // State
    user,
    isAuthenticated,
    permissions,
    sessionValid,
    lastActivity,

    // Computed
    userFullName,
    userInitials,
    userRole,
    isAdmin,
    isManager,
    isHR,

    // Actions
    loadCurrentUser,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    checkSession,
    updatePreferences,
    updateProfile,
    logout,
    initialize,
    updateLastActivity,
    canAccess
  }
})
