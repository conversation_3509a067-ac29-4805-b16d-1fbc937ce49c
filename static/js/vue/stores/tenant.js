/**
 * Tenant Store - Pinia Store per configurazione tenant
 * Gestisce tutti i contenuti personalizzabili per il tenant
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useTenantStore = defineStore('tenant', () => {
  // === STATE ===
  const config = ref({})
  const isLoaded = ref(false)
  
  // === COMPUTED ===
  
  // Company info
  const company = computed(() => config.value.company || {})
  const contact = computed(() => config.value.contact || {})
  const pages = computed(() => config.value.pages || {})
  const routes = computed(() => config.value.routes || {})
  const navigation = computed(() => config.value.navigation || {})
  const footer = computed(() => config.value.footer || {})
  
  // Helper per interpolazione variabili
  const interpolate = (text, extraVars = {}) => {
    if (!text) return ''
    
    const vars = {
      'company.name': company.value.name || 'DatVinci',
      'company.tagline': company.value.tagline || 'Innovazione per il futuro',
      'company.description': company.value.description || '',
      'company.mission': company.value.mission || '',
      'company.vision': company.value.vision || '',
      'company.founded': company.value.founded || '2018',
      'company.team_size': company.value.team_size || '25+ professionisti',
      'contact.email': contact.value.email || '<EMAIL>',
      'contact.phone': contact.value.phone || '+39 02 1234567',
      'contact.address': contact.value.address || '',
      'current_year': new Date().getFullYear(),
      ...extraVars
    }
    
    let result = text
    Object.entries(vars).forEach(([key, value]) => {
      const regex = new RegExp(`\\{${key}\\}`, 'g')
      result = result.replace(regex, value)
    })
    
    return result
  }
  
  // Route titles con interpolazione
  const getRouteTitle = (routeName) => {
    const routeConfig = routes.value[routeName]
    if (!routeConfig?.title) return company.value.name || 'DatVinci'
    
    return interpolate(routeConfig.title)
  }
  
  const getRouteMetaDescription = (routeName) => {
    const routeConfig = routes.value[routeName]
    if (!routeConfig?.meta_description) return company.value.description || ''
    
    return interpolate(routeConfig.meta_description)
  }
  
  // Page content con interpolazione
  const getPageContent = (pageName, sectionName = null, key = null) => {
    const pageConfig = pages.value[pageName]
    if (!pageConfig) return ''
    
    let content = pageConfig
    if (sectionName) {
      content = content[sectionName]
      if (!content) return ''
    }
    
    if (key) {
      content = content[key]
      if (!content) return ''
    }
    
    if (typeof content === 'string') {
      return interpolate(content)
    }
    
    return content
  }
  
  // Navigation labels
  const getNavigationLabel = (key) => {
    return navigation.value[key] || key
  }
  
  // Footer content
  const getFooterContent = (key) => {
    const content = footer.value[key]
    if (!content) return ''
    
    return interpolate(content)
  }
  
  // === ACTIONS ===
  
  /**
   * Carica la configurazione del tenant
   */
  async function loadConfig() {
    try {
      const response = await fetch('/api/public/config')
      if (response.ok) {
        const data = await response.json()
        config.value = data.data
        isLoaded.value = true
        
        console.log('✅ Tenant configuration loaded')
        return true
      } else {
        throw new Error('Failed to load tenant configuration')
      }
    } catch (error) {
      console.error('Failed to load tenant configuration:', error)
      
      // Fallback configuration
      config.value = {
        company: {
          name: 'DatVinci',
          tagline: 'Innovazione per il futuro',
          description: 'Supportiamo le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all\'avanguardia.'
        },
        contact: {
          email: '<EMAIL>',
          phone: '+39 02 1234567',
          address: 'Via dell\'Innovazione 123, Milano'
        },
        routes: {},
        pages: {},
        navigation: {},
        footer: {}
      }
      isLoaded.value = true
      return false
    }
  }
  
  /**
   * Aggiorna la configurazione del tenant (solo admin)
   */
  async function updateConfig(newConfig) {
    try {
      const response = await fetch('/api/admin/tenant/config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': window.APP_CONFIG.csrfToken
        },
        body: JSON.stringify(newConfig)
      })
      
      if (response.ok) {
        const data = await response.json()
        config.value = data.data
        
        console.log('✅ Tenant configuration updated')
        return true
      } else {
        throw new Error('Failed to update tenant configuration')
      }
    } catch (error) {
      console.error('Failed to update tenant configuration:', error)
      throw error
    }
  }
  
  /**
   * Reset alla configurazione di default
   */
  async function resetConfig() {
    try {
      const response = await fetch('/api/admin/tenant/config/reset', {
        method: 'POST',
        headers: {
          'X-CSRFToken': window.APP_CONFIG.csrfToken
        }
      })
      
      if (response.ok) {
        await loadConfig()
        console.log('✅ Tenant configuration reset to default')
        return true
      } else {
        throw new Error('Failed to reset tenant configuration')
      }
    } catch (error) {
      console.error('Failed to reset tenant configuration:', error)
      throw error
    }
  }
  
  return {
    // State
    config,
    isLoaded,
    
    // Computed
    company,
    contact,
    pages,
    routes,
    navigation,
    footer,
    
    // Methods
    interpolate,
    getRouteTitle,
    getRouteMetaDescription,
    getPageContent,
    getNavigationLabel,
    getFooterContent,
    loadConfig,
    updateConfig,
    resetConfig
  }
})
