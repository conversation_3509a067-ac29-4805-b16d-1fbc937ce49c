/**
 * App Store - Pinia Store per stato globale dell'applicazione
 * Gestisce loading, notifiche, sidebar, modali e stato generale
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // === STATE ===
  
  // Loading states
  const isLoading = ref(false)
  const loadingMessage = ref('')
  const isInitialized = ref(false)
  
  // Sidebar state
  const sidebarOpen = ref(true)
  const sidebarCollapsed = ref(false)
  
  // Notifications
  const notifications = ref([])
  const maxNotifications = ref(5)
  
  // Modals
  const activeModals = ref([])
  
  // Page state
  const currentPage = ref('')
  const pageTitle = ref('DatPortal')
  const breadcrumbs = ref([])
  
  // Theme
  const darkMode = ref(false)
  
  // Mobile detection
  const isMobile = ref(false)
  const isTablet = ref(false)
  
  // Network status
  const isOnline = ref(navigator.onLine)
  
  // === COMPUTED ===
  
  const hasNotifications = computed(() => notifications.value.length > 0)
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.read).length
  )
  const hasActiveModals = computed(() => activeModals.value.length > 0)
  const isDesktop = computed(() => !isMobile.value && !isTablet.value)
  
  // === ACTIONS ===
  
  /**
   * Set loading state
   * @param {boolean} loading - Loading state
   * @param {string} message - Optional loading message
   */
  function setLoading(loading, message = '') {
    isLoading.value = loading
    loadingMessage.value = message
  }
  
  /**
   * Set initialized state
   * @param {boolean} initialized - Initialized state
   */
  function setInitialized(initialized) {
    isInitialized.value = initialized
  }
  
  /**
   * Toggle sidebar
   */
  function toggleSidebar() {
    sidebarOpen.value = !sidebarOpen.value
    localStorage.setItem('sidebarOpen', sidebarOpen.value.toString())
  }
  
  /**
   * Set sidebar state
   * @param {boolean} open - Sidebar open state
   */
  function setSidebarOpen(open) {
    sidebarOpen.value = open
    localStorage.setItem('sidebarOpen', open.toString())
  }
  
  /**
   * Toggle sidebar collapsed state
   */
  function toggleSidebarCollapsed() {
    sidebarCollapsed.value = !sidebarCollapsed.value
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())
  }
  
  /**
   * Show notification
   * @param {string} message - Notification message
   * @param {string} type - Notification type (success, error, warning, info)
   * @param {number} duration - Auto-hide duration in ms (0 = no auto-hide)
   * @param {Object} options - Additional options
   */
  function showNotification(message, type = 'info', duration = 5000, options = {}) {
    const notification = {
      id: Date.now() + Math.random(),
      message,
      type,
      read: false,
      timestamp: new Date(),
      duration,
      ...options
    }
    
    notifications.value.unshift(notification)
    
    // Limit number of notifications
    if (notifications.value.length > maxNotifications.value) {
      notifications.value = notifications.value.slice(0, maxNotifications.value)
    }
    
    // Auto-hide notification
    if (duration > 0) {
      setTimeout(() => {
        hideNotification(notification.id)
      }, duration)
    }
    
    return notification.id
  }
  
  /**
   * Hide notification
   * @param {string|number} id - Notification ID
   */
  function hideNotification(id) {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }
  
  /**
   * Mark notification as read
   * @param {string|number} id - Notification ID
   */
  function markNotificationRead(id) {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
    }
  }
  
  /**
   * Clear all notifications
   */
  function clearNotifications() {
    notifications.value = []
  }
  
  /**
   * Show modal
   * @param {string} component - Modal component name
   * @param {Object} props - Props to pass to modal
   * @param {Object} options - Modal options
   */
  function showModal(component, props = {}, options = {}) {
    const modal = {
      id: Date.now() + Math.random(),
      component,
      props,
      options: {
        closable: true,
        backdrop: true,
        ...options
      }
    }
    
    activeModals.value.push(modal)
    
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden'
    
    return modal.id
  }
  
  /**
   * Hide modal
   * @param {string|number} id - Modal ID
   */
  function hideModal(id) {
    const index = activeModals.value.findIndex(m => m.id === id)
    if (index > -1) {
      activeModals.value.splice(index, 1)
    }
    
    // Restore body scroll if no modals are open
    if (activeModals.value.length === 0) {
      document.body.style.overflow = ''
    }
  }
  
  /**
   * Hide all modals
   */
  function hideAllModals() {
    activeModals.value = []
    document.body.style.overflow = ''
  }
  
  /**
   * Set current page
   * @param {string} page - Page name
   * @param {string} title - Page title
   * @param {Array} breadcrumbsArray - Breadcrumbs array
   */
  function setCurrentPage(page, title = '', breadcrumbsArray = []) {
    currentPage.value = page
    pageTitle.value = title || 'DatPortal'
    breadcrumbs.value = breadcrumbsArray
    
    // Update document title
    document.title = title ? `${title} - DatPortal` : 'DatPortal'
  }
  
  /**
   * Toggle dark mode
   */
  function toggleDarkMode() {
    darkMode.value = !darkMode.value
    
    // Apply to document
    if (darkMode.value) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
    
    // Save preference
    localStorage.setItem('darkMode', darkMode.value.toString())
    
    // Emit event for other components
    window.dispatchEvent(new CustomEvent('darkModeChanged', {
      detail: { darkMode: darkMode.value }
    }))
  }
  
  /**
   * Set dark mode
   * @param {boolean} enabled - Dark mode enabled
   */
  function setDarkMode(enabled) {
    darkMode.value = enabled
    
    if (enabled) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
    
    localStorage.setItem('darkMode', enabled.toString())
    
    window.dispatchEvent(new CustomEvent('darkModeChanged', {
      detail: { darkMode: enabled }
    }))
  }
  
  /**
   * Update screen size detection
   */
  function updateScreenSize() {
    const width = window.innerWidth
    isMobile.value = width < 768
    isTablet.value = width >= 768 && width < 1024
  }
  
  /**
   * Initialize app store
   */
  function initialize() {
    // Load saved preferences
    const savedSidebarOpen = localStorage.getItem('sidebarOpen')
    if (savedSidebarOpen !== null) {
      sidebarOpen.value = savedSidebarOpen === 'true'
    }
    
    const savedSidebarCollapsed = localStorage.getItem('sidebarCollapsed')
    if (savedSidebarCollapsed !== null) {
      sidebarCollapsed.value = savedSidebarCollapsed === 'true'
    }
    
    const savedDarkMode = localStorage.getItem('darkMode')
    if (savedDarkMode !== null) {
      setDarkMode(savedDarkMode === 'true')
    }
    
    // Set up screen size detection
    updateScreenSize()
    window.addEventListener('resize', updateScreenSize)
    
    // Set up online/offline detection
    window.addEventListener('online', () => {
      isOnline.value = true
      showNotification('Connessione ripristinata', 'success', 3000)
    })
    
    window.addEventListener('offline', () => {
      isOnline.value = false
      showNotification('Connessione persa', 'warning', 0)
    })
    
    // Auto-collapse sidebar on mobile
    if (isMobile.value) {
      sidebarOpen.value = false
    }
  }
  
  /**
   * Handle API errors globally
   * @param {Error} error - Error object
   * @param {string} context - Error context
   */
  function handleApiError(error, context = '') {
    console.error('API Error:', error, context)
    
    let message = 'Si è verificato un errore'
    
    if (error.response) {
      // Server responded with error status
      const status = error.response.status
      
      switch (status) {
        case 401:
          message = 'Sessione scaduta. Effettua nuovamente il login.'
          // Redirect to login after a delay
          setTimeout(() => {
            window.location.href = '/auth/login'
          }, 2000)
          break
        case 403:
          message = 'Non hai i permessi per eseguire questa operazione'
          break
        case 404:
          message = 'Risorsa non trovata'
          break
        case 422:
          message = 'Dati non validi'
          break
        case 500:
          message = 'Errore interno del server'
          break
        default:
          message = `Errore ${status}: ${error.response.statusText}`
      }
    } else if (error.request) {
      // Network error
      message = 'Errore di connessione. Verifica la tua connessione internet.'
    }
    
    showNotification(message, 'error', 8000)
  }
  
  // Initialize store
  initialize()
  
  return {
    // State
    isLoading,
    loadingMessage,
    isInitialized,
    sidebarOpen,
    sidebarCollapsed,
    notifications,
    activeModals,
    currentPage,
    pageTitle,
    breadcrumbs,
    darkMode,
    isMobile,
    isTablet,
    isOnline,
    
    // Computed
    hasNotifications,
    unreadNotifications,
    hasActiveModals,
    isDesktop,
    
    // Actions
    setLoading,
    setInitialized,
    toggleSidebar,
    setSidebarOpen,
    toggleSidebarCollapsed,
    showNotification,
    hideNotification,
    markNotificationRead,
    clearNotifications,
    showModal,
    hideModal,
    hideAllModals,
    setCurrentPage,
    toggleDarkMode,
    setDarkMode,
    updateScreenSize,
    initialize,
    handleApiError
  }
})
