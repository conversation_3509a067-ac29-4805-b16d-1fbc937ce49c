/**
 * Vue.js 3 Main Application Entry Point
 * DatPortal SPA with Landing Pages
 */

import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'

// Configurazione router
const routes = [
    { 
        path: '/', 
        component: {
            template: `
                <div class="min-h-screen bg-gray-50">
                    <nav class="bg-white shadow-sm border-b">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="flex justify-between h-16">
                                <div class="flex items-center">
                                    <h1 class="text-xl font-bold text-gray-900">DatPortal</h1>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <router-link to="/" class="text-blue-600 font-medium">Home</router-link>
                                    <router-link to="/services" class="text-gray-700 hover:text-blue-600">Servizi</router-link>
                                    <router-link to="/about" class="text-gray-700 hover:text-blue-600">Chi Siamo</router-link>
                                    <router-link to="/contact" class="text-gray-700 hover:text-blue-600">Contatti</router-link>
                                    <a href="/auth/login" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Login</a>
                                </div>
                            </div>
                        </div>
                    </nav>
                    <div class="flex items-center justify-center py-20">
                        <div class="text-center">
                            <h1 class="text-5xl font-bold text-gray-900 mb-6">Benvenuto in DatPortal</h1>
                            <p class="text-xl text-gray-600 mb-8">Sistema di gestione progetti e risorse</p>
                            <div class="space-x-4">
                                <router-link to="/services" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600">
                                    Scopri i Servizi
                                </router-link>
                                <router-link to="/about" class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50">
                                    Chi Siamo
                                </router-link>
                            </div>
                        </div>
                    </div>
                </div>
            `
        }
    },
    { 
        path: '/services', 
        component: {
            template: `
                <div class="min-h-screen bg-gray-50">
                    <nav class="bg-white shadow-sm border-b">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="flex justify-between h-16">
                                <div class="flex items-center">
                                    <router-link to="/" class="text-xl font-bold text-gray-900">DatPortal</router-link>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <router-link to="/" class="text-gray-700 hover:text-blue-600">Home</router-link>
                                    <router-link to="/services" class="text-blue-600 font-medium">Servizi</router-link>
                                    <router-link to="/about" class="text-gray-700 hover:text-blue-600">Chi Siamo</router-link>
                                    <router-link to="/contact" class="text-gray-700 hover:text-blue-600">Contatti</router-link>
                                    <a href="/auth/login" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Login</a>
                                </div>
                            </div>
                        </div>
                    </nav>
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                        <h1 class="text-4xl font-bold text-gray-900 mb-8">I Nostri Servizi</h1>
                        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <div class="bg-white p-6 rounded-lg shadow-sm">
                                <h3 class="text-xl font-semibold mb-4">Gestione Progetti</h3>
                                <p class="text-gray-600">Pianificazione, monitoraggio e controllo completo dei progetti aziendali.</p>
                            </div>
                            <div class="bg-white p-6 rounded-lg shadow-sm">
                                <h3 class="text-xl font-semibold mb-4">Gestione Risorse</h3>
                                <p class="text-gray-600">Allocazione ottimale delle risorse umane e materiali.</p>
                            </div>
                            <div class="bg-white p-6 rounded-lg shadow-sm">
                                <h3 class="text-xl font-semibold mb-4">Analytics & KPI</h3>
                                <p class="text-gray-600">Dashboard avanzate per il monitoraggio delle performance.</p>
                            </div>
                        </div>
                    </div>
                </div>
            `
        }
    },
    { 
        path: '/about', 
        component: {
            template: `
                <div class="min-h-screen bg-gray-50">
                    <nav class="bg-white shadow-sm border-b">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="flex justify-between h-16">
                                <div class="flex items-center">
                                    <router-link to="/" class="text-xl font-bold text-gray-900">DatPortal</router-link>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <router-link to="/" class="text-gray-700 hover:text-blue-600">Home</router-link>
                                    <router-link to="/services" class="text-gray-700 hover:text-blue-600">Servizi</router-link>
                                    <router-link to="/about" class="text-blue-600 font-medium">Chi Siamo</router-link>
                                    <router-link to="/contact" class="text-gray-700 hover:text-blue-600">Contatti</router-link>
                                    <a href="/auth/login" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Login</a>
                                </div>
                            </div>
                        </div>
                    </nav>
                    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                        <h1 class="text-4xl font-bold text-gray-900 mb-8">Chi Siamo</h1>
                        <div class="prose prose-lg">
                            <p class="text-gray-600 mb-6">
                                DatPortal è una piattaforma innovativa per la gestione integrata di progetti, risorse e performance aziendali.
                            </p>
                            <p class="text-gray-600 mb-6">
                                La nostra missione è fornire alle aziende gli strumenti necessari per ottimizzare i processi, 
                                migliorare l'efficienza operativa e raggiungere i propri obiettivi strategici.
                            </p>
                            <h2 class="text-2xl font-semibold text-gray-900 mb-4">La Nostra Visione</h2>
                            <p class="text-gray-600">
                                Crediamo in un futuro dove la gestione aziendale sia semplice, trasparente e data-driven.
                            </p>
                        </div>
                    </div>
                </div>
            `
        }
    },
    { 
        path: '/contact', 
        component: {
            template: `
                <div class="min-h-screen bg-gray-50">
                    <nav class="bg-white shadow-sm border-b">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="flex justify-between h-16">
                                <div class="flex items-center">
                                    <router-link to="/" class="text-xl font-bold text-gray-900">DatPortal</router-link>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <router-link to="/" class="text-gray-700 hover:text-blue-600">Home</router-link>
                                    <router-link to="/services" class="text-gray-700 hover:text-blue-600">Servizi</router-link>
                                    <router-link to="/about" class="text-gray-700 hover:text-blue-600">Chi Siamo</router-link>
                                    <router-link to="/contact" class="text-blue-600 font-medium">Contatti</router-link>
                                    <a href="/auth/login" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Login</a>
                                </div>
                            </div>
                        </div>
                    </nav>
                    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                        <h1 class="text-4xl font-bold text-gray-900 mb-8">Contattaci</h1>
                        <div class="grid md:grid-cols-2 gap-8">
                            <div>
                                <h2 class="text-2xl font-semibold mb-4">Informazioni di Contatto</h2>
                                <div class="space-y-4">
                                    <div>
                                        <h3 class="font-medium">Email</h3>
                                        <p class="text-gray-600"><EMAIL></p>
                                    </div>
                                    <div>
                                        <h3 class="font-medium">Telefono</h3>
                                        <p class="text-gray-600">+39 02 1234 5678</p>
                                    </div>
                                    <div>
                                        <h3 class="font-medium">Indirizzo</h3>
                                        <p class="text-gray-600">Via Roma 123<br>20100 Milano, Italia</p>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h2 class="text-2xl font-semibold mb-4">Invia un Messaggio</h2>
                                <form class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Nome</label>
                                        <input type="text" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Email</label>
                                        <input type="email" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Messaggio</label>
                                        <textarea rows="4" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"></textarea>
                                    </div>
                                    <button type="submit" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                                        Invia Messaggio
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            `
        }
    },
    { path: '/privacy', redirect: '/about' }
]

const router = createRouter({
    history: createWebHistory(),
    routes
})

// App Vue.js con router
const vueApp = createApp({
    template: '<router-view />'
})

vueApp.use(router)

// Mount the app
vueApp.mount('#app')

// Make it available globally for debugging
window.vueApp = vueApp

console.log('🎉 Vue.js SPA con router caricato con successo!')

export default vueApp
