/**
 * Vue.js 3 Main Application Entry Point - VERSIONE SEMPLIFICATA
 * DatPortal SPA Basic Setup
 */

import { createApp } from 'vue'

// Semplice app Vue.js per testare che i file locali funzionano
const vueApp = createApp({
    data() {
        return {
            message: 'Vue.js SPA Funziona! 🎉 (File Locali)',
            isAuthenticated: window.APP_CONFIG?.isAuthenticated || false,
            user: window.APP_CONFIG?.user || null
        }
    },
    template: `
        <div class="min-h-screen flex items-center justify-center bg-gray-50">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-green-600 mb-4">{{ message }}</h1>
                <p class="text-gray-600 mb-4">DatPortal Vue.js SPA è attivo con file locali</p>
                <div v-if="isAuthenticated" class="mb-4">
                    <p class="text-blue-600">Benvenuto, {{ user?.first_name || 'Utente' }}!</p>
                </div>
                <div class="mt-8 space-x-4">
                    <a href="/auth/login" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                        Vai al Login
                    </a>
                    <a href="/dashboard" class="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600">
                        Dashboard
                    </a>
                </div>
            </div>
        </div>
    `
})

// Mount the app
vueApp.mount('#app')

// Make it available globally for debugging
window.vueApp = vueApp

console.log('🎉 Vue.js SPA caricato con successo dai file locali!')

export default vueApp
