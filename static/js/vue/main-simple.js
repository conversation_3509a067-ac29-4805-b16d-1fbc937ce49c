/**
 * Vue.js 3 Simple Entry Point - IIFE Version
 * Temporary solution to get the app running
 */

(function() {
    'use strict';
    
    // Check if Vue.js is loaded
    if (typeof Vue === 'undefined') {
        console.error('Vue.js is not loaded');
        return;
    }
    
    const { createApp, ref, onMounted } = Vue;
    const { createRouter, createWebHistory } = VueRouter;
    const { createPinia, defineStore } = Pinia;

    // Simple routes
    const routes = [
        { 
            path: '/', 
            component: {
                template: `
                    <div class="min-h-screen bg-white">
                        <nav class="bg-blue-600 text-white p-4">
                            <div class="container mx-auto">
                                <h1 class="text-2xl font-bold">{{ tenantName }}</h1>
                            </div>
                        </nav>
                        <main class="container mx-auto p-8">
                            <h2 class="text-3xl font-bold mb-6">{{ heroTitle }}</h2>
                            <p class="text-lg text-gray-600 mb-8">{{ heroSubtitle }}</p>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="bg-gray-50 p-6 rounded-lg">
                                    <h3 class="text-xl font-semibold mb-2">Servizi</h3>
                                    <p>Scopri i nostri servizi</p>
                                </div>
                                <div class="bg-gray-50 p-6 rounded-lg">
                                    <h3 class="text-xl font-semibold mb-2">Chi Siamo</h3>
                                    <p>La nostra storia</p>
                                </div>
                                <div class="bg-gray-50 p-6 rounded-lg">
                                    <h3 class="text-xl font-semibold mb-2">Contatti</h3>
                                    <p>Mettiti in contatto</p>
                                </div>
                            </div>
                        </main>
                    </div>
                `,
                setup() {
                    const tenantName = ref('DatVinci');
                    const heroTitle = ref('Innovazione per il futuro');
                    const heroSubtitle = ref('Supportiamo le aziende nel loro percorso di crescita');
                    
                    // Load tenant config
                    onMounted(async () => {
                        try {
                            const response = await axios.get('/api/public/config');
                            if (response.data.success) {
                                const config = response.data.data;
                                tenantName.value = config.company?.name || 'DatVinci';
                                heroTitle.value = config.pages?.home?.hero?.title || 'Innovazione per il futuro';
                                heroSubtitle.value = config.pages?.home?.hero?.subtitle || 'Supportiamo le aziende nel loro percorso di crescita';
                            }
                        } catch (error) {
                            console.error('Failed to load tenant config:', error);
                        }
                    });
                    
                    return {
                        tenantName,
                        heroTitle,
                        heroSubtitle
                    };
                }
            }
        },
        { 
            path: '/services', 
            component: {
                template: `
                    <div class="min-h-screen bg-white">
                        <nav class="bg-blue-600 text-white p-4">
                            <div class="container mx-auto">
                                <h1 class="text-2xl font-bold">DatVinci</h1>
                            </div>
                        </nav>
                        <main class="container mx-auto p-8">
                            <h2 class="text-3xl font-bold mb-6">I nostri servizi</h2>
                            <p class="text-lg text-gray-600">Soluzioni innovative per ogni esigenza aziendale</p>
                        </main>
                    </div>
                `
            }
        },
        { 
            path: '/about', 
            component: {
                template: `
                    <div class="min-h-screen bg-white">
                        <nav class="bg-blue-600 text-white p-4">
                            <div class="container mx-auto">
                                <h1 class="text-2xl font-bold">DatVinci</h1>
                            </div>
                        </nav>
                        <main class="container mx-auto p-8">
                            <h2 class="text-3xl font-bold mb-6">Chi Siamo</h2>
                            <p class="text-lg text-gray-600">La nostra storia e i nostri valori</p>
                        </main>
                    </div>
                `
            }
        },
        { 
            path: '/contact', 
            component: {
                template: `
                    <div class="min-h-screen bg-white">
                        <nav class="bg-blue-600 text-white p-4">
                            <div class="container mx-auto">
                                <h1 class="text-2xl font-bold">DatVinci</h1>
                            </div>
                        </nav>
                        <main class="container mx-auto p-8">
                            <h2 class="text-3xl font-bold mb-6">Contattaci</h2>
                            <p class="text-lg text-gray-600">Siamo qui per aiutarti</p>
                        </main>
                    </div>
                `
            }
        },
        { 
            path: '/privacy', 
            component: {
                template: `
                    <div class="min-h-screen bg-white">
                        <nav class="bg-blue-600 text-white p-4">
                            <div class="container mx-auto">
                                <h1 class="text-2xl font-bold">DatVinci</h1>
                            </div>
                        </nav>
                        <main class="container mx-auto p-8">
                            <h2 class="text-3xl font-bold mb-6">Privacy Policy</h2>
                            <p class="text-lg text-gray-600">Informativa sulla privacy</p>
                        </main>
                    </div>
                `
            }
        }
    ];

    // Create router
    const router = createRouter({
        history: createWebHistory(),
        routes
    });

    // Create Pinia store
    const pinia = createPinia();

    // Create Vue app
    const app = createApp({
        template: '<router-view></router-view>'
    });

    // Use plugins
    app.use(router);
    app.use(pinia);

    // Mount app
    app.mount('#app');

    console.log('🎉 Vue.js SPA loaded successfully!');
    
})();
