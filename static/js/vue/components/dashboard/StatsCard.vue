<template>
  <div class="card-brand p-6 hover:shadow-brand-lg transition-all duration-300 group">
    <div class="flex items-center">
      <!-- Icon -->
      <div 
        class="flex-shrink-0 rounded-md p-3 transition-transform duration-300 group-hover:scale-110"
        :class="iconBgClass"
      >
        <i :class="[icon, 'h-6 w-6 text-white']"></i>
      </div>
      
      <!-- Content -->
      <div class="ml-5 w-0 flex-1">
        <dl>
          <dt class="text-sm font-medium text-brand-text-secondary truncate">
            {{ title }}
          </dt>
          <dd>
            <div class="text-lg font-medium text-brand-text-primary">
              {{ formattedValue }}
            </div>
          </dd>
        </dl>
      </div>
    </div>
    
    <!-- Footer with trend and link -->
    <div class="mt-4 flex items-center justify-between">
      <!-- Trend -->
      <div v-if="trend" class="flex items-center text-sm">
        <i 
          :class="[
            trend.direction === 'up' ? 'fas fa-arrow-up text-brand-success-500' : 
            trend.direction === 'down' ? 'fas fa-arrow-down text-brand-error-500' : 
            'fas fa-minus text-brand-text-tertiary',
            'h-3 w-3 mr-1'
          ]"
        ></i>
        <span 
          :class="[
            trend.direction === 'up' ? 'text-brand-success-600' : 
            trend.direction === 'down' ? 'text-brand-error-600' : 
            'text-brand-text-tertiary'
          ]"
        >
          {{ trend.value }}{{ trend.unit || '%' }}
        </span>
        <span class="text-brand-text-tertiary ml-1">
          {{ trend.period || 'vs periodo precedente' }}
        </span>
      </div>
      
      <!-- Link -->
      <div v-if="link">
        <router-link
          :to="link"
          class="text-sm font-medium text-brand-primary-600 dark:text-brand-primary-400 hover:text-brand-primary-500 dark:hover:text-brand-primary-300 transition-colors"
        >
          Vedi tutti
          <i class="fas fa-arrow-right ml-1 h-3 w-3"></i>
        </router-link>
      </div>
    </div>
    
    <!-- Loading overlay -->
    <div 
      v-if="loading"
      class="absolute inset-0 bg-white dark:bg-gray-800 bg-opacity-75 flex items-center justify-center rounded-lg"
    >
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-brand-primary-500"></div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [String, Number],
    required: true
  },
  icon: {
    type: String,
    required: true
  },
  color: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'success', 'warning', 'error'].includes(value)
  },
  trend: {
    type: Object,
    default: null
    // Expected format: { direction: 'up'|'down'|'neutral', value: number, unit: '%', period: 'vs last month' }
  },
  link: {
    type: String,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  format: {
    type: String,
    default: 'number',
    validator: (value) => ['number', 'currency', 'percentage'].includes(value)
  }
})

// Computed
const iconBgClass = computed(() => {
  const classes = {
    primary: 'bg-brand-primary-500',
    secondary: 'bg-brand-secondary-500',
    success: 'bg-brand-success-500',
    warning: 'bg-brand-warning-500',
    error: 'bg-brand-error-500'
  }
  return classes[props.color] || classes.primary
})

const formattedValue = computed(() => {
  if (typeof props.value === 'string') {
    return props.value
  }
  
  switch (props.format) {
    case 'currency':
      return new Intl.NumberFormat('it-IT', {
        style: 'currency',
        currency: 'EUR'
      }).format(props.value)
    
    case 'percentage':
      return `${props.value}%`
    
    case 'number':
    default:
      return new Intl.NumberFormat('it-IT').format(props.value)
  }
})
</script>

<style scoped>
/* Card hover effects */
.group:hover .card-brand {
  transform: translateY(-2px);
}

/* Icon animation */
.group:hover .flex-shrink-0 {
  transform: scale(1.1);
}

/* Loading state */
.relative {
  position: relative;
}

/* Trend animations */
.fas.fa-arrow-up,
.fas.fa-arrow-down {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .text-lg {
    font-size: 1rem;
  }
  
  .p-6 {
    padding: 1rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .transition-all,
  .transition-transform,
  .transition-colors,
  .group:hover .card-brand,
  .group:hover .flex-shrink-0 {
    transition: none;
    transform: none;
  }
  
  .fas.fa-arrow-up,
  .fas.fa-arrow-down {
    animation: none;
  }
}

/* Focus styles */
.router-link:focus {
  outline: 2px solid var(--brand-primary-500);
  outline-offset: 2px;
  border-radius: var(--brand-radius-sm);
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .card-brand {
    border: 2px solid var(--brand-border-primary);
  }
}
</style>
