<template>
  <div class="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
    <transition-group
      name="notification"
      tag="div"
      class="space-y-2"
    >
      <div
        v-for="notification in appStore.notifications"
        :key="notification.id"
        class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border-l-4 p-4 transition-all duration-300"
        :class="getNotificationClasses(notification.type)"
      >
        <div class="flex items-start">
          <!-- Icon -->
          <div class="flex-shrink-0">
            <i 
              :class="getNotificationIcon(notification.type)"
              class="h-5 w-5"
            ></i>
          </div>
          
          <!-- Content -->
          <div class="ml-3 flex-1">
            <p class="text-sm font-medium text-brand-text-primary">
              {{ notification.message }}
            </p>
            <p v-if="notification.description" class="mt-1 text-xs text-brand-text-secondary">
              {{ notification.description }}
            </p>
            
            <!-- Actions -->
            <div v-if="notification.actions" class="mt-2 flex space-x-2">
              <button
                v-for="action in notification.actions"
                :key="action.label"
                @click="handleAction(notification, action)"
                class="text-xs font-medium px-2 py-1 rounded transition-colors"
                :class="action.primary 
                  ? 'bg-brand-primary-500 text-white hover:bg-brand-primary-600' 
                  : 'text-brand-primary-600 hover:text-brand-primary-700'"
              >
                {{ action.label }}
              </button>
            </div>
          </div>
          
          <!-- Close Button -->
          <div class="ml-4 flex-shrink-0">
            <button
              @click="appStore.hideNotification(notification.id)"
              class="text-brand-text-tertiary hover:text-brand-text-secondary transition-colors"
            >
              <i class="fas fa-times h-4 w-4"></i>
            </button>
          </div>
        </div>
        
        <!-- Progress Bar (for timed notifications) -->
        <div 
          v-if="notification.duration > 0"
          class="mt-2 h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden"
        >
          <div 
            class="h-full bg-current opacity-30 transition-all ease-linear"
            :class="getProgressBarClass(notification.type)"
            :style="{ 
              width: '100%',
              animation: `shrink ${notification.duration}ms linear forwards`
            }"
          ></div>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script setup>
import { useAppStore } from '../../stores/app.js'

// Store
const appStore = useAppStore()

// Methods
function getNotificationClasses(type) {
  const classes = {
    success: 'border-brand-success-500 bg-brand-success-50 dark:bg-brand-success-900/20',
    error: 'border-brand-error-500 bg-brand-error-50 dark:bg-brand-error-900/20',
    warning: 'border-brand-warning-500 bg-brand-warning-50 dark:bg-brand-warning-900/20',
    info: 'border-brand-primary-500 bg-brand-primary-50 dark:bg-brand-primary-900/20'
  }
  return classes[type] || classes.info
}

function getNotificationIcon(type) {
  const icons = {
    success: 'fas fa-check-circle text-brand-success-500',
    error: 'fas fa-exclamation-circle text-brand-error-500',
    warning: 'fas fa-exclamation-triangle text-brand-warning-500',
    info: 'fas fa-info-circle text-brand-primary-500'
  }
  return icons[type] || icons.info
}

function getProgressBarClass(type) {
  const classes = {
    success: 'text-brand-success-500',
    error: 'text-brand-error-500',
    warning: 'text-brand-warning-500',
    info: 'text-brand-primary-500'
  }
  return classes[type] || classes.info
}

function handleAction(notification, action) {
  if (action.handler) {
    action.handler(notification)
  }
  
  if (action.closeOnClick !== false) {
    appStore.hideNotification(notification.id)
  }
}
</script>

<style scoped>
/* Notification animations */
.notification-enter-active {
  transition: all 0.3s ease-out;
}

.notification-leave-active {
  transition: all 0.3s ease-in;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}

/* Progress bar shrink animation */
@keyframes shrink {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .fixed.top-4.right-4 {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
  }
}

/* Hover effects */
.bg-white:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark .bg-white:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* Focus styles */
button:focus {
  outline: 2px solid var(--brand-primary-500);
  outline-offset: 2px;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .notification-enter-active,
  .notification-leave-active,
  .notification-move,
  .transition-all {
    transition: none;
  }
  
  @keyframes shrink {
    from, to {
      width: 100%;
    }
  }
}
</style>
