<template>
  <div class="fixed bottom-4 left-4 bg-brand-warning-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-brand-slide-up">
    <div class="flex items-center">
      <i class="fas fa-wifi-slash mr-2"></i>
      <span class="text-sm font-medium">Connessione assente</span>
    </div>
  </div>
</template>

<script setup>
// Simple offline indicator component
</script>

<style scoped>
/* Slide up animation */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-brand-slide-up {
  animation: slideUp 0.3s ease-out;
}
</style>
