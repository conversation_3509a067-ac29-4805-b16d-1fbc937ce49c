<template>
  <div>
    <transition-group name="modal" tag="div">
      <div
        v-for="modal in appStore.activeModals"
        :key="modal.id"
        class="fixed inset-0 z-50 overflow-y-auto"
        @click="handleBackdropClick(modal)"
      >
        <!-- Backdrop -->
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
          
          <!-- Modal Content -->
          <div
            class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
            :class="getModalSizeClass(modal.options.size)"
            @click.stop
          >
            <!-- Header -->
            <div 
              v-if="modal.options.title || modal.options.closable"
              class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4 border-b border-gray-200 dark:border-gray-700"
            >
              <div class="flex items-center justify-between">
                <h3 
                  v-if="modal.options.title"
                  class="text-lg leading-6 font-medium text-brand-text-primary"
                >
                  {{ modal.options.title }}
                </h3>
                <button
                  v-if="modal.options.closable"
                  @click="appStore.hideModal(modal.id)"
                  class="text-brand-text-tertiary hover:text-brand-text-secondary transition-colors"
                >
                  <i class="fas fa-times h-5 w-5"></i>
                </button>
              </div>
            </div>
            
            <!-- Body -->
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6">
              <component 
                :is="modal.component" 
                v-bind="modal.props"
                @close="appStore.hideModal(modal.id)"
                @confirm="handleConfirm(modal, $event)"
                @cancel="handleCancel(modal)"
              />
            </div>
            
            <!-- Footer (if actions are provided) -->
            <div 
              v-if="modal.options.actions"
              class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"
            >
              <button
                v-for="action in modal.options.actions"
                :key="action.label"
                @click="handleActionClick(modal, action)"
                class="w-full inline-flex justify-center rounded-md border px-4 py-2 text-base font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm transition-colors"
                :class="getActionClasses(action.type)"
              >
                <i v-if="action.icon" :class="[action.icon, 'mr-2 h-4 w-4']"></i>
                {{ action.label }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script setup>
import { useAppStore } from '../../stores/app.js'

// Store
const appStore = useAppStore()

// Methods
function getModalSizeClass(size) {
  const sizes = {
    sm: 'sm:max-w-sm',
    md: 'sm:max-w-md',
    lg: 'sm:max-w-lg',
    xl: 'sm:max-w-xl',
    '2xl': 'sm:max-w-2xl',
    '3xl': 'sm:max-w-3xl',
    '4xl': 'sm:max-w-4xl',
    '5xl': 'sm:max-w-5xl',
    '6xl': 'sm:max-w-6xl',
    full: 'sm:max-w-full sm:m-4'
  }
  return sizes[size] || sizes.lg
}

function getActionClasses(type) {
  const classes = {
    primary: 'border-transparent text-white bg-brand-primary-600 hover:bg-brand-primary-700 focus:ring-brand-primary-500',
    secondary: 'border-gray-300 dark:border-gray-600 text-brand-text-primary bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-brand-primary-500',
    danger: 'border-transparent text-white bg-brand-error-600 hover:bg-brand-error-700 focus:ring-brand-error-500',
    success: 'border-transparent text-white bg-brand-success-600 hover:bg-brand-success-700 focus:ring-brand-success-500',
    warning: 'border-transparent text-white bg-brand-warning-600 hover:bg-brand-warning-700 focus:ring-brand-warning-500'
  }
  return classes[type] || classes.secondary
}

function handleBackdropClick(modal) {
  if (modal.options.backdrop && modal.options.closable) {
    appStore.hideModal(modal.id)
  }
}

function handleConfirm(modal, data) {
  if (modal.options.onConfirm) {
    modal.options.onConfirm(data)
  }
  appStore.hideModal(modal.id)
}

function handleCancel(modal) {
  if (modal.options.onCancel) {
    modal.options.onCancel()
  }
  appStore.hideModal(modal.id)
}

function handleActionClick(modal, action) {
  if (action.handler) {
    action.handler(modal)
  }
  
  if (action.closeModal !== false) {
    appStore.hideModal(modal.id)
  }
}
</script>

<style scoped>
/* Modal animations */
.modal-enter-active {
  transition: opacity 0.3s ease-out;
}

.modal-leave-active {
  transition: opacity 0.3s ease-in;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-active .inline-block {
  transition: all 0.3s ease-out;
}

.modal-leave-active .inline-block {
  transition: all 0.3s ease-in;
}

.modal-enter-from .inline-block {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

.modal-leave-to .inline-block {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

/* Backdrop animation */
.modal-enter-active .fixed.inset-0.bg-gray-500 {
  transition: opacity 0.3s ease-out;
}

.modal-leave-active .fixed.inset-0.bg-gray-500 {
  transition: opacity 0.3s ease-in;
}

.modal-enter-from .fixed.inset-0.bg-gray-500,
.modal-leave-to .fixed.inset-0.bg-gray-500 {
  opacity: 0;
}

/* Focus trap */
.modal-container {
  isolation: isolate;
}

/* Scrollbar styling for modal content */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: var(--brand-bg-tertiary);
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: var(--brand-border-secondary);
  border-radius: var(--brand-radius-full);
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: var(--brand-primary-400);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .sm\\:max-w-lg {
    max-width: calc(100vw - 2rem);
    margin: 1rem;
  }
  
  .sm\\:flex-row-reverse {
    flex-direction: column;
  }
  
  .sm\\:ml-3 {
    margin-left: 0;
    margin-top: 0.75rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .modal-enter-active,
  .modal-leave-active,
  .transition-all,
  .transition-colors,
  .transition-opacity {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .border-gray-300 {
    border-color: #000;
  }
  
  .bg-gray-50 {
    background-color: #f0f0f0;
  }
}
</style>
