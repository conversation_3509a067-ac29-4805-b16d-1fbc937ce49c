<template>
  <div 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 transition-opacity duration-300"
    :class="{ 'opacity-0 pointer-events-none': !visible }"
  >
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl max-w-sm w-full mx-4 animate-brand-scale-in">
      <!-- Loading Spinner -->
      <div class="flex items-center justify-center mb-4">
        <div class="relative">
          <!-- Outer ring -->
          <div class="w-16 h-16 border-4 border-brand-primary-200 rounded-full animate-spin"></div>
          <!-- Inner ring -->
          <div class="absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-brand-primary-500 rounded-full animate-spin"></div>
          <!-- Center dot -->
          <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-brand-primary-500 rounded-full animate-pulse"></div>
        </div>
      </div>
      
      <!-- Loading Message -->
      <div class="text-center">
        <h3 class="text-lg font-medium text-brand-text-primary mb-2">
          {{ title }}
        </h3>
        <p class="text-sm text-brand-text-secondary">
          {{ message }}
        </p>
        
        <!-- Progress Bar (optional) -->
        <div v-if="showProgress" class="mt-4">
          <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-brand-primary-500 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${progress}%` }"
            ></div>
          </div>
          <p class="text-xs text-brand-text-tertiary mt-1">
            {{ progress }}% completato
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// Props
const props = defineProps({
  message: {
    type: String,
    default: 'Caricamento in corso...'
  },
  title: {
    type: String,
    default: 'Attendere'
  },
  showProgress: {
    type: Boolean,
    default: false
  },
  progress: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0 && value <= 100
  }
})

// State
const visible = ref(false)

// Show overlay with animation
onMounted(() => {
  // Small delay to ensure smooth animation
  setTimeout(() => {
    visible.value = true
  }, 10)
})

// Prevent body scroll when overlay is visible
onMounted(() => {
  document.body.style.overflow = 'hidden'
})

onUnmounted(() => {
  document.body.style.overflow = ''
})
</script>

<style scoped>
/* Custom spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Scale in animation */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-brand-scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* Pulse animation for center dot */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .animate-spin,
  .animate-brand-scale-in,
  .animate-pulse {
    animation: none;
  }
}
</style>
