<template>
  <router-link
    :to="to"
    class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-150"
    :class="[
      isActive 
        ? 'text-white bg-brand-primary-800 dark:bg-gray-700 shadow-md' 
        : 'text-brand-primary-100 hover:bg-brand-primary-600 dark:text-gray-300 dark:hover:bg-gray-700',
      collapsed ? 'justify-center' : ''
    ]"
    @click="handleClick"
  >
    <!-- Icon -->
    <i 
      :class="[
        icon,
        collapsed ? 'mr-0' : 'mr-3',
        'h-6 w-6 flex-shrink-0 transition-colors duration-150'
      ]"
    ></i>
    
    <!-- Label -->
    <span 
      class="truncate transition-all duration-150"
      :class="{ 'hidden': collapsed }"
    >
      {{ label }}
    </span>
    
    <!-- Badge (optional) -->
    <span 
      v-if="badge && !collapsed"
      class="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
      :class="badgeClass"
    >
      {{ badge }}
    </span>
    
    <!-- Tooltip for collapsed state -->
    <div 
      v-if="collapsed"
      class="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 pointer-events-none group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap"
    >
      {{ label }}
      <div class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-full">
        <div class="border-4 border-transparent border-r-gray-900"></div>
      </div>
    </div>
  </router-link>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

// Props
const props = defineProps({
  to: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    required: true
  },
  label: {
    type: String,
    required: true
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  badge: {
    type: [String, Number],
    default: null
  },
  badgeType: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'success', 'warning', 'error'].includes(value)
  }
})

// Emits
const emit = defineEmits(['click'])

// Route
const route = useRoute()

// Computed
const isActive = computed(() => {
  if (props.to === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(props.to)
})

const badgeClass = computed(() => {
  const classes = {
    primary: 'bg-brand-primary-500 text-white',
    secondary: 'bg-brand-secondary-500 text-white',
    success: 'bg-brand-success-500 text-white',
    warning: 'bg-brand-warning-500 text-white',
    error: 'bg-brand-error-500 text-white'
  }
  return classes[props.badgeType] || classes.primary
})

// Methods
function handleClick() {
  emit('click')
}
</script>

<style scoped>
/* Hover effects */
.group:hover .transition-colors {
  color: inherit;
}

/* Active state enhancements */
.router-link-active {
  position: relative;
}

.router-link-active::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: var(--brand-secondary-400);
  border-radius: 0 2px 2px 0;
}

/* Tooltip positioning */
.group {
  position: relative;
}

/* Animation for badge */
.badge-enter-active,
.badge-leave-active {
  transition: all 0.2s ease;
}

.badge-enter-from,
.badge-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

/* Focus styles */
.group:focus {
  outline: 2px solid var(--brand-primary-300);
  outline-offset: 2px;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .transition-all,
  .transition-colors,
  .transition-opacity {
    transition: none;
  }
}
</style>
