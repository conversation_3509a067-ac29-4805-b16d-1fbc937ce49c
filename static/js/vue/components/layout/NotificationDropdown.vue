<template>
  <div class="relative" v-click-outside="closeDropdown">
    <!-- Notification Button -->
    <button
      @click="toggleDropdown"
      class="p-2 rounded-full text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-brand-primary-500 transition-colors relative"
    >
      <i class="fas fa-bell h-5 w-5"></i>
      
      <!-- Badge -->
      <span 
        v-if="appStore.unreadNotifications > 0"
        class="absolute -top-1 -right-1 h-4 w-4 bg-brand-error-500 text-white text-xs rounded-full flex items-center justify-center"
      >
        {{ appStore.unreadNotifications > 9 ? '9+' : appStore.unreadNotifications }}
      </span>
    </button>

    <!-- Dropdown -->
    <transition name="dropdown">
      <div 
        v-if="isOpen"
        class="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50"
      >
        <!-- Header -->
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="text-sm font-medium text-brand-text-primary">
              Notifiche
            </h3>
            <button
              v-if="appStore.notifications.length > 0"
              @click="clearAll"
              class="text-xs text-brand-primary-600 hover:text-brand-primary-700 transition-colors"
            >
              Cancella tutto
            </button>
          </div>
        </div>

        <!-- Notifications List -->
        <div class="max-h-64 overflow-y-auto">
          <div v-if="appStore.notifications.length === 0" class="px-4 py-6 text-center">
            <i class="fas fa-bell-slash h-8 w-8 text-gray-400 mx-auto mb-2"></i>
            <p class="text-sm text-brand-text-secondary">Nessuna notifica</p>
          </div>
          
          <div v-else class="py-1">
            <div
              v-for="notification in appStore.notifications"
              :key="notification.id"
              class="px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
              :class="{ 'bg-brand-primary-50 dark:bg-brand-primary-900/20': !notification.read }"
              @click="markAsRead(notification)"
            >
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <i 
                    :class="getNotificationIcon(notification.type)"
                    class="h-4 w-4 mt-0.5"
                  ></i>
                </div>
                <div class="ml-3 flex-1 min-w-0">
                  <p class="text-sm text-brand-text-primary">
                    {{ notification.message }}
                  </p>
                  <p class="text-xs text-brand-text-tertiary mt-1">
                    {{ formatTime(notification.timestamp) }}
                  </p>
                </div>
                <div v-if="!notification.read" class="flex-shrink-0 ml-2">
                  <div class="h-2 w-2 bg-brand-primary-500 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div v-if="appStore.notifications.length > 0" class="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
          <router-link
            to="/notifications"
            class="text-sm text-brand-primary-600 hover:text-brand-primary-700 transition-colors"
            @click="closeDropdown"
          >
            Vedi tutte le notifiche
          </router-link>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAppStore } from '../../stores/app.js'

// Store
const appStore = useAppStore()

// State
const isOpen = ref(false)

// Methods
function toggleDropdown() {
  isOpen.value = !isOpen.value
}

function closeDropdown() {
  isOpen.value = false
}

function markAsRead(notification) {
  appStore.markNotificationRead(notification.id)
}

function clearAll() {
  appStore.clearNotifications()
  closeDropdown()
}

function getNotificationIcon(type) {
  const icons = {
    success: 'fas fa-check-circle text-brand-success-500',
    error: 'fas fa-exclamation-circle text-brand-error-500',
    warning: 'fas fa-exclamation-triangle text-brand-warning-500',
    info: 'fas fa-info-circle text-brand-primary-500'
  }
  return icons[type] || icons.info
}

function formatTime(timestamp) {
  const now = new Date()
  const time = new Date(timestamp)
  const diff = now - time
  
  if (diff < 60000) return 'Ora'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}m fa`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}h fa`
  return time.toLocaleDateString('it-IT')
}
</script>

<style scoped>
/* Dropdown animations */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Custom scrollbar */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: var(--brand-bg-tertiary);
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: var(--brand-border-secondary);
  border-radius: 2px;
}
</style>
