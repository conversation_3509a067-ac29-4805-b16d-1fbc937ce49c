<template>
  <div class="relative" v-click-outside="closeDropdown">
    <!-- User Button -->
    <button
      @click="toggleDropdown"
      class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500 transition-all"
    >
      <img 
        v-if="authStore.user?.profile_image"
        class="h-8 w-8 rounded-full object-cover"
        :src="authStore.user.profile_image" 
        :alt="authStore.userFullName"
      >
      <div 
        v-else
        class="h-8 w-8 rounded-full bg-brand-primary-500 flex items-center justify-center text-white text-sm font-medium"
      >
        {{ authStore.userInitials }}
      </div>
    </button>

    <!-- Dropdown -->
    <transition name="dropdown">
      <div 
        v-if="isOpen"
        class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50"
      >
        <!-- User Info -->
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <p class="text-sm font-medium text-brand-text-primary">
            {{ authStore.userFullName }}
          </p>
          <p class="text-xs text-brand-text-secondary">
            {{ authStore.user?.email }}
          </p>
          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-brand-primary-100 text-brand-primary-800 dark:bg-brand-primary-900 dark:text-brand-primary-200 mt-1">
            {{ authStore.userRole }}
          </span>
        </div>

        <!-- Menu Items -->
        <div class="py-1">
          <!-- Profile -->
          <router-link
            to="/profile"
            @click="closeDropdown"
            class="flex items-center px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <i class="fas fa-user mr-3 h-4 w-4 text-gray-400"></i>
            Il mio profilo
          </router-link>

          <!-- Settings -->
          <router-link
            to="/settings"
            @click="closeDropdown"
            class="flex items-center px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <i class="fas fa-cog mr-3 h-4 w-4 text-gray-400"></i>
            Impostazioni
          </router-link>

          <!-- Dark Mode Toggle -->
          <button
            @click="toggleDarkMode"
            class="flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <i 
              :class="[
                appStore.darkMode ? 'fas fa-sun' : 'fas fa-moon',
                'mr-3 h-4 w-4 text-gray-400'
              ]"
            ></i>
            {{ appStore.darkMode ? 'Modalità chiara' : 'Modalità scura' }}
          </button>

          <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>

          <!-- Admin Panel (Admin only) -->
          <router-link
            v-if="authStore.isAdmin"
            to="/admin"
            @click="closeDropdown"
            class="flex items-center px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <i class="fas fa-shield-alt mr-3 h-4 w-4 text-brand-warning-500"></i>
            Pannello Admin
          </router-link>

          <!-- Brand Settings (Admin only) -->
          <router-link
            v-if="authStore.isAdmin"
            to="/settings/brand"
            @click="closeDropdown"
            class="flex items-center px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <i class="fas fa-palette mr-3 h-4 w-4 text-brand-accent-500"></i>
            Impostazioni Brand
          </router-link>

          <div v-if="authStore.isAdmin" class="border-t border-gray-200 dark:border-gray-700 my-1"></div>

          <!-- Logout -->
          <button
            @click="logout"
            class="flex items-center w-full px-4 py-2 text-sm text-brand-error-600 hover:bg-brand-error-50 dark:hover:bg-brand-error-900/20 transition-colors"
          >
            <i class="fas fa-sign-out-alt mr-3 h-4 w-4"></i>
            Logout
          </button>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAuthStore } from '../../stores/auth.js'
import { useAppStore } from '../../stores/app.js'

// Stores
const authStore = useAuthStore()
const appStore = useAppStore()

// State
const isOpen = ref(false)

// Methods
function toggleDropdown() {
  isOpen.value = !isOpen.value
}

function closeDropdown() {
  isOpen.value = false
}

function toggleDarkMode() {
  appStore.toggleDarkMode()
}

function logout() {
  closeDropdown()
  authStore.logout()
}
</script>

<style scoped>
/* Dropdown animations */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Avatar hover effect */
button:hover img,
button:hover div {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* Focus styles */
button:focus {
  outline: 2px solid var(--brand-primary-500);
  outline-offset: 2px;
}

/* Menu item hover effects */
.router-link:hover i,
button:hover i {
  color: var(--brand-primary-500);
  transition: color 0.2s ease;
}
</style>
