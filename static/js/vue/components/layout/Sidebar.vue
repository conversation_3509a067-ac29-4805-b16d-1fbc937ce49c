<template>
  <!-- Desktop Sidebar -->
  <div class="hidden md:flex md:flex-shrink-0">
    <div 
      class="flex flex-col transition-all duration-300"
      :class="[
        appStore.sidebarOpen ? 'w-sidebar' : 'w-sidebar-collapsed'
      ]"
    >
      <div class="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-brand-primary-700 dark:bg-gray-800 border-r border-brand-primary-600 dark:border-gray-700">
        <!-- Logo Section -->
        <div class="flex items-center flex-shrink-0 px-4">
          <div class="flex items-center" :class="{ 'justify-center': !appStore.sidebarOpen }">
            <!-- Logo when sidebar is open -->
            <router-link 
              to="/" 
              class="flex items-center" 
              :class="{ 'hidden': !appStore.sidebarOpen }"
            >
              <img 
                :src="brandStore.currentLogo" 
                alt="Logo" 
                class="h-12 w-auto rounded-lg shadow-xl"
              >
              <h3 class="ml-3 text-2xl font-light leading-tight text-white">
                {{ brandStore.brandConfig.name }}
              </h3>
            </router-link>
            
            <!-- Compact logo for collapsed sidebar -->
            <router-link 
              to="/" 
              class="flex items-center justify-center" 
              :class="{ 'hidden': appStore.sidebarOpen }"
            >
              <img 
                :src="brandStore.compactLogo" 
                alt="Logo" 
                class="h-8 w-auto rounded-lg shadow-xl"
              >
            </router-link>
          </div>
          
          <!-- Toggle Button -->
          <button 
            @click="appStore.toggleSidebar()"
            class="ml-auto text-white focus:outline-none hover:text-brand-primary-200 transition-colors"
          >
            <i class="fas fa-bars" v-if="!appStore.sidebarOpen"></i>
            <i class="fas fa-times" v-else></i>
          </button>
        </div>

        <!-- Navigation Menu -->
        <nav class="mt-5 flex-1 px-2 space-y-1">
          <!-- Dashboard -->
          <SidebarItem
            to="/"
            icon="fas fa-home"
            label="Dashboard"
            :collapsed="!appStore.sidebarOpen"
          />

          <!-- Personnel with Submenu -->
          <SidebarGroup
            icon="fas fa-users"
            label="Personale"
            :collapsed="!appStore.sidebarOpen"
            :items="personnelItems"
          />

          <!-- Projects -->
          <SidebarItem
            to="/projects"
            icon="fas fa-project-diagram"
            label="Progetti"
            :collapsed="!appStore.sidebarOpen"
          />

          <!-- CRM -->
          <SidebarItem
            to="/crm"
            icon="fas fa-handshake"
            label="CRM"
            :collapsed="!appStore.sidebarOpen"
          />

          <!-- Products -->
          <SidebarItem
            to="/products"
            icon="fas fa-box"
            label="Prodotti"
            :collapsed="!appStore.sidebarOpen"
          />

          <!-- Performance -->
          <SidebarItem
            to="/performance"
            icon="fas fa-chart-line"
            label="Performance"
            :collapsed="!appStore.sidebarOpen"
          />

          <!-- Communications -->
          <SidebarItem
            to="/communications"
            icon="fas fa-bullhorn"
            label="Comunicazione"
            :collapsed="!appStore.sidebarOpen"
          />

          <!-- Funding -->
          <SidebarItem
            to="/funding"
            icon="fas fa-coins"
            label="Finanziamenti"
            :collapsed="!appStore.sidebarOpen"
          />

          <!-- Reporting -->
          <SidebarItem
            to="/reporting"
            icon="fas fa-file-alt"
            label="Rendicontazione"
            :collapsed="!appStore.sidebarOpen"
          />

          <!-- Startup -->
          <SidebarItem
            to="/startup"
            icon="fas fa-rocket"
            label="Startup"
            :collapsed="!appStore.sidebarOpen"
          />

          <!-- Admin (only for admins) -->
          <SidebarItem
            v-if="authStore.isAdmin"
            to="/admin"
            icon="fas fa-cog"
            label="Admin"
            :collapsed="!appStore.sidebarOpen"
          />
        </nav>

        <!-- User Section -->
        <div class="flex-shrink-0 flex border-t border-brand-primary-800 dark:border-gray-700 p-2">
          <UserProfile :collapsed="!appStore.sidebarOpen" />
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Sidebar -->
  <MobileSidebar />
</template>

<script setup>
import { computed } from 'vue'
import { useAuthStore } from '../../stores/auth.js'
import { useAppStore } from '../../stores/app.js'
import { useBrandStore } from '../../stores/brand.js'

// Components
import SidebarItem from './SidebarItem.vue'
import SidebarGroup from './SidebarGroup.vue'
import UserProfile from './UserProfile.vue'
import MobileSidebar from './MobileSidebar.vue'

// Stores
const authStore = useAuthStore()
const appStore = useAppStore()
const brandStore = useBrandStore()

// Personnel submenu items
const personnelItems = computed(() => {
  const items = [
    { to: '/personnel', label: 'Team', icon: '👥' },
    { to: '/personnel/directory', label: 'Directory', icon: '📖' },
    { to: '/personnel/orgchart', label: 'Organigramma', icon: '🏢' },
    { to: '/personnel/skills', label: 'Competenze', icon: '🎯' }
  ]

  // Add admin-only items
  if (authStore.isManager) {
    items.push(
      { to: '/personnel/departments', label: 'Dipartimenti', icon: '🏢' },
      { to: '/personnel/admin', label: 'Amministrazione', icon: '⚙️' }
    )
  }

  return items
})
</script>

<style scoped>
/* Sidebar animations */
.sidebar-enter-active,
.sidebar-leave-active {
  transition: transform var(--brand-transition-normal);
}

.sidebar-enter-from,
.sidebar-leave-to {
  transform: translateX(-100%);
}

/* Custom scrollbar for sidebar */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: var(--brand-radius-full);
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Focus styles */
button:focus {
  outline: 2px solid var(--brand-primary-300);
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hidden.md\\:flex {
    display: none !important;
  }
}
</style>
