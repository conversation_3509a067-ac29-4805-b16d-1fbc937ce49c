<template>
  <div class="space-y-1">
    <!-- Group Header -->
    <button
      @click="toggleGroup"
      class="group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-150"
      :class="[
        isActive 
          ? 'text-white bg-brand-primary-800 dark:bg-gray-700' 
          : 'text-brand-primary-100 hover:bg-brand-primary-600 dark:text-gray-300 dark:hover:bg-gray-700',
        collapsed ? 'justify-center' : ''
      ]"
    >
      <!-- Icon -->
      <i 
        :class="[
          icon,
          collapsed ? 'mr-0' : 'mr-3',
          'h-6 w-6 flex-shrink-0 transition-colors duration-150'
        ]"
      ></i>
      
      <!-- Label -->
      <span 
        class="truncate flex-1 text-left transition-all duration-150"
        :class="{ 'hidden': collapsed }"
      >
        {{ label }}
      </span>
      
      <!-- Expand/Collapse Icon -->
      <i 
        v-if="!collapsed"
        class="fas fa-chevron-right ml-2 h-4 w-4 transition-transform duration-150"
        :class="{ 'rotate-90': isOpen }"
      ></i>
      
      <!-- Tooltip for collapsed state -->
      <div 
        v-if="collapsed"
        class="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 pointer-events-none group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap"
      >
        {{ label }}
        <div class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-full">
          <div class="border-4 border-transparent border-r-gray-900"></div>
        </div>
      </div>
    </button>

    <!-- Submenu Items -->
    <transition
      name="submenu"
      @enter="onEnter"
      @leave="onLeave"
    >
      <div 
        v-show="isOpen && !collapsed" 
        class="ml-6 space-y-1 overflow-hidden"
      >
        <router-link
          v-for="item in items"
          :key="item.to"
          :to="item.to"
          class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150"
          :class="[
            isItemActive(item.to)
              ? 'text-white bg-brand-primary-700 dark:bg-gray-600'
              : 'text-brand-primary-100 hover:bg-brand-primary-600 dark:text-gray-300 dark:hover:bg-gray-700'
          ]"
        >
          <!-- Item Icon/Emoji -->
          <span class="mr-2 text-sm">{{ item.icon }}</span>
          
          <!-- Item Label -->
          <span class="truncate">{{ item.label }}</span>
          
          <!-- Badge (optional) -->
          <span 
            v-if="item.badge"
            class="ml-auto inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-brand-secondary-500 text-white"
          >
            {{ item.badge }}
          </span>
        </router-link>
      </div>
    </transition>

    <!-- Mobile Submenu (when collapsed) -->
    <div 
      v-if="collapsed && isOpen"
      class="fixed left-20 top-0 bg-white dark:bg-gray-800 shadow-lg rounded-md py-2 z-50 min-w-48"
      :style="{ top: mobileMenuTop + 'px' }"
    >
      <router-link
        v-for="item in items"
        :key="item.to"
        :to="item.to"
        class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        @click="closeMobileMenu"
      >
        <span class="mr-2">{{ item.icon }}</span>
        {{ item.label }}
      </router-link>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { useRoute } from 'vue-router'

// Props
const props = defineProps({
  icon: {
    type: String,
    required: true
  },
  label: {
    type: String,
    required: true
  },
  items: {
    type: Array,
    required: true
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  defaultOpen: {
    type: Boolean,
    default: false
  }
})

// State
const isOpen = ref(props.defaultOpen)
const mobileMenuTop = ref(0)

// Route
const route = useRoute()

// Computed
const isActive = computed(() => {
  return props.items.some(item => route.path.startsWith(item.to))
})

// Auto-open if current route matches any item
if (isActive.value) {
  isOpen.value = true
}

// Methods
function toggleGroup() {
  if (props.collapsed) {
    // For collapsed sidebar, show mobile menu
    isOpen.value = !isOpen.value
    if (isOpen.value) {
      nextTick(() => {
        // Calculate position for mobile menu
        const button = event.currentTarget
        const rect = button.getBoundingClientRect()
        mobileMenuTop.value = rect.top
      })
    }
  } else {
    // Normal toggle
    isOpen.value = !isOpen.value
  }
}

function isItemActive(itemTo) {
  if (itemTo === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(itemTo)
}

function closeMobileMenu() {
  if (props.collapsed) {
    isOpen.value = false
  }
}

// Animation hooks
function onEnter(el) {
  el.style.height = '0'
  el.offsetHeight // force reflow
  el.style.height = el.scrollHeight + 'px'
}

function onLeave(el) {
  el.style.height = el.scrollHeight + 'px'
  el.offsetHeight // force reflow
  el.style.height = '0'
}

// Close mobile menu when clicking outside
if (typeof window !== 'undefined') {
  document.addEventListener('click', (event) => {
    if (props.collapsed && isOpen.value) {
      const mobileMenu = event.target.closest('.fixed')
      const button = event.target.closest('button')
      
      if (!mobileMenu && !button) {
        isOpen.value = false
      }
    }
  })
}
</script>

<style scoped>
/* Submenu animations */
.submenu-enter-active,
.submenu-leave-active {
  transition: height 0.3s ease;
  overflow: hidden;
}

.submenu-enter-from,
.submenu-leave-to {
  height: 0;
}

/* Chevron rotation */
.rotate-90 {
  transform: rotate(90deg);
}

/* Mobile menu positioning */
.fixed {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Group hover effects */
.group {
  position: relative;
}

/* Active state indicator */
.router-link-active {
  position: relative;
}

.router-link-active::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background-color: var(--brand-secondary-400);
  border-radius: 0 2px 2px 0;
}

/* Focus styles */
button:focus,
.router-link-active:focus {
  outline: 2px solid var(--brand-primary-300);
  outline-offset: 2px;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .transition-all,
  .transition-colors,
  .transition-transform,
  .submenu-enter-active,
  .submenu-leave-active {
    transition: none;
  }
  
  .fixed {
    animation: none;
  }
}
</style>
