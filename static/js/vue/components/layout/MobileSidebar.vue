<template>
  <!-- Mobile Sidebar Overlay -->
  <div 
    v-if="appStore.isMobile"
    class="md:hidden"
  >
    <!-- Backdrop -->
    <div 
      v-show="appStore.sidebarOpen"
      class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 transition-opacity duration-300"
      @click="appStore.setSidebarOpen(false)"
    ></div>
    
    <!-- Sidebar -->
    <div 
      class="fixed inset-y-0 left-0 flex flex-col w-64 bg-brand-primary-700 dark:bg-gray-800 z-50 transform transition-transform duration-300"
      :class="[
        appStore.sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      ]"
    >
      <!-- Header -->
      <div class="flex items-center justify-between p-4 border-b border-brand-primary-600 dark:border-gray-700">
        <div class="flex items-center">
          <img 
            :src="brandStore.currentLogo" 
            alt="Logo" 
            class="h-8 w-auto rounded-lg"
          >
          <span class="ml-2 text-white font-bold text-lg">
            {{ brandStore.brandConfig.name }}
          </span>
        </div>
        <button
          @click="appStore.setSidebarOpen(false)"
          class="text-white hover:text-brand-primary-200 transition-colors"
        >
          <i class="fas fa-times h-6 w-6"></i>
        </button>
      </div>
      
      <!-- Navigation -->
      <nav class="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
        <!-- Dashboard -->
        <MobileSidebarItem
          to="/"
          icon="fas fa-home"
          label="Dashboard"
          @click="closeSidebar"
        />

        <!-- Personnel -->
        <MobileSidebarItem
          to="/personnel"
          icon="fas fa-users"
          label="Personale"
          @click="closeSidebar"
        />

        <!-- Projects -->
        <MobileSidebarItem
          to="/projects"
          icon="fas fa-project-diagram"
          label="Progetti"
          @click="closeSidebar"
        />

        <!-- CRM -->
        <MobileSidebarItem
          to="/crm"
          icon="fas fa-handshake"
          label="CRM"
          @click="closeSidebar"
        />

        <!-- Performance -->
        <MobileSidebarItem
          to="/performance"
          icon="fas fa-chart-line"
          label="Performance"
          @click="closeSidebar"
        />

        <!-- Admin (only for admins) -->
        <MobileSidebarItem
          v-if="authStore.isAdmin"
          to="/admin"
          icon="fas fa-cog"
          label="Admin"
          @click="closeSidebar"
        />
      </nav>
      
      <!-- User Section -->
      <div class="border-t border-brand-primary-600 dark:border-gray-700 p-4">
        <div class="flex items-center">
          <div class="h-10 w-10 rounded-full bg-brand-primary-500 flex items-center justify-center text-white font-medium">
            {{ authStore.userInitials }}
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-white">
              {{ authStore.userFullName }}
            </p>
            <button
              @click="logout"
              class="text-xs text-brand-primary-200 hover:text-white transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAppStore } from '../../stores/app.js'
import { useAuthStore } from '../../stores/auth.js'
import { useBrandStore } from '../../stores/brand.js'
import MobileSidebarItem from './MobileSidebarItem.vue'

// Stores
const appStore = useAppStore()
const authStore = useAuthStore()
const brandStore = useBrandStore()

// Methods
function closeSidebar() {
  appStore.setSidebarOpen(false)
}

function logout() {
  authStore.logout()
}
</script>

<style scoped>
/* Sidebar animations */
.transform.transition-transform {
  transition: transform 0.3s ease-in-out;
}

/* Backdrop animation */
.transition-opacity {
  transition: opacity 0.3s ease-in-out;
}

/* Custom scrollbar */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}
</style>
