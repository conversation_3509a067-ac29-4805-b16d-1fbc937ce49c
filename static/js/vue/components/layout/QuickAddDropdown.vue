<template>
  <div class="relative" v-click-outside="closeDropdown">
    <!-- Quick Add Button -->
    <button
      @click="toggleDropdown"
      class="p-2 rounded-full text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-brand-primary-500 transition-colors"
      title="Aggiungi nuovo"
    >
      <i class="fas fa-plus h-5 w-5"></i>
    </button>

    <!-- Dropdown -->
    <transition name="dropdown">
      <div 
        v-if="isOpen"
        class="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50"
      >
        <div class="py-1">
          <!-- Project -->
          <button
            @click="quickAdd('project')"
            class="flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <i class="fas fa-project-diagram mr-3 h-4 w-4 text-brand-primary-500"></i>
            Nuovo Progetto
          </button>

          <!-- Task -->
          <button
            @click="quickAdd('task')"
            class="flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <i class="fas fa-tasks mr-3 h-4 w-4 text-brand-secondary-500"></i>
            Nuova Attività
          </button>

          <!-- Client -->
          <button
            @click="quickAdd('client')"
            class="flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <i class="fas fa-handshake mr-3 h-4 w-4 text-brand-accent-500"></i>
            Nuovo Cliente
          </button>

          <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>

          <!-- User (Admin only) -->
          <button
            v-if="authStore.isAdmin"
            @click="quickAdd('user')"
            class="flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <i class="fas fa-user-plus mr-3 h-4 w-4 text-brand-success-500"></i>
            Nuovo Utente
          </button>

          <!-- Department (Manager only) -->
          <button
            v-if="authStore.isManager"
            @click="quickAdd('department')"
            class="flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <i class="fas fa-building mr-3 h-4 w-4 text-brand-warning-500"></i>
            Nuovo Dipartimento
          </button>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth.js'
import { useAppStore } from '../../stores/app.js'

// Stores
const authStore = useAuthStore()
const appStore = useAppStore()
const router = useRouter()

// State
const isOpen = ref(false)

// Methods
function toggleDropdown() {
  isOpen.value = !isOpen.value
}

function closeDropdown() {
  isOpen.value = false
}

function quickAdd(type) {
  closeDropdown()
  
  switch (type) {
    case 'project':
      // Show project creation modal
      appStore.showModal('ProjectCreateModal', {}, {
        title: 'Nuovo Progetto',
        size: 'lg'
      })
      break
      
    case 'task':
      // Show task creation modal
      appStore.showModal('TaskCreateModal', {}, {
        title: 'Nuova Attività',
        size: 'md'
      })
      break
      
    case 'client':
      // Navigate to CRM with create mode
      router.push('/crm/clients?action=create')
      break
      
    case 'user':
      // Show user creation modal
      appStore.showModal('UserCreateModal', {}, {
        title: 'Nuovo Utente',
        size: 'lg'
      })
      break
      
    case 'department':
      // Navigate to departments with create mode
      router.push('/personnel/departments?action=create')
      break
      
    default:
      console.warn('Unknown quick add type:', type)
  }
}
</script>

<style scoped>
/* Dropdown animations */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Button hover effects */
button:hover i {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

/* Focus styles */
button:focus {
  outline: 2px solid var(--brand-primary-500);
  outline-offset: 2px;
}
</style>
