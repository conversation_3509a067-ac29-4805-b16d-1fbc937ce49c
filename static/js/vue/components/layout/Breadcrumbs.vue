<template>
  <nav class="flex mb-6" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-3">
      <li 
        v-for="(item, index) in items" 
        :key="index"
        class="inline-flex items-center"
      >
        <!-- Separator (except for first item) -->
        <i 
          v-if="index > 0"
          class="fas fa-chevron-right text-gray-400 mx-2 h-3 w-3"
        ></i>
        
        <!-- Breadcrumb Item -->
        <router-link
          v-if="item.path && index < items.length - 1"
          :to="item.path"
          class="inline-flex items-center text-sm font-medium text-brand-text-secondary hover:text-brand-primary-600 dark:hover:text-brand-primary-400 transition-colors"
        >
          <i 
            v-if="index === 0 && item.icon"
            :class="[item.icon, 'mr-2 h-4 w-4']"
          ></i>
          {{ item.name }}
        </router-link>
        
        <!-- Current Page (no link) -->
        <span
          v-else
          class="inline-flex items-center text-sm font-medium text-brand-text-primary"
          :class="{ 'text-brand-primary-600 dark:text-brand-primary-400': index === items.length - 1 }"
        >
          <i 
            v-if="index === 0 && item.icon"
            :class="[item.icon, 'mr-2 h-4 w-4']"
          ></i>
          {{ item.name }}
        </span>
      </li>
    </ol>
  </nav>
</template>

<script setup>
// Props
defineProps({
  items: {
    type: Array,
    required: true,
    default: () => []
  }
})
</script>

<style scoped>
/* Hover effects */
.router-link:hover {
  text-decoration: none;
}

/* Focus styles */
.router-link:focus {
  outline: 2px solid var(--brand-primary-500);
  outline-offset: 2px;
  border-radius: var(--brand-radius-sm);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .md\\:space-x-3 > * + * {
    margin-left: 0.25rem;
  }
  
  .text-sm {
    font-size: 0.75rem;
  }
}
</style>
