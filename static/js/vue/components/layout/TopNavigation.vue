<template>
  <header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 h-header">
    <div class="flex items-center justify-between px-4 sm:px-6 lg:px-8 h-full">
      <!-- Left Section -->
      <div class="flex items-center">
        <!-- Mobile Menu Button -->
        <button
          @click="appStore.toggleSidebar()"
          class="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-brand-primary-500"
        >
          <i class="fas fa-bars h-6 w-6"></i>
        </button>

        <!-- Page Title -->
        <div class="ml-4 md:ml-0">
          <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
            {{ appStore.pageTitle }}
          </h1>
          <p v-if="pageSubtitle" class="text-sm text-gray-500 dark:text-gray-400">
            {{ pageSubtitle }}
          </p>
        </div>
      </div>

      <!-- Right Section -->
      <div class="flex items-center space-x-4">
        <!-- Search -->
        <div class="hidden lg:block">
          <div class="relative">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Cerca..."
              class="input-brand w-64 pl-10 pr-4 py-2 text-sm"
              @keyup.enter="performSearch"
            >
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-search h-4 w-4 text-gray-400"></i>
            </div>
            <button
              v-if="searchQuery"
              @click="clearSearch"
              class="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <i class="fas fa-times h-4 w-4 text-gray-400 hover:text-gray-600"></i>
            </button>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="flex items-center space-x-2">
          <!-- Notifications -->
          <NotificationDropdown />

          <!-- Dark Mode Toggle -->
          <button
            @click="appStore.toggleDarkMode()"
            class="p-2 rounded-full text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-brand-primary-500 transition-colors"
            :title="appStore.darkMode ? 'Modalità chiara' : 'Modalità scura'"
          >
            <i 
              :class="appStore.darkMode ? 'fas fa-sun' : 'fas fa-moon'"
              class="h-5 w-5"
            ></i>
          </button>

          <!-- Quick Add Button -->
          <QuickAddDropdown />

          <!-- User Menu -->
          <UserDropdown />
        </div>
      </div>
    </div>

    <!-- Mobile Search (shown when search is active) -->
    <div 
      v-if="showMobileSearch"
      class="lg:hidden border-t border-gray-200 dark:border-gray-700 px-4 py-3"
    >
      <div class="relative">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Cerca..."
          class="input-brand w-full pl-10 pr-4 py-2 text-sm"
          @keyup.enter="performSearch"
          ref="mobileSearchInput"
        >
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <i class="fas fa-search h-4 w-4 text-gray-400"></i>
        </div>
        <button
          @click="closeMobileSearch"
          class="absolute inset-y-0 right-0 pr-3 flex items-center"
        >
          <i class="fas fa-times h-4 w-4 text-gray-400 hover:text-gray-600"></i>
        </button>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '../../stores/app.js'
import { useAuthStore } from '../../stores/auth.js'

// Components
import NotificationDropdown from './NotificationDropdown.vue'
import QuickAddDropdown from './QuickAddDropdown.vue'
import UserDropdown from './UserDropdown.vue'

// Stores
const appStore = useAppStore()
const authStore = useAuthStore()
const route = useRoute()

// State
const searchQuery = ref('')
const showMobileSearch = ref(false)
const mobileSearchInput = ref(null)

// Computed
const pageSubtitle = computed(() => {
  // Generate subtitle based on current route
  const routeMeta = route.meta
  if (routeMeta?.subtitle) {
    return routeMeta.subtitle
  }
  
  // Auto-generate subtitle based on user info
  if (route.name === 'dashboard') {
    return `Benvenuto, ${authStore.userFullName}`
  }
  
  return null
})

// Methods
function performSearch() {
  if (!searchQuery.value.trim()) return
  
  // Implement global search
  console.log('Searching for:', searchQuery.value)
  
  // You can emit an event or call a search service here
  appStore.showNotification(`Ricerca per: "${searchQuery.value}"`, 'info', 3000)
  
  // Close mobile search
  if (showMobileSearch.value) {
    closeMobileSearch()
  }
}

function clearSearch() {
  searchQuery.value = ''
}

function openMobileSearch() {
  showMobileSearch.value = true
  nextTick(() => {
    mobileSearchInput.value?.focus()
  })
}

function closeMobileSearch() {
  showMobileSearch.value = false
  searchQuery.value = ''
}

// Keyboard shortcuts
function handleKeydown(event) {
  // Ctrl/Cmd + K to open search
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    if (appStore.isMobile) {
      openMobileSearch()
    } else {
      // Focus desktop search
      const searchInput = document.querySelector('input[placeholder="Cerca..."]')
      searchInput?.focus()
    }
  }
  
  // Escape to close mobile search
  if (event.key === 'Escape' && showMobileSearch.value) {
    closeMobileSearch()
  }
}

// Add keyboard event listener
if (typeof window !== 'undefined') {
  window.addEventListener('keydown', handleKeydown)
}
</script>

<style scoped>
/* Header animations */
.header-enter-active,
.header-leave-active {
  transition: all var(--brand-transition-normal);
}

.header-enter-from,
.header-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Search input focus effects */
.input-brand:focus {
  box-shadow: 0 0 0 3px rgba(var(--brand-primary-500), 0.1);
}

/* Mobile search animation */
.mobile-search-enter-active,
.mobile-search-leave-active {
  transition: all 0.3s ease;
}

.mobile-search-enter-from,
.mobile-search-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Button hover effects */
button:hover i {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

/* Search suggestions (if implemented) */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--brand-border-primary);
  border-top: none;
  border-radius: 0 0 var(--brand-radius-md) var(--brand-radius-md);
  box-shadow: var(--brand-shadow-lg);
  z-index: 50;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .text-xl {
    font-size: 1.125rem;
  }
  
  .space-x-4 > * + * {
    margin-left: 0.5rem;
  }
}

/* Focus management */
button:focus,
input:focus {
  outline: 2px solid var(--brand-primary-500);
  outline-offset: 2px;
}

/* Dark mode specific styles */
.dark .input-brand {
  background-color: var(--brand-bg-secondary);
  border-color: var(--brand-border-secondary);
  color: var(--brand-text-primary);
}

.dark .input-brand::placeholder {
  color: var(--brand-text-tertiary);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .transition-colors,
  .header-enter-active,
  .header-leave-active,
  .mobile-search-enter-active,
  .mobile-search-leave-active {
    transition: none;
  }
  
  button:hover i {
    transform: none;
  }
}

/* Print styles */
@media print {
  header {
    display: none !important;
  }
}
</style>
