<template>
  <div class="flex-shrink-0 w-full group block">
    <div class="flex items-center" :class="{ 'justify-center': collapsed }">
      <!-- Avatar -->
      <div class="inline-block h-9 w-9 rounded-full" :class="{ 'mr-3': !collapsed }">
        <img 
          v-if="authStore.user?.profile_image"
          class="h-9 w-9 rounded-full object-cover"
          :src="authStore.user.profile_image" 
          :alt="authStore.userFullName"
        >
        <div 
          v-else
          class="h-9 w-9 rounded-full bg-brand-primary-500 dark:bg-brand-primary-600 flex items-center justify-center text-white text-sm font-medium uppercase"
        >
          {{ authStore.userInitials }}
        </div>
      </div>
      
      <!-- User Info -->
      <div :class="{ 'hidden': collapsed }">
        <p class="text-sm font-medium text-white">
          {{ authStore.userFullName }}
        </p>
        <div class="flex items-center space-x-2">
          <span class="text-xs text-brand-primary-200">
            {{ authStore.userRole }}
          </span>
          <button
            @click="logout"
            class="text-xs font-medium text-brand-primary-200 hover:text-brand-primary-100 transition-colors"
          >
            Logout
          </button>
        </div>
      </div>
      
      <!-- Tooltip for collapsed state -->
      <div 
        v-if="collapsed"
        class="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 pointer-events-none group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap"
      >
        {{ authStore.userFullName }}
        <div class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-full">
          <div class="border-4 border-transparent border-r-gray-900"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAuthStore } from '../../stores/auth.js'

// Props
defineProps({
  collapsed: {
    type: Boolean,
    default: false
  }
})

// Store
const authStore = useAuthStore()

// Methods
function logout() {
  authStore.logout()
}
</script>

<style scoped>
/* Hover effects */
.group:hover .text-brand-primary-200 {
  color: rgb(var(--brand-primary-100));
}

/* Avatar animation */
.h-9.w-9 {
  transition: transform 0.2s ease;
}

.group:hover .h-9.w-9 {
  transform: scale(1.05);
}

/* Tooltip positioning */
.group {
  position: relative;
}

/* Focus styles */
button:focus {
  outline: 2px solid var(--brand-primary-300);
  outline-offset: 2px;
  border-radius: var(--brand-radius-sm);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .transition-colors,
  .transition-opacity,
  .h-9.w-9,
  .group:hover .h-9.w-9 {
    transition: none;
    transform: none;
  }
}
</style>
