<template>
  <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-auto">
    <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <!-- Left Section -->
        <div class="flex items-center space-x-4">
          <p class="text-sm text-brand-text-secondary">
            © {{ currentYear }} {{ brandStore.brandConfig.name }}. Tutti i diritti riservati.
          </p>
          <span class="hidden md:inline text-brand-text-tertiary">|</span>
          <p class="text-xs text-brand-text-tertiary">
            Versione {{ appVersion }}
          </p>
        </div>

        <!-- Right Section -->
        <div class="mt-2 md:mt-0 flex items-center space-x-4">
          <!-- Status Indicator -->
          <div class="flex items-center space-x-2">
            <div 
              class="h-2 w-2 rounded-full"
              :class="appStore.isOnline ? 'bg-brand-success-500' : 'bg-brand-error-500'"
            ></div>
            <span class="text-xs text-brand-text-tertiary">
              {{ appStore.isOnline ? 'Online' : 'Offline' }}
            </span>
          </div>

          <!-- Links -->
          <div class="flex items-center space-x-3 text-xs">
            <a 
              href="/privacy" 
              class="text-brand-text-tertiary hover:text-brand-primary-600 dark:hover:text-brand-primary-400 transition-colors"
            >
              Privacy
            </a>
            <a 
              href="/terms" 
              class="text-brand-text-tertiary hover:text-brand-primary-600 dark:hover:text-brand-primary-400 transition-colors"
            >
              Termini
            </a>
            <a 
              href="/support" 
              class="text-brand-text-tertiary hover:text-brand-primary-600 dark:hover:text-brand-primary-400 transition-colors"
            >
              Supporto
            </a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { computed } from 'vue'
import { useAppStore } from '../../stores/app.js'
import { useBrandStore } from '../../stores/brand.js'

// Stores
const appStore = useAppStore()
const brandStore = useBrandStore()

// Computed
const currentYear = computed(() => new Date().getFullYear())
const appVersion = computed(() => window.APP_CONFIG?.version || '1.0.0')
</script>

<style scoped>
/* Footer styling */
footer {
  flex-shrink: 0;
}

/* Link hover effects */
a:hover {
  text-decoration: none;
}

/* Focus styles */
a:focus {
  outline: 2px solid var(--brand-primary-500);
  outline-offset: 2px;
  border-radius: var(--brand-radius-sm);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .flex-col.md\\:flex-row {
    text-align: center;
  }
  
  .space-x-4 > * + * {
    margin-left: 0.5rem;
  }
}

/* Print styles */
@media print {
  footer {
    display: none !important;
  }
}
</style>
