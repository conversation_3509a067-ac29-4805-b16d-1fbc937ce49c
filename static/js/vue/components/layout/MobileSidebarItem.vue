<template>
  <router-link
    :to="to"
    class="group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors duration-150"
    :class="[
      isActive 
        ? 'text-white bg-brand-primary-800 dark:bg-gray-700' 
        : 'text-brand-primary-100 hover:bg-brand-primary-600 dark:text-gray-300 dark:hover:bg-gray-700'
    ]"
    @click="$emit('click')"
  >
    <i :class="[icon, 'mr-4 h-6 w-6']"></i>
    {{ label }}
  </router-link>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

// Props
const props = defineProps({
  to: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    required: true
  },
  label: {
    type: String,
    required: true
  }
})

// Emits
defineEmits(['click'])

// Route
const route = useRoute()

// Computed
const isActive = computed(() => {
  if (props.to === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(props.to)
})
</script>

<style scoped>
/* Active state indicator */
.router-link-active {
  position: relative;
}

.router-link-active::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: var(--brand-secondary-400);
  border-radius: 0 2px 2px 0;
}
</style>
