<template>
  <footer class="bg-gray-900 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="col-span-1 md:col-span-2">
          <div class="flex items-center mb-4">
            <img 
              :src="brandStore.brandConfig.logos.white || brandStore.currentLogo" 
              alt="DatVinci" 
              class="h-8 w-auto"
            >
            <span class="ml-2 text-xl font-bold">
              {{ brandStore.brandConfig.name }}
            </span>
          </div>
          <p class="text-gray-300 mb-4">
            Supportiamo le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all'avanguardia.
          </p>
          <div class="flex space-x-4">
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fab fa-linkedin text-xl"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fab fa-twitter text-xl"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fab fa-facebook text-xl"></i>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div>
          <h3 class="text-lg font-semibold mb-4">Link Rapidi</h3>
          <ul class="space-y-2">
            <li>
              <router-link to="/" class="text-gray-300 hover:text-white transition-colors">
                Home
              </router-link>
            </li>
            <li>
              <router-link to="/services" class="text-gray-300 hover:text-white transition-colors">
                Servizi
              </router-link>
            </li>
            <li>
              <router-link to="/about" class="text-gray-300 hover:text-white transition-colors">
                Chi Siamo
              </router-link>
            </li>
            <li>
              <router-link to="/contact" class="text-gray-300 hover:text-white transition-colors">
                Contatti
              </router-link>
            </li>
          </ul>
        </div>

        <!-- Contact Info -->
        <div>
          <h3 class="text-lg font-semibold mb-4">Contatti</h3>
          <ul class="space-y-2 text-gray-300">
            <li class="flex items-center">
              <i class="fas fa-map-marker-alt mr-2"></i>
              Via dell'Innovazione 123, Milano
            </li>
            <li class="flex items-center">
              <i class="fas fa-phone mr-2"></i>
              +39 02 1234567
            </li>
            <li class="flex items-center">
              <i class="fas fa-envelope mr-2"></i>
              <EMAIL>
            </li>
          </ul>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
        <p class="text-gray-400 text-sm">
          © {{ currentYear }} {{ brandStore.brandConfig.name }}. Tutti i diritti riservati.
        </p>
        <div class="flex space-x-4 mt-4 md:mt-0">
          <router-link to="/privacy" class="text-gray-400 hover:text-white text-sm transition-colors">
            Privacy Policy
          </router-link>
          <span class="text-gray-600">|</span>
          <a href="/terms" class="text-gray-400 hover:text-white text-sm transition-colors">
            Termini di Servizio
          </a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { computed } from 'vue'
import { useBrandStore } from '../../stores/brand.js'

// Store
const brandStore = useBrandStore()

// Computed
const currentYear = computed(() => new Date().getFullYear())
</script>

<style scoped>
/* Link hover effects */
a:hover {
  text-decoration: none;
}

/* Social icons hover */
.fab:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

/* Focus styles */
a:focus {
  outline: 2px solid var(--brand-primary-500);
  outline-offset: 2px;
  border-radius: 2px;
}
</style>
