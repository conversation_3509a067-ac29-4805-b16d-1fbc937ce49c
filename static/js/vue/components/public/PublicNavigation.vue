<template>
  <nav class="bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center">
            <img 
              :src="brandStore.currentLogo" 
              alt="DatVinci" 
              class="h-8 w-auto"
            >
            <span class="ml-2 text-xl font-bold text-brand-primary-600">
              {{ brandStore.brandConfig.name }}
            </span>
          </router-link>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-8">
          <router-link
            to="/"
            class="text-brand-text-secondary hover:text-brand-primary-600 transition-colors"
            :class="{ 'text-brand-primary-600 font-medium': $route.name === 'home' }"
          >
            Home
          </router-link>
          <router-link
            to="/services"
            class="text-brand-text-secondary hover:text-brand-primary-600 transition-colors"
            :class="{ 'text-brand-primary-600 font-medium': $route.name === 'services' }"
          >
            Servizi
          </router-link>
          <router-link
            to="/about"
            class="text-brand-text-secondary hover:text-brand-primary-600 transition-colors"
            :class="{ 'text-brand-primary-600 font-medium': $route.name === 'about' }"
          >
            Chi Siamo
          </router-link>
          <router-link
            to="/contact"
            class="text-brand-text-secondary hover:text-brand-primary-600 transition-colors"
            :class="{ 'text-brand-primary-600 font-medium': $route.name === 'contact' }"
          >
            Contatti
          </router-link>
          
          <!-- Login Button -->
          <a
            href="/auth/login"
            class="bg-brand-primary-600 text-white px-4 py-2 rounded-md hover:bg-brand-primary-700 transition-colors"
          >
            Accedi
          </a>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden flex items-center">
          <button
            @click="mobileMenuOpen = !mobileMenuOpen"
            class="text-brand-text-secondary hover:text-brand-primary-600 focus:outline-none focus:text-brand-primary-600"
          >
            <i :class="mobileMenuOpen ? 'fas fa-times' : 'fas fa-bars'" class="h-6 w-6"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Navigation -->
    <div v-if="mobileMenuOpen" class="md:hidden bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
      <div class="px-2 pt-2 pb-3 space-y-1">
        <router-link
          to="/"
          @click="mobileMenuOpen = false"
          class="block px-3 py-2 text-brand-text-secondary hover:text-brand-primary-600 transition-colors"
          :class="{ 'text-brand-primary-600 font-medium': $route.name === 'home' }"
        >
          Home
        </router-link>
        <router-link
          to="/services"
          @click="mobileMenuOpen = false"
          class="block px-3 py-2 text-brand-text-secondary hover:text-brand-primary-600 transition-colors"
          :class="{ 'text-brand-primary-600 font-medium': $route.name === 'services' }"
        >
          Servizi
        </router-link>
        <router-link
          to="/about"
          @click="mobileMenuOpen = false"
          class="block px-3 py-2 text-brand-text-secondary hover:text-brand-primary-600 transition-colors"
          :class="{ 'text-brand-primary-600 font-medium': $route.name === 'about' }"
        >
          Chi Siamo
        </router-link>
        <router-link
          to="/contact"
          @click="mobileMenuOpen = false"
          class="block px-3 py-2 text-brand-text-secondary hover:text-brand-primary-600 transition-colors"
          :class="{ 'text-brand-primary-600 font-medium': $route.name === 'contact' }"
        >
          Contatti
        </router-link>
        <a
          href="/auth/login"
          class="block px-3 py-2 bg-brand-primary-600 text-white rounded-md hover:bg-brand-primary-700 transition-colors mx-3 mt-4 text-center"
        >
          Accedi
        </a>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref } from 'vue'
import { useBrandStore } from '../../stores/brand.js'

// Store
const brandStore = useBrandStore()

// State
const mobileMenuOpen = ref(false)
</script>

<style scoped>
/* Sticky navigation */
.sticky {
  backdrop-filter: blur(10px);
}

/* Active link styles */
.router-link-active {
  color: var(--brand-primary-600);
  font-weight: 500;
}

/* Mobile menu animation */
.md\\:hidden > div {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus styles */
button:focus,
a:focus {
  outline: 2px solid var(--brand-primary-500);
  outline-offset: 2px;
}
</style>
