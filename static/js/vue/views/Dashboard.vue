<template>
  <div class="py-6 animate-brand-fade-in">
    <!-- Dashboard Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
      <div>
        <h1 class="text-2xl font-bold text-brand-text-primary">Dashboard</h1>
        <p class="mt-1 text-sm text-brand-text-secondary">
          Benvenuto, {{ authStore.userFullName }}! Ecco una panoramica delle attività della tua azienda.
        </p>
      </div>
      <div class="mt-4 md:mt-0 flex space-x-3">
        <select 
          v-model="selectedPeriod"
          @change="loadDashboardData"
          class="input-brand"
        >
          <option value="7">Ultimi 7 giorni</option>
          <option value="30">Ultimo mese</option>
          <option value="90">Ultimi 3 mesi</option>
        </select>
        <button 
          @click="refreshData"
          :disabled="isLoading"
          class="btn-brand-primary"
        >
          <i 
            :class="['fas fa-sync-alt mr-2', { 'animate-spin': isLoading }]"
          ></i>
          Aggiorna
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatsCard
        v-for="stat in stats"
        :key="stat.id"
        :title="stat.title"
        :value="stat.value"
        :icon="stat.icon"
        :color="stat.color"
        :trend="stat.trend"
        :link="stat.link"
        class="animate-brand-slide-up"
        :style="{ animationDelay: `${stat.id * 100}ms` }"
      />
    </div>

    <!-- Charts and Activity Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Project Status Chart -->
      <ChartCard
        title="Stato Progetti"
        :loading="chartsLoading"
        class="animate-brand-slide-up"
        style="animation-delay: 400ms"
      >
        <template #actions>
          <select 
            v-model="projectChartFilter"
            @change="loadProjectChart"
            class="text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 focus:ring-brand-primary-500 focus:border-brand-primary-500 dark:text-gray-300"
          >
            <option value="all">Tutti</option>
            <option value="quarter">Ultimo trimestre</option>
            <option value="year">Anno corrente</option>
          </select>
        </template>
        
        <DoughnutChart
          v-if="projectChartData"
          :data="projectChartData"
          :options="chartOptions.doughnut"
        />
      </ChartCard>

      <!-- Task Status Chart -->
      <ChartCard
        title="Stato Attività"
        :loading="chartsLoading"
        class="animate-brand-slide-up"
        style="animation-delay: 500ms"
      >
        <template #actions>
          <select 
            v-model="taskChartFilter"
            @change="loadTaskChart"
            class="text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 focus:ring-brand-primary-500 focus:border-brand-primary-500 dark:text-gray-300"
          >
            <option value="all">Tutte</option>
            <option value="active">Progetti attivi</option>
            <option value="mine">Le mie attività</option>
          </select>
        </template>
        
        <BarChart
          v-if="taskChartData"
          :data="taskChartData"
          :options="chartOptions.bar"
        />
      </ChartCard>
    </div>

    <!-- Tasks, News and Calendar Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Upcoming Tasks -->
      <ActivityCard
        title="Attività in Scadenza"
        icon="fas fa-tasks"
        :items="upcomingTasks"
        :loading="tasksLoading"
        link="/projects"
        linkText="Vedi tutti i progetti"
        class="animate-brand-slide-up"
        style="animation-delay: 600ms"
      >
        <template #item="{ item }">
          <TaskItem :task="item" />
        </template>
      </ActivityCard>

      <!-- Latest News -->
      <ActivityCard
        title="Ultime Notizie"
        icon="fas fa-newspaper"
        :items="latestNews"
        :loading="newsLoading"
        link="/communications/news"
        linkText="Vedi tutte le notizie"
        class="animate-brand-slide-up"
        style="animation-delay: 700ms"
      >
        <template #item="{ item }">
          <NewsItem :news="item" />
        </template>
      </ActivityCard>

      <!-- Upcoming Events -->
      <ActivityCard
        title="Eventi Imminenti"
        icon="fas fa-calendar"
        :items="upcomingEvents"
        :loading="eventsLoading"
        link="#"
        linkText="Visualizza calendario"
        class="animate-brand-slide-up"
        style="animation-delay: 800ms"
      >
        <template #item="{ item }">
          <EventItem :event="item" />
        </template>
      </ActivityCard>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '../stores/auth.js'
import { useAppStore } from '../stores/app.js'

// Components
import StatsCard from '../components/dashboard/StatsCard.vue'
import ChartCard from '../components/dashboard/ChartCard.vue'
import ActivityCard from '../components/dashboard/ActivityCard.vue'
import TaskItem from '../components/dashboard/TaskItem.vue'
import NewsItem from '../components/dashboard/NewsItem.vue'
import EventItem from '../components/dashboard/EventItem.vue'
import DoughnutChart from '../components/charts/DoughnutChart.vue'
import BarChart from '../components/charts/BarChart.vue'

// Stores
const authStore = useAuthStore()
const appStore = useAppStore()

// State
const isLoading = ref(false)
const chartsLoading = ref(false)
const tasksLoading = ref(false)
const newsLoading = ref(false)
const eventsLoading = ref(false)

const selectedPeriod = ref('30')
const projectChartFilter = ref('all')
const taskChartFilter = ref('all')

// Data
const stats = ref([])
const projectChartData = ref(null)
const taskChartData = ref(null)
const upcomingTasks = ref([])
const latestNews = ref([])
const upcomingEvents = ref([])

// Chart options
const chartOptions = {
  doughnut: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15,
          font: { size: 12 }
        }
      }
    },
    cutout: '70%'
  },
  bar: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: { color: 'rgba(156, 163, 175, 0.1)' },
        ticks: { precision: 0 }
      },
      x: {
        grid: { display: false }
      }
    }
  }
}

// Methods
async function loadDashboardData() {
  isLoading.value = true
  
  try {
    // Load stats
    await loadStats()
    
    // Load charts
    await Promise.all([
      loadProjectChart(),
      loadTaskChart()
    ])
    
    // Load activities
    await Promise.all([
      loadUpcomingTasks(),
      loadLatestNews(),
      loadUpcomingEvents()
    ])
    
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
    appStore.handleApiError(error, 'Dashboard')
  } finally {
    isLoading.value = false
  }
}

async function loadStats() {
  try {
    const response = await fetch(`/api/dashboard/stats?period=${selectedPeriod.value}`)
    if (response.ok) {
      const data = await response.json()
      stats.value = data.data.stats
    }
  } catch (error) {
    console.error('Failed to load stats:', error)
  }
}

async function loadProjectChart() {
  chartsLoading.value = true
  try {
    const response = await fetch(`/api/dashboard/charts/projects?filter=${projectChartFilter.value}`)
    if (response.ok) {
      const data = await response.json()
      projectChartData.value = data.data
    }
  } catch (error) {
    console.error('Failed to load project chart:', error)
  } finally {
    chartsLoading.value = false
  }
}

async function loadTaskChart() {
  chartsLoading.value = true
  try {
    const response = await fetch(`/api/dashboard/charts/tasks?filter=${taskChartFilter.value}`)
    if (response.ok) {
      const data = await response.json()
      taskChartData.value = data.data
    }
  } catch (error) {
    console.error('Failed to load task chart:', error)
  } finally {
    chartsLoading.value = false
  }
}

async function loadUpcomingTasks() {
  tasksLoading.value = true
  try {
    const response = await fetch('/api/dashboard/tasks/upcoming?limit=5')
    if (response.ok) {
      const data = await response.json()
      upcomingTasks.value = data.data.tasks
    }
  } catch (error) {
    console.error('Failed to load upcoming tasks:', error)
  } finally {
    tasksLoading.value = false
  }
}

async function loadLatestNews() {
  newsLoading.value = true
  try {
    const response = await fetch('/api/dashboard/news?limit=5')
    if (response.ok) {
      const data = await response.json()
      latestNews.value = data.data.news
    }
  } catch (error) {
    console.error('Failed to load latest news:', error)
  } finally {
    newsLoading.value = false
  }
}

async function loadUpcomingEvents() {
  eventsLoading.value = true
  try {
    const response = await fetch('/api/dashboard/events/upcoming?limit=5')
    if (response.ok) {
      const data = await response.json()
      upcomingEvents.value = data.data.events
    }
  } catch (error) {
    console.error('Failed to load upcoming events:', error)
  } finally {
    eventsLoading.value = false
  }
}

async function refreshData() {
  await loadDashboardData()
  appStore.showNotification('Dashboard aggiornata', 'success', 2000)
}

// Lifecycle
onMounted(() => {
  // Set page info
  appStore.setCurrentPage('dashboard', 'Dashboard', [
    { name: 'Dashboard', path: '/' }
  ])
  
  // Load initial data
  loadDashboardData()
})
</script>

<style scoped>
/* Animation delays for staggered entrance */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-brand-slide-up {
  animation: slideUp 0.6s ease-out forwards;
  opacity: 0;
}

/* Responsive grid adjustments */
@media (max-width: 640px) {
  .grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-4 {
    gap: 1rem;
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.dark .loading-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

/* Chart container */
.chart-container {
  position: relative;
  height: 300px;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .animate-brand-slide-up,
  .animate-brand-fade-in {
    animation: none;
    opacity: 1;
    transform: none;
  }
  
  .loading-shimmer {
    animation: none;
  }
}
</style>
