<template>
  <div class="min-h-screen bg-white dark:bg-gray-900">
    <PublicNavigation />
    
    <!-- Hero Section -->
    <section class="bg-brand-primary-600 text-white py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl font-bold mb-4">
          {{ tenantStore.getPageContent('about', 'hero', 'title') || 'Chi Siamo' }}
        </h1>
        <p class="text-xl text-brand-primary-100">
          {{ tenantStore.getPageContent('about', 'hero', 'subtitle') || 'La nostra storia e i nostri valori' }}
        </p>
      </div>
    </section>

    <!-- Content -->
    <section class="py-16">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="prose prose-lg max-w-none">
          <h2>{{ tenantStore.getPageContent('about', 'story_section', 'title') || 'La nostra storia' }}</h2>
          <p>{{ tenantStore.getPageContent('about', 'story_section', 'content') || 'Contenuto della storia aziendale...' }}</p>
          
          <h2>{{ tenantStore.getPageContent('about', 'mission_section', 'title') || 'La nostra missione' }}</h2>
          <p>{{ tenantStore.getPageContent('about', 'mission_section', 'content') || 'La nostra missione...' }}</p>
          
          <h2>{{ tenantStore.getPageContent('about', 'vision_section', 'title') || 'La nostra visione' }}</h2>
          <p>{{ tenantStore.getPageContent('about', 'vision_section', 'content') || 'La nostra visione...' }}</p>
        </div>
      </div>
    </section>

    <PublicFooter />
  </div>
</template>

<script setup>
import { useTenantStore } from '../../stores/tenant.js'
import PublicNavigation from '../../components/public/PublicNavigation.vue'
import PublicFooter from '../../components/public/PublicFooter.vue'

const tenantStore = useTenantStore()
</script>
