<template>
  <div class="min-h-screen bg-white dark:bg-gray-900">
    <PublicNavigation />
    
    <!-- Hero Section -->
    <section class="bg-brand-primary-600 text-white py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl font-bold mb-4">
          {{ tenantStore.getPageContent('privacy', 'hero', 'title') || 'Privacy Policy' }}
        </h1>
        <p class="text-xl text-brand-primary-100">
          {{ tenantStore.getPageContent('privacy', 'hero', 'subtitle') || 'Informativa sulla privacy' }}
        </p>
      </div>
    </section>

    <!-- Privacy Content -->
    <section class="py-16">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="prose prose-lg max-w-none">
          <p class="text-sm text-gray-500 mb-8">
            {{ tenantStore.getPageContent('privacy', 'last_updated') || 'Ultimo aggiornamento: 1 novembre 2023' }}
          </p>
          
          <div class="space-y-8">
            <div>
              <h2>{{ tenantStore.getPageContent('privacy', 'sections', 'intro')?.title || 'Introduzione' }}</h2>
              <p>{{ tenantStore.getPageContent('privacy', 'sections', 'intro')?.content || 'Contenuto introduzione privacy...' }}</p>
            </div>
            
            <div>
              <h2>{{ tenantStore.getPageContent('privacy', 'sections', 'data_collection')?.title || 'Raccolta dei dati' }}</h2>
              <p>{{ tenantStore.getPageContent('privacy', 'sections', 'data_collection')?.content || 'Contenuto raccolta dati...' }}</p>
            </div>
            
            <div>
              <h2>{{ tenantStore.getPageContent('privacy', 'sections', 'data_usage')?.title || 'Utilizzo dei dati' }}</h2>
              <p>{{ tenantStore.getPageContent('privacy', 'sections', 'data_usage')?.content || 'Contenuto utilizzo dati...' }}</p>
            </div>
            
            <div>
              <h2>{{ tenantStore.getPageContent('privacy', 'sections', 'contact')?.title || 'Contatti' }}</h2>
              <p>{{ tenantStore.getPageContent('privacy', 'sections', 'contact')?.content || 'Contenuto contatti privacy...' }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <PublicFooter />
  </div>
</template>

<script setup>
import { useTenantStore } from '../../stores/tenant.js'
import PublicNavigation from '../../components/public/PublicNavigation.vue'
import PublicFooter from '../../components/public/PublicFooter.vue'

const tenantStore = useTenantStore()
</script>
