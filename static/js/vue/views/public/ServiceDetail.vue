<template>
  <div class="min-h-screen bg-white dark:bg-gray-900">
    <PublicNavigation />
    
    <div v-if="service">
      <!-- Hero Section -->
      <section class="bg-brand-primary-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 class="text-4xl font-bold mb-4">{{ service.name }}</h1>
          <p class="text-xl text-brand-primary-100">{{ service.category }}</p>
        </div>
      </section>

      <!-- Service Details -->
      <section class="py-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="prose prose-lg max-w-none">
            <p>{{ service.description }}</p>
          </div>
        </div>
      </section>

      <!-- Related Services -->
      <section v-if="service.related_services?.length" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 class="text-3xl font-bold text-center mb-12">Servizi correlati</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div 
              v-for="related in service.related_services" 
              :key="related.id"
              class="bg-white p-6 rounded-lg shadow-md"
            >
              <h3 class="text-xl font-semibold mb-2">{{ related.name }}</h3>
              <p class="text-gray-600 mb-4">{{ related.description }}</p>
              <router-link
                :to="`/services/${related.id}`"
                class="text-brand-primary-600 hover:text-brand-primary-700 font-medium"
              >
                Scopri di più →
              </router-link>
            </div>
          </div>
        </div>
      </section>
    </div>

    <PublicFooter />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import PublicNavigation from '../../components/public/PublicNavigation.vue'
import PublicFooter from '../../components/public/PublicFooter.vue'

const route = useRoute()
const service = ref(null)

async function loadService() {
  try {
    const response = await fetch(`/api/public/services/${route.params.id}`)
    if (response.ok) {
      const data = await response.json()
      service.value = data.data
    }
  } catch (error) {
    console.error('Failed to load service:', error)
  }
}

onMounted(() => {
  loadService()
})
</script>
