<template>
  <div class="min-h-screen bg-white dark:bg-gray-900">
    <PublicNavigation />
    
    <!-- Hero Section -->
    <section class="bg-brand-primary-600 text-white py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl font-bold mb-4">
          {{ tenantStore.getPageContent('services', 'hero', 'title') || 'I nostri servizi' }}
        </h1>
        <p class="text-xl text-brand-primary-100">
          {{ tenantStore.getPageContent('services', 'hero', 'subtitle') || 'Soluzioni complete per la tua azienda' }}
        </p>
      </div>
    </section>

    <!-- Services Grid -->
    <section class="py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div 
            v-for="service in services" 
            :key="service.id"
            class="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow"
          >
            <div class="text-brand-primary-500 mb-4">
              <i :class="service.icon" class="text-3xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-brand-text-primary mb-2">
              {{ service.name }}
            </h3>
            <p class="text-brand-text-secondary mb-4">
              {{ service.description }}
            </p>
            <router-link
              :to="`/services/${service.id}`"
              class="text-brand-primary-600 hover:text-brand-primary-700 font-medium"
            >
              Scopri di più →
            </router-link>
          </div>
        </div>
      </div>
    </section>

    <PublicFooter />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useTenantStore } from '../../stores/tenant.js'
import PublicNavigation from '../../components/public/PublicNavigation.vue'
import PublicFooter from '../../components/public/PublicFooter.vue'

const tenantStore = useTenantStore()
const services = ref([])

async function loadServices() {
  try {
    const response = await fetch('/api/public/services')
    if (response.ok) {
      const data = await response.json()
      services.value = data.data || []
    }
  } catch (error) {
    console.error('Failed to load services:', error)
  }
}

onMounted(() => {
  loadServices()
})
</script>
