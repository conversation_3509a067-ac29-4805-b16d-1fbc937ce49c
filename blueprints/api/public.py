from flask import Blueprint, jsonify
from sqlalchemy import desc
from models import Service, News

public_api_bp = Blueprint('public_api', __name__, url_prefix='/api/public')

@public_api_bp.route('/services/featured')
def featured_services():
    """Get featured services for homepage"""
    try:
        services = Service.query.filter_by(status='active').limit(4).all()
        
        services_data = []
        for service in services:
            services_data.append({
                'id': service.id,
                'name': service.name,
                'description': service.description[:150] + '...' if len(service.description) > 150 else service.description,
                'category': service.category,
                'icon': get_service_icon(service.category)
            })
        
        return jsonify({
            'success': True,
            'data': services_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@public_api_bp.route('/news/recent')
def recent_news():
    """Get recent news for homepage"""
    try:
        news = News.query.filter_by(is_published=True).order_by(desc(News.created_at)).limit(3).all()
        
        news_data = []
        for item in news:
            news_data.append({
                'id': item.id,
                'title': item.title,
                'excerpt': item.content[:200] + '...' if len(item.content) > 200 else item.content,
                'created_at': item.created_at.isoformat()
            })
        
        return jsonify({
            'success': True,
            'data': news_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@public_api_bp.route('/services')
def all_services():
    """Get all services"""
    try:
        services = Service.query.filter_by(status='active').order_by(Service.name).all()
        
        services_data = []
        for service in services:
            services_data.append({
                'id': service.id,
                'name': service.name,
                'description': service.description,
                'category': service.category,
                'icon': get_service_icon(service.category)
            })
        
        return jsonify({
            'success': True,
            'data': services_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@public_api_bp.route('/services/<int:service_id>')
def service_detail(service_id):
    """Get service detail"""
    try:
        service = Service.query.get_or_404(service_id)
        
        # Get related services
        related_services = Service.query.filter(
            Service.category == service.category,
            Service.id != service.id,
            Service.status == 'active'
        ).limit(3).all()
        
        related_data = []
        for related in related_services:
            related_data.append({
                'id': related.id,
                'name': related.name,
                'description': related.description[:100] + '...' if len(related.description) > 100 else related.description,
                'category': related.category
            })
        
        service_data = {
            'id': service.id,
            'name': service.name,
            'description': service.description,
            'category': service.category,
            'icon': get_service_icon(service.category),
            'related_services': related_data
        }
        
        return jsonify({
            'success': True,
            'data': service_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def get_service_icon(category):
    """Get icon for service category"""
    icons = {
        'Sviluppo Software': 'fas fa-code',
        'Intelligenza Artificiale': 'fas fa-brain',
        'Consulenza IT': 'fas fa-laptop',
        'Gestione Progetti': 'fas fa-project-diagram',
        'Finanziamenti': 'fas fa-coins',
        'default': 'fas fa-cog'
    }
    return icons.get(category, icons['default'])
