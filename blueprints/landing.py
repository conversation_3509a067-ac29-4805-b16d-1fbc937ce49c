from flask import Blueprint, render_template, redirect, url_for, request, flash, current_app
from flask_login import current_user
from sqlalchemy import desc

from app import db
from models import Service, News
from ai_services import analyze_text_with_openai

landing_bp = Blueprint('landing', __name__)

@landing_bp.route('/')
def home():
    # Get recent news for the homepage
    recent_news = News.query.filter_by(is_published=True).order_by(desc(News.created_at)).limit(3).all()
    
    # Get featured services
    featured_services = Service.query.filter_by(status='active').limit(4).all()
    
    # Check if user is logged in to determine where to direct
    if current_user.is_authenticated:
        dashboard_url = url_for('dashboard.index')
    else:
        dashboard_url = url_for('auth.login')
    
    return render_template(
        'landing/home.html',
        recent_news=recent_news,
        featured_services=featured_services,
        dashboard_url=dashboard_url
    )

@landing_bp.route('/services')
def services():
    # Get all active services
    services_query = Service.query.filter_by(status='active')
    
    # Apply filtering by category if provided
    category = request.args.get('category', None)
    if category:
        services_query = services_query.filter_by(category=category)
    
    # Get all services
    services = services_query.order_by(Service.name).all()
    
    # Get unique categories for filtering
    categories = db.session.query(Service.category).distinct().all()
    categories = [c[0] for c in categories if c[0]]
    
    # Group services by category for display
    services_by_category = {}
    for service in services:
        if service.category not in services_by_category:
            services_by_category[service.category] = []
        services_by_category[service.category].append(service)
    
    return render_template(
        'landing/services.html',
        services=services,
        services_by_category=services_by_category,
        categories=categories,
        current_category=category
    )

@landing_bp.route('/services/<int:service_id>')
def service_detail(service_id):
    service = Service.query.get_or_404(service_id)
    
    # Get other services in the same category
    related_services = Service.query.filter(
        Service.category == service.category,
        Service.id != service.id,
        Service.status == 'active'
    ).limit(3).all()
    
    # AI-generated case study (if service description is substantial)
    case_study = None
    if service.description and len(service.description) > 50:
        try:
            case_study = analyze_text_with_openai(
                service.description,
                "Genera un breve caso di studio su come questo servizio ha aiutato un'azienda cliente:"
            )
        except Exception as e:
            current_app.logger.error(f"Service case study generation error: {str(e)}")
    
    return render_template(
        'landing/service_detail.html',
        service=service,
        related_services=related_services,
        case_study=case_study
    )

@landing_bp.route('/contact', methods=['GET', 'POST'])
def contact():
    if request.method == 'POST':
        name = request.form.get('name')
        email = request.form.get('email')
        message = request.form.get('message')
        
        if not name or not email or not message:
            flash('Tutti i campi sono obbligatori', 'error')
            return redirect(url_for('landing.contact'))
        
        # In a real application, we would send an email or store the contact request
        # For now, we'll just show a success message
        
        flash('Messaggio inviato con successo! Ti contatteremo presto.', 'success')
        return redirect(url_for('landing.contact'))
    
    # Company contact information
    company_info = {
        'name': 'DatVinci',
        'address': 'Via dell\'Innovazione 123, Milano',
        'email': '<EMAIL>',
        'phone': '+39 02 1234567',
        'hours': 'Lun-Ven: 9:00 - 18:00'
    }
    
    return render_template('landing/contact.html', company_info=company_info)

@landing_bp.route('/privacy')
def privacy():
    privacy_policy = {
        'last_updated': '2023-11-01',
        'company_name': 'DatVinci',
        'company_address': 'Via dell\'Innovazione 123, Milano',
        'company_email': '<EMAIL>'
    }
    
    return render_template('landing/privacy.html', privacy_policy=privacy_policy)

@landing_bp.route('/about')
def about():
    # Company information
    company_info = {
        'name': 'DatVinci',
        'founded': '2018',
        'mission': 'Supportare le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all\'avanguardia.',
        'vision': 'Diventare il partner di riferimento per l\'innovazione tecnologica in Italia, aiutando le aziende a cogliere le opportunità del futuro digitale.',
        'team_size': '25+ professionisti',
        'expertise': [
            'Sviluppo Software',
            'Intelligenza Artificiale',
            'Consulenza IT',
            'Gestione Progetti Innovativi',
            'Supporto su Bandi e Finanziamenti'
        ]
    }
    
    # Team members (would normally come from a database)
    team = [
        {
            'name': 'Marco Rossi',
            'role': 'CEO & Founder',
            'bio': 'Esperto di innovazione tecnologica con 15+ anni di esperienza nel settore IT.'
        },
        {
            'name': 'Laura Bianchi',
            'role': 'CTO',
            'bio': 'Specialista in architetture software e intelligenza artificiale.'
        },
        {
            'name': 'Alessandro Verdi',
            'role': 'Business Development Manager',
            'bio': 'Esperto in sviluppo commerciale e gestione clienti nel settore tech.'
        }
    ]
    
    return render_template('landing/about.html', company_info=company_info, team=team)
