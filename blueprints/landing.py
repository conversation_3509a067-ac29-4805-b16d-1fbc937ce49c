from flask import Blueprint, render_template

landing_bp = Blueprint('landing', __name__)

@landing_bp.route('/')
@landing_bp.route('/about')
@landing_bp.route('/contact')
@landing_bp.route('/privacy')
def spa_routes():
    """Serve Vue.js SPA for all public routes"""
    return render_template('spa.html')

@landing_bp.route('/services')
@landing_bp.route('/services/<path:path>')
def services_spa():
    """Serve Vue.js SPA for services routes"""
    return render_template('spa.html')
