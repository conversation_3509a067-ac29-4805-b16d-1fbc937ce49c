*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[17:14:34] 




[17:14:34] Extension host agent started.
[17:14:34] [<unknown>][e4f9c9ac][ExtensionHostConnection] New connection established.
[17:14:34] [<unknown>][b9968bfb][ManagementConnection] New connection established.
[17:14:35] [<unknown>][e4f9c9ac][ExtensionHostConnection] <441> Launched Extension Host Process.
[17:14:35] Deleted marked for removal extension from disk augment.vscode-augment /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.458.1
[17:14:35] ComputeTargetPlatform: linux-x64
[17:14:37] ComputeTargetPlatform: linux-x64
[17:14:50] [File Watcher] Unexpected error: inotify_add_watch on '/home/<USER>/workspace/.config/.vscode-server/data/logs/20250521T185720' failed: No such file or directory (EUNKNOWN) (path: /home/<USER>/workspace)
[17:14:50] [File Watcher (universal)] inotify_add_watch on '/home/<USER>/workspace/.config/.vscode-server/data/logs/20250521T185720' failed: No such file or directory
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[17:18:23] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[17:18:23] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5000
}
New EH opened, aborting shutdown
[17:19:34] New EH opened, aborting shutdown
[17:19:53] [<unknown>][b9968bfb][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[17:19:53] [<unknown>][e4f9c9ac][ExtensionHostConnection] <441> Extension Host Process exited with code: 0, signal: null.
Last EH closed, waiting before shutting down
[17:19:53] Last EH closed, waiting before shutting down
[17:20:02] [<unknown>][9f6d75aa][ManagementConnection] New connection established.
[17:20:02] [<unknown>][9dd2c2a1][ExtensionHostConnection] New connection established.
[17:20:02] [<unknown>][9dd2c2a1][ExtensionHostConnection] <775> Launched Extension Host Process.
[17:22:21] [<unknown>][9f6d75aa][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[17:22:21] [<unknown>][9dd2c2a1][ExtensionHostConnection] <775> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[17:22:21] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[17:22:21] Last EH closed, waiting before shutting down
[17:22:32] [<unknown>][fab7489b][ExtensionHostConnection] New connection established.
[17:22:32] [<unknown>][72112737][ManagementConnection] New connection established.
[17:22:32] [<unknown>][fab7489b][ExtensionHostConnection] <1338> Launched Extension Host Process.
