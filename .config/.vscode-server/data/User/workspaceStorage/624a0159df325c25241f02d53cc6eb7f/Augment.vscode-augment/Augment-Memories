# Implementation Planning and Priorities
- User prefers to create complete implementation plans with clear priorities before starting development work.
- Include routes and templates to be created or modified in the implementation plan.
- Task 7 HR Management implementation plan: Phase 1 (Profile Management, Skills CRUD), Phase 2 (Department Management, Organization Chart), Phase 3 (Resource Allocation, Advanced Features), Phase 4 (Security & Compliance).
- User suggests postponing Task 2.4 (Resource Allocation UI) until Task 7 (HR Management) is implemented, as proper team structure, departments, and managers need to be modeled first.
- For Task 7 HR Management: complete department management (including fixing missing orgchart navigation link) before implementing resource allocation.
- User prefers to proceed with planned implementation phases (Day 2: Dashboard API) and fix test issues in parallel rather than blocking progress on test perfection.

# UI/UX Design and Branding
- User wants to know if the Vue.js application will maintain similar aesthetics to the current Alpine.js version or if it will have a different design approach.
- User prefers consolidated UI interfaces with tab-based pages rather than separate dedicated pages.
- User prefers icons in all tab headers for consistency and expects URL hash fragments to maintain tab state on page reload.
- When creating items via modal forms, the page should stay on the current tab instead of redirecting to a default tab.
- File upload forms should include loading spinners for consistency with the rest of the application UI.
- User prefers centralized CSS instead of inline <style> tags and CSRF tokens should always be in hidden input fields.
- User wants skill proficiency levels to be clearly visible and integrated throughout the UI.
- Admin and HR users should see sensitive user information directly in the profile view, displayed at the bottom of the page.
- Implement pagination consistently across all personnel module pages for better performance, UX, and scalability.
- User expects the organization chart to display a proper graphical visualization, not just hierarchical text format.
- User prefers to hide or remove the white modal/toast that briefly appears on page load.
- User strongly prefers to maintain SPA functionality with non-reloading page navigation and static sidebar during page changes.
- When implementing SPA navigation with Alpine.js, be careful that tab # hash changes don't cause cascading page reloads.
- Future task will implement dynamic branding management (logo, fonts, colors), so Tailwind CSS should be configured to use CSS variables for customizable branding instead of hardcoded values.

# Task and Project Management
- Task 2.4 covers basic task management with interactive lists, CRUD forms, status/priority changes, and simple dependency UI.
- User wants task creation functionality in project detail view and timesheet tab to show monthly view with tasks per row.
- Different project types (service, license, consulting, product, R&D) should have different KPI expectations and calculations.
- Projects should have billable/non-billable flag, and personnel costs should be valued with validity periods to handle changes.
- Project economic management should include client rate display, real billing tracking, and comprehensive financial features.
- Client data should be managed through CRM functionality using the customers/clients table.

# Cost, Revenue and KPI Calculations
- For personnel cost calculations, use daily rates instead of hourly rates as the base unit.
- User prefers KPI thresholds to be configurable per project, with AI-suggested or default values, rather than hardcoded.
- KPI system should include Cost/Revenue metrics; only admin can configure default KPI templates, while project owners configure project-specific targets.
- Task model should include both start_date and estimated_hours fields to enable KPI calculations comparing planned vs actual work.
- When adding new database fields for KPI calculations, ensure the field is added to all relevant templates and models consistently.

# Timesheet Views
- For timesheet views, user prefers days as columns and hours in cells, plus an aggregated view with persons as rows and months as columns.
- For timesheet task tables, show all team member names who worked on each task rather than just the assigned person.
- The system must manage monthly employee timesheets including reimbursable expenses for payroll integration.

# Database and Technical Management
- When seeding database, preserve existing users instead of clearing them completely; database clearing should be optional.
- For database schema changes, use db_update file instead of creating migrations.
- User prefers migration approaches that don't leave behind unused/obsolete files, favoring cleaner code organization.
- The Flask application should be started with 'python main.py' instead of 'python app.py'.
- User prefers not to use stagewise integration in the project and wants it removed.
- The Flask application already has existing APIs implemented, which simplifies the migration to Vue.js SPA architecture.
- Always keep Swagger JSON documentation updated for every API endpoint when creating or modifying APIs.

# Testing and Alpine.js
- User wants tests to be written, verified and updated when implementing new features or making code changes.
- After model changes, APIs should be aligned, tests updated, and validation tests written specifically for KPI calculations.
- Alpine.js is already initialized in base.html template, so manual initialization should not be added elsewhere.
- User wants task status updated step-by-step in @specs/task_16_vue_refactoring.txt as work progresses.