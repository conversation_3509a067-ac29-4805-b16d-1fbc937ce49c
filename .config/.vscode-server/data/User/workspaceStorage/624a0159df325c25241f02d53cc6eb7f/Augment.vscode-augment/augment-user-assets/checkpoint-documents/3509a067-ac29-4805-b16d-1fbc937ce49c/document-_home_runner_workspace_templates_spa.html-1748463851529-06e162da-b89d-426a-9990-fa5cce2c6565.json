{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/spa.html"}, "originalCode": "<!DOCTYPE html>\n<html lang=\"it\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>DatPortal</title>\n\n    <!-- Favicon -->\n    <link rel=\"icon\" type=\"image/x-icon\" href=\"{{ url_for('static', filename='favicon.ico') }}\">\n\n    <!-- CSS -->\n    <link href=\"{{ url_for('static', filename='css/brand-variables.css') }}\" rel=\"stylesheet\">\n    <link href=\"{{ url_for('static', filename='css/tailwind.css') }}\" rel=\"stylesheet\">\n\n    <!-- Font Awesome for icons -->\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n\n    <!-- Chart.js for charts -->\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n\n    <!-- Import Maps for ES6 modules -->\n    <script type=\"importmap\">\n    {\n        \"imports\": {\n            \"vue\": \"https://unpkg.com/vue@3.4.0/dist/vue.esm-browser.prod.js\",\n            \"vue-router\": \"https://unpkg.com/vue-router@4.2.0/dist/vue-router.esm-browser.js\",\n            \"pinia\": \"https://unpkg.com/pinia@2.1.0/dist/pinia.esm-browser.js\",\n            \"axios\": \"https://unpkg.com/axios@1.6.0/dist/esm/axios.min.js\"\n        }\n    }\n    </script>\n\n    <!-- Meta tags for SEO -->\n    <meta name=\"description\" content=\"DatPortal - Sistema di gestione progetti, task e risorse\">\n    <meta name=\"keywords\" content=\"progetti, task, gestione, risorse, KPI, dashboard\">\n    <meta name=\"author\" content=\"DatVinci\">\n\n    <!-- Open Graph meta tags -->\n    <meta property=\"og:title\" content=\"DatPortal\">\n    <meta property=\"og:description\" content=\"Sistema di gestione progetti, task e risorse\">\n    <meta property=\"og:type\" content=\"website\">\n\n    <!-- CSRF Token for API requests -->\n    <meta name=\"csrf-token\" content=\"{{ csrf_token() }}\">\n</head>\n<body class=\"bg-gray-50 dark:bg-gray-900\">\n    <!-- Vue.js App Container -->\n    <div id=\"app\">\n        <!-- Loading spinner while Vue.js loads -->\n        <div class=\"min-h-screen flex items-center justify-center\">\n            <div class=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n        </div>\n    </div>\n\n    <!-- Global Configuration for Vue.js -->\n    <script>\n        // Global app configuration\n        window.APP_CONFIG = {\n            apiUrl: '/api',\n            baseUrl: '{{ request.url_root }}',\n            csrfToken: '{{ csrf_token() }}',\n            user: {{ current_user.to_dict()|tojson if current_user.is_authenticated else 'null' }},\n            isAuthenticated: {{ 'true' if current_user.is_authenticated else 'false' }},\n            version: '1.0.0',\n            environment: '{{ config.ENV }}',\n            debug: {{ 'true' if config.DEBUG else 'false' }}\n        };\n\n        // Configure Axios defaults\n        if (typeof axios !== 'undefined') {\n            axios.defaults.baseURL = window.APP_CONFIG.apiUrl;\n            axios.defaults.headers.common['X-CSRFToken'] = window.APP_CONFIG.csrfToken;\n            axios.defaults.headers.common['Content-Type'] = 'application/json';\n\n            // Add request interceptor for authentication\n            axios.interceptors.request.use(function (config) {\n                // Add timestamp to prevent caching\n                config.params = config.params || {};\n                config.params._t = Date.now();\n                return config;\n            });\n\n            // Add response interceptor for error handling\n            axios.interceptors.response.use(\n                function (response) {\n                    return response;\n                },\n                function (error) {\n                    if (error.response && error.response.status === 401) {\n                        // Redirect to login if unauthorized\n                        window.location.href = '/auth/login';\n                    }\n                    return Promise.reject(error);\n                }\n            );\n        }\n\n        // Global error handler\n        window.addEventListener('error', function(event) {\n            console.error('Global error:', event.error);\n            // You can send errors to a logging service here\n        });\n\n        // Global unhandled promise rejection handler\n        window.addEventListener('unhandledrejection', function(event) {\n            console.error('Unhandled promise rejection:', event.reason);\n            // You can send errors to a logging service here\n        });\n    </script>\n\n    <!-- Vue.js Application Entry Point -->\n    <script type=\"module\" src=\"{{ url_for('static', filename='js/vue/main.js') }}\"></script>\n</body>\n</html>\n", "modifiedCode": "<!DOCTYPE html>\n<html lang=\"it\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>DatPortal</title>\n\n    <!-- Favicon -->\n    <link rel=\"icon\" type=\"image/x-icon\" href=\"{{ url_for('static', filename='favicon.ico') }}\">\n\n    <!-- CSS -->\n    <link href=\"{{ url_for('static', filename='css/brand-variables.css') }}\" rel=\"stylesheet\">\n    <link href=\"{{ url_for('static', filename='css/tailwind.css') }}\" rel=\"stylesheet\">\n\n    <!-- Font Awesome for icons -->\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n\n    <!-- Chart.js for charts -->\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n\n    <!-- Import Maps for ES6 modules -->\n    <script type=\"importmap\">\n    {\n        \"imports\": {\n            \"vue\": \"https://unpkg.com/vue@3.4.0/dist/vue.esm-browser.prod.js\",\n            \"vue-router\": \"https://unpkg.com/vue-router@4.2.0/dist/vue-router.esm-browser.js\",\n            \"pinia\": \"https://unpkg.com/pinia@2.1.0/dist/pinia.esm-browser.js\",\n            \"axios\": \"https://unpkg.com/axios@1.6.0/dist/esm/axios.min.js\"\n        }\n    }\n    </script>\n\n    <!-- Meta tags for SEO -->\n    <meta name=\"description\" content=\"DatPortal - Sistema di gestione progetti, task e risorse\">\n    <meta name=\"keywords\" content=\"progetti, task, gestione, risorse, KPI, dashboard\">\n    <meta name=\"author\" content=\"DatVinci\">\n\n    <!-- Open Graph meta tags -->\n    <meta property=\"og:title\" content=\"DatPortal\">\n    <meta property=\"og:description\" content=\"Sistema di gestione progetti, task e risorse\">\n    <meta property=\"og:type\" content=\"website\">\n\n    <!-- CSRF Token for API requests -->\n    <meta name=\"csrf-token\" content=\"{{ csrf_token() }}\">\n</head>\n<body class=\"bg-gray-50 dark:bg-gray-900\">\n    <!-- Vue.js App Container -->\n    <div id=\"app\">\n        <!-- Loading spinner while Vue.js loads -->\n        <div class=\"min-h-screen flex items-center justify-center\">\n            <div class=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n        </div>\n    </div>\n\n    <!-- Global Configuration for Vue.js -->\n    <script>\n        // Global app configuration\n        window.APP_CONFIG = {\n            apiUrl: '/api',\n            baseUrl: '{{ request.url_root }}',\n            csrfToken: '{{ csrf_token() }}',\n            user: {{ current_user.to_dict()|tojson if current_user.is_authenticated else 'null' }},\n            isAuthenticated: {{ 'true' if current_user.is_authenticated else 'false' }},\n            version: '1.0.0',\n            environment: '{{ config.ENV }}',\n            debug: {{ 'true' if config.DEBUG else 'false' }}\n        };\n\n        // Configure Axios defaults\n        if (typeof axios !== 'undefined') {\n            axios.defaults.baseURL = window.APP_CONFIG.apiUrl;\n            axios.defaults.headers.common['X-CSRFToken'] = window.APP_CONFIG.csrfToken;\n            axios.defaults.headers.common['Content-Type'] = 'application/json';\n\n            // Add request interceptor for authentication\n            axios.interceptors.request.use(function (config) {\n                // Add timestamp to prevent caching\n                config.params = config.params || {};\n                config.params._t = Date.now();\n                return config;\n            });\n\n            // Add response interceptor for error handling\n            axios.interceptors.response.use(\n                function (response) {\n                    return response;\n                },\n                function (error) {\n                    if (error.response && error.response.status === 401) {\n                        // Redirect to login if unauthorized\n                        window.location.href = '/auth/login';\n                    }\n                    return Promise.reject(error);\n                }\n            );\n        }\n\n        // Global error handler\n        window.addEventListener('error', function(event) {\n            console.error('Global error:', event.error);\n            // You can send errors to a logging service here\n        });\n\n        // Global unhandled promise rejection handler\n        window.addEventListener('unhandledrejection', function(event) {\n            console.error('Unhandled promise rejection:', event.reason);\n            // You can send errors to a logging service here\n        });\n    </script>\n\n    <!-- Vue.js Application Entry Point -->\n    <script type=\"module\" src=\"{{ url_for('static', filename='js/vue/main.js') }}\"></script>\n\n    <!-- Fallback for browsers that don't support import maps -->\n    <script nomodule>\n        document.getElementById('app').innerHTML = `\n            <div class=\"min-h-screen flex items-center justify-center bg-gray-50\">\n                <div class=\"text-center\">\n                    <h1 class=\"text-2xl font-bold text-gray-900 mb-4\">Browser non supportato</h1>\n                    <p class=\"text-gray-600 mb-4\">Il tuo browser non supporta le Import Maps.</p>\n                    <p class=\"text-gray-600\">Aggiorna il tuo browser per utilizzare DatPortal.</p>\n                </div>\n            </div>\n        `;\n    </script>\n</body>\n</html>\n"}