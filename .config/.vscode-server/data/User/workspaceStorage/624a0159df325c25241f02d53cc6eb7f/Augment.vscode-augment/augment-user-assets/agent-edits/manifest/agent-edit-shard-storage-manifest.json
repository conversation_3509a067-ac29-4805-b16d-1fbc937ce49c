{"version": 1, "lastUpdated": 1748455443044, "shards": {"shard-f331798c-cd73-4dc9-b150-f4cffdfb1f14": {"checkpointDocumentIds": ["f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/api/projects.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/static/swagger/swagger.json", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/api/tasks.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/api/base.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/api/test_tasks.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/dashboard.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/api/resources.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/api/test_resources.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/utils/permissions.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/api/task_dependencies.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/api/test_task_dependencies.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/api/project_kpis.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/api/test_project_kpis.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/api/test_projects.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/static/js/components.js", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/components/toast.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/components/modal.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/components/loader.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/base.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/projects/index.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/projects.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/static/js/alpine-init.js", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/static/js/utils.js", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/api/kpis.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/api/test_kpis.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/conftest.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/models.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/projects/create.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/projects/view.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/seed_data.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/projects/edit.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/utils/cost_calculator.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/db_update_cost_management.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/admin.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/admin/kpi_templates.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/app.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/components/sidebar.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/projects/expenses.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/projects/kpi_config.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/scripts/seed_kpi_templates.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/unit/test_kpi_calculations.py"], "size": 21913987, "checkpointCount": 393, "lastModified": 1748192836659}, "shard-d1979f69-469e-4d68-9b82-aa95de57cefc": {"checkpointDocumentIds": ["d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/projects/view.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/blueprints/projects.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/models.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/db_update.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/add_task_fields.sql", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/tests/api/test_tasks.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/tests/integration/test_project_crud.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/tests/unit/test_task_kpi_calculations.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/index.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/profile.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/skills.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/admin.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/blueprints/personnel.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/edit_profile.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/directory.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/orgchart.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/_department_node.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/profile_completion.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/ai_services.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/utils/cv_parser.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/db_update_cv.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/generated_cv.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/utils/filters.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/test_cv.txt", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/cv_pdf_template.html"], "size": 17271241, "checkpointCount": 137, "lastModified": 1748208267554}, "shard-b78c4e9e-00fa-421d-9c6d-2d78753d686b": {"checkpointDocumentIds": ["b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/static/css/tailwind.css", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/static/css/cv-styles.css", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/projects/view.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/generated_cv.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/cv_pdf_template.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/static/css/components.css", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/base.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/docs/css-architecture.md", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/blueprints/personnel.py", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/edit_skill.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/skills.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/edit_profile.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/utils/image_utils.py", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/skills_matrix.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/profile.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/migrate_skills.py", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/models.py", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/create_complete_profiles.py", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/index.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/components/sidebar.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/orgchart.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/departments/index.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/departments/create.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/departments/edit.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/departments/dashboard.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/departments/manage_employees.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/components/toast.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/components/notification.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/static/js/spa-navigation.js"], "size": 8273586, "checkpointCount": 155, "lastModified": 1748294260449}, "shard-92082ae1-d76e-4e99-a0d3-7673aa7d0d81": {"checkpointDocumentIds": ["92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/db_update.py", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/specs/todo_7_26_maggio.txt", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/templates/personnel/orgchart.html", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/templates/personnel/_department_chart_node.html", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/specs/task_7_status.txt", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/static/js/stagewise-config.js", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/static/js/alpine-init.js", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/templates/base.html", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/app.py"], "size": 460254, "checkpointCount": 30, "lastModified": 1748375379643}, "shard-3509a067-ac29-4805-b16d-1fbc937ce49c": {"checkpointDocumentIds": ["3509a067-ac29-4805-b16d-1fbc937ce49c:/home/<USER>/workspace/specs/task_16_vue_refactoring.txt", "3509a067-ac29-4805-b16d-1fbc937ce49c:/home/<USER>/workspace/blueprints/api/personnel.py", "3509a067-ac29-4805-b16d-1fbc937ce49c:/home/<USER>/workspace/blueprints/api/base.py", "3509a067-ac29-4805-b16d-1fbc937ce49c:/home/<USER>/workspace/static/swagger/swagger.json", "3509a067-ac29-4805-b16d-1fbc937ce49c:/home/<USER>/workspace/tests/api/test_personnel.py", "3509a067-ac29-4805-b16d-1fbc937ce49c:/home/<USER>/workspace/tests/conftest.py", "3509a067-ac29-4805-b16d-1fbc937ce49c:/home/<USER>/workspace/app.py"], "size": 3015774, "checkpointCount": 76, "lastModified": 1748455438879}}}