{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/auth.py"}, "originalCode": "from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, session\nfrom flask_login import login_user, logout_user, login_required, current_user\nfrom werkzeug.security import generate_password_hash\nfrom datetime import datetime, timedelta\nimport secrets\nimport time\n\nfrom app import db\nfrom models import User\nfrom utils.token_utils import generate_reset_token, verify_reset_token\nfrom utils.email_utils import send_password_reset_email\n\nauth_bp = Blueprint('auth', __name__, url_prefix='/auth')\n\n@auth_bp.route('/login', methods=['GET', 'POST'])\ndef login():\n    if current_user.is_authenticated:\n        return redirect(url_for('dashboard.index'))\n\n    if request.method == 'POST':\n        username = request.form.get('username')\n        password = request.form.get('password')\n        remember = True if request.form.get('remember') else False\n\n        user = User.query.filter_by(username=username).first()\n\n        if not user or not user.check_password(password):\n            flash('Verifica nome utente e password e riprova.', 'error')\n            return redirect(url_for('auth.login'))\n\n        if not user.is_active:\n            flash('Il tuo account è disattivato. Contatta l\\'amministratore.', 'error')\n            return redirect(url_for('auth.login'))\n\n        # Login successful, update last login time\n        user.last_login = datetime.utcnow()\n        db.session.commit()\n\n        login_user(user, remember=remember)\n        session['login_time'] = time.time()\n        session['last_activity'] = time.time()\n\n        # Redirect to the page user was trying to access or dashboard\n        next_page = request.args.get('next')\n        if next_page:\n            return redirect(next_page)\n        return redirect(url_for('dashboard.index'))\n\n    return render_template('auth/login.html')\n\n@auth_bp.route('/register', methods=['GET', 'POST'])\ndef register():\n    if current_user.is_authenticated:\n        return redirect(url_for('dashboard.index'))\n\n    if request.method == 'POST':\n        username = request.form.get('username')\n        email = request.form.get('email')\n        password = request.form.get('password')\n        confirm_password = request.form.get('confirm_password')\n        first_name = request.form.get('first_name')\n        last_name = request.form.get('last_name')\n\n        # Validate input\n        if not username or not email or not password or not confirm_password:\n            flash('Tutti i campi sono obbligatori', 'error')\n            return redirect(url_for('auth.register'))\n\n        if password != confirm_password:\n            flash('Le password non corrispondono', 'error')\n            return redirect(url_for('auth.register'))\n\n        # Check if username or email already exists\n        if User.query.filter_by(username=username).first():\n            flash('Username già in uso', 'error')\n            return redirect(url_for('auth.register'))\n\n        if User.query.filter_by(email=email).first():\n            flash('Email già registrata', 'error')\n            return redirect(url_for('auth.register'))\n\n        # Create new user - inactive by default until approved\n        new_user = User(\n            username=username,\n            email=email,\n            first_name=first_name,\n            last_name=last_name,\n            is_active=False  # Admin needs to activate\n        )\n        new_user.set_password(password)\n\n        try:\n            db.session.add(new_user)\n            db.session.commit()\n            flash('Account creato con successo! Un amministratore attiverà il tuo account a breve.', 'success')\n            return redirect(url_for('auth.login'))\n        except Exception as e:\n            db.session.rollback()\n            current_app.logger.error(f\"Registration error: {str(e)}\")\n            flash('Si è verificato un errore durante la registrazione. Riprova più tardi.', 'error')\n            return redirect(url_for('auth.register'))\n\n    return render_template('auth/register.html')\n\n@auth_bp.route('/logout')\n@login_required\ndef logout():\n    logout_user()\n    flash('Disconnessione effettuata con successo', 'success')\n    return redirect(url_for('landing.home'))\n\n@auth_bp.route('/forgot-password', methods=['GET', 'POST'])\ndef forgot_password():\n    if current_user.is_authenticated:\n        return redirect(url_for('dashboard.index'))\n    if request.method == 'POST':\n        email = request.form.get('email')\n        user = User.query.filter_by(email=email).first()\n        if user:\n            token = generate_reset_token()\n            expiry_time = datetime.utcnow() + timedelta(seconds=current_app.config.get('PASSWORD_RESET_TOKEN_EXPIRATION_SECONDS', 3600))\n            user.reset_token = token\n            user.reset_token_expiry = expiry_time\n            db.session.commit()\n\n            send_password_reset_email(user.email, user.username, token)\n            flash('Se l\\'indirizzo email è nel nostro database, riceverai un link per il reset della password.', 'info')\n        else:\n            flash('Se l\\'indirizzo email è nel nostro database, riceverai un link per il reset della password.', 'info')\n        return redirect(url_for('auth.login'))\n    return render_template('auth/forgot_password.html')\n\n@auth_bp.route('/reset-password/<token>', methods=['GET', 'POST'])\ndef reset_password_with_token(token):\n    if current_user.is_authenticated:\n        return redirect(url_for('dashboard.index'))\n\n    user = verify_reset_token(token)\n\n    if not user:\n        flash('Il link di reset password non è valido o è scaduto.', 'error')\n        return redirect(url_for('auth.login'))\n\n    if request.method == 'POST':\n        password = request.form.get('password')\n        confirm_password = request.form.get('confirm_password')\n\n        if not password or not confirm_password:\n            flash('Entrambi i campi password sono obbligatori.', 'error')\n            return render_template('auth/reset_password_form.html', token=token)\n\n        if password != confirm_password:\n            flash('Le password non corrispondono.', 'error')\n            return render_template('auth/reset_password_form.html', token=token)\n\n        # Qui potresti aggiungere validatori di complessità password\n\n        user.set_password(password)\n        user.reset_token = None\n        user.reset_token_expiry = None\n        db.session.commit()\n\n        flash('La tua password è stata resettata con successo. Ora puoi effettuare il login.', 'success')\n        return redirect(url_for('auth.login'))\n\n    return render_template('auth/reset_password_form.html', token=token)\n\n@auth_bp.route('/profile', methods=['GET', 'POST'])\n@login_required\ndef profile():\n    if request.method == 'POST':\n        first_name = request.form.get('first_name')\n        last_name = request.form.get('last_name')\n        email = request.form.get('email')\n        phone = request.form.get('phone')\n        bio = request.form.get('bio')\n        dark_mode = True if request.form.get('dark_mode') else False\n        current_password = request.form.get('current_password')\n        new_password = request.form.get('new_password')\n        confirm_password = request.form.get('confirm_password')\n\n        # Validate email change\n        if email != current_user.email and User.query.filter_by(email=email).first():\n            flash('Email già in uso', 'error')\n            return redirect(url_for('auth.profile'))\n\n        # Update user information\n        current_user.first_name = first_name\n        current_user.last_name = last_name\n        current_user.email = email\n        current_user.phone = phone\n        current_user.bio = bio\n        current_user.dark_mode = dark_mode\n\n        # Handle password change if requested\n        if current_password and new_password:\n            if not current_user.check_password(current_password):\n                flash('Password attuale non corretta', 'error')\n                return redirect(url_for('auth.profile'))\n\n            if new_password != confirm_password:\n                flash('Le nuove password non corrispondono', 'error')\n                return redirect(url_for('auth.profile'))\n\n            current_user.set_password(new_password)\n            flash('Password aggiornata con successo', 'success')\n\n        try:\n            db.session.commit()\n            flash('Profilo aggiornato con successo', 'success')\n        except Exception as e:\n            db.session.rollback()\n            current_app.logger.error(f\"Profile update error: {str(e)}\")\n            flash('Si è verificato un errore durante l\\'aggiornamento del profilo', 'error')\n\n        return redirect(url_for('auth.profile'))\n\n    return render_template('personnel/profile.html', user=current_user)\n", "modifiedCode": "from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, session\nfrom flask_login import login_user, logout_user, login_required, current_user\nfrom werkzeug.security import generate_password_hash\nfrom datetime import datetime, timedelta\nimport secrets\nimport time\n\nfrom app import db\nfrom models import User\nfrom utils.token_utils import generate_reset_token, verify_reset_token\nfrom utils.email_utils import send_password_reset_email\n\nauth_bp = Blueprint('auth', __name__, url_prefix='/auth')\n\n@auth_bp.route('/login', methods=['GET', 'POST'])\ndef login():\n    if current_user.is_authenticated:\n        return redirect(url_for('dashboard.index'))\n\n    if request.method == 'POST':\n        username = request.form.get('username')\n        password = request.form.get('password')\n        remember = True if request.form.get('remember') else False\n\n        user = User.query.filter_by(username=username).first()\n\n        if not user or not user.check_password(password):\n            flash('Verifica nome utente e password e riprova.', 'error')\n            return redirect(url_for('auth.login'))\n\n        if not user.is_active:\n            flash('Il tuo account è disattivato. Contatta l\\'amministratore.', 'error')\n            return redirect(url_for('auth.login'))\n\n        # Login successful, update last login time\n        user.last_login = datetime.utcnow()\n        db.session.commit()\n\n        login_user(user, remember=remember)\n        session['login_time'] = time.time()\n        session['last_activity'] = time.time()\n\n        # Redirect to the page user was trying to access or dashboard\n        next_page = request.args.get('next')\n        if next_page:\n            return redirect(next_page)\n        return redirect(url_for('dashboard.index'))\n\n    return render_template('spa.html')\n\n@auth_bp.route('/register', methods=['GET', 'POST'])\ndef register():\n    if current_user.is_authenticated:\n        return redirect(url_for('dashboard.index'))\n\n    if request.method == 'POST':\n        username = request.form.get('username')\n        email = request.form.get('email')\n        password = request.form.get('password')\n        confirm_password = request.form.get('confirm_password')\n        first_name = request.form.get('first_name')\n        last_name = request.form.get('last_name')\n\n        # Validate input\n        if not username or not email or not password or not confirm_password:\n            flash('Tutti i campi sono obbligatori', 'error')\n            return redirect(url_for('auth.register'))\n\n        if password != confirm_password:\n            flash('Le password non corrispondono', 'error')\n            return redirect(url_for('auth.register'))\n\n        # Check if username or email already exists\n        if User.query.filter_by(username=username).first():\n            flash('Username già in uso', 'error')\n            return redirect(url_for('auth.register'))\n\n        if User.query.filter_by(email=email).first():\n            flash('Email già registrata', 'error')\n            return redirect(url_for('auth.register'))\n\n        # Create new user - inactive by default until approved\n        new_user = User(\n            username=username,\n            email=email,\n            first_name=first_name,\n            last_name=last_name,\n            is_active=False  # Admin needs to activate\n        )\n        new_user.set_password(password)\n\n        try:\n            db.session.add(new_user)\n            db.session.commit()\n            flash('Account creato con successo! Un amministratore attiverà il tuo account a breve.', 'success')\n            return redirect(url_for('auth.login'))\n        except Exception as e:\n            db.session.rollback()\n            current_app.logger.error(f\"Registration error: {str(e)}\")\n            flash('Si è verificato un errore durante la registrazione. Riprova più tardi.', 'error')\n            return redirect(url_for('auth.register'))\n\n    return render_template('auth/register.html')\n\n@auth_bp.route('/logout')\n@login_required\ndef logout():\n    logout_user()\n    flash('Disconnessione effettuata con successo', 'success')\n    return redirect(url_for('landing.home'))\n\n@auth_bp.route('/forgot-password', methods=['GET', 'POST'])\ndef forgot_password():\n    if current_user.is_authenticated:\n        return redirect(url_for('dashboard.index'))\n    if request.method == 'POST':\n        email = request.form.get('email')\n        user = User.query.filter_by(email=email).first()\n        if user:\n            token = generate_reset_token()\n            expiry_time = datetime.utcnow() + timedelta(seconds=current_app.config.get('PASSWORD_RESET_TOKEN_EXPIRATION_SECONDS', 3600))\n            user.reset_token = token\n            user.reset_token_expiry = expiry_time\n            db.session.commit()\n\n            send_password_reset_email(user.email, user.username, token)\n            flash('Se l\\'indirizzo email è nel nostro database, riceverai un link per il reset della password.', 'info')\n        else:\n            flash('Se l\\'indirizzo email è nel nostro database, riceverai un link per il reset della password.', 'info')\n        return redirect(url_for('auth.login'))\n    return render_template('auth/forgot_password.html')\n\n@auth_bp.route('/reset-password/<token>', methods=['GET', 'POST'])\ndef reset_password_with_token(token):\n    if current_user.is_authenticated:\n        return redirect(url_for('dashboard.index'))\n\n    user = verify_reset_token(token)\n\n    if not user:\n        flash('Il link di reset password non è valido o è scaduto.', 'error')\n        return redirect(url_for('auth.login'))\n\n    if request.method == 'POST':\n        password = request.form.get('password')\n        confirm_password = request.form.get('confirm_password')\n\n        if not password or not confirm_password:\n            flash('Entrambi i campi password sono obbligatori.', 'error')\n            return render_template('auth/reset_password_form.html', token=token)\n\n        if password != confirm_password:\n            flash('Le password non corrispondono.', 'error')\n            return render_template('auth/reset_password_form.html', token=token)\n\n        # Qui potresti aggiungere validatori di complessità password\n\n        user.set_password(password)\n        user.reset_token = None\n        user.reset_token_expiry = None\n        db.session.commit()\n\n        flash('La tua password è stata resettata con successo. Ora puoi effettuare il login.', 'success')\n        return redirect(url_for('auth.login'))\n\n    return render_template('auth/reset_password_form.html', token=token)\n\n@auth_bp.route('/profile', methods=['GET', 'POST'])\n@login_required\ndef profile():\n    if request.method == 'POST':\n        first_name = request.form.get('first_name')\n        last_name = request.form.get('last_name')\n        email = request.form.get('email')\n        phone = request.form.get('phone')\n        bio = request.form.get('bio')\n        dark_mode = True if request.form.get('dark_mode') else False\n        current_password = request.form.get('current_password')\n        new_password = request.form.get('new_password')\n        confirm_password = request.form.get('confirm_password')\n\n        # Validate email change\n        if email != current_user.email and User.query.filter_by(email=email).first():\n            flash('Email già in uso', 'error')\n            return redirect(url_for('auth.profile'))\n\n        # Update user information\n        current_user.first_name = first_name\n        current_user.last_name = last_name\n        current_user.email = email\n        current_user.phone = phone\n        current_user.bio = bio\n        current_user.dark_mode = dark_mode\n\n        # Handle password change if requested\n        if current_password and new_password:\n            if not current_user.check_password(current_password):\n                flash('Password attuale non corretta', 'error')\n                return redirect(url_for('auth.profile'))\n\n            if new_password != confirm_password:\n                flash('Le nuove password non corrispondono', 'error')\n                return redirect(url_for('auth.profile'))\n\n            current_user.set_password(new_password)\n            flash('Password aggiornata con successo', 'success')\n\n        try:\n            db.session.commit()\n            flash('Profilo aggiornato con successo', 'success')\n        except Exception as e:\n            db.session.rollback()\n            current_app.logger.error(f\"Profile update error: {str(e)}\")\n            flash('Si è verificato un errore durante l\\'aggiornamento del profilo', 'error')\n\n        return redirect(url_for('auth.profile'))\n\n    return render_template('personnel/profile.html', user=current_user)\n"}