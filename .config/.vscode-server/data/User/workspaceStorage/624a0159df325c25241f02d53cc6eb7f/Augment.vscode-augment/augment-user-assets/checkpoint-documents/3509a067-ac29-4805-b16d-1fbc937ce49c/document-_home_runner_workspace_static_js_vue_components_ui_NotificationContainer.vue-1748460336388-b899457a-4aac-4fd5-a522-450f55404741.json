{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/ui/NotificationContainer.vue"}, "modifiedCode": "<template>\n  <div class=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full\">\n    <transition-group\n      name=\"notification\"\n      tag=\"div\"\n      class=\"space-y-2\"\n    >\n      <div\n        v-for=\"notification in appStore.notifications\"\n        :key=\"notification.id\"\n        class=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border-l-4 p-4 transition-all duration-300\"\n        :class=\"getNotificationClasses(notification.type)\"\n      >\n        <div class=\"flex items-start\">\n          <!-- Icon -->\n          <div class=\"flex-shrink-0\">\n            <i \n              :class=\"getNotificationIcon(notification.type)\"\n              class=\"h-5 w-5\"\n            ></i>\n          </div>\n          \n          <!-- Content -->\n          <div class=\"ml-3 flex-1\">\n            <p class=\"text-sm font-medium text-brand-text-primary\">\n              {{ notification.message }}\n            </p>\n            <p v-if=\"notification.description\" class=\"mt-1 text-xs text-brand-text-secondary\">\n              {{ notification.description }}\n            </p>\n            \n            <!-- Actions -->\n            <div v-if=\"notification.actions\" class=\"mt-2 flex space-x-2\">\n              <button\n                v-for=\"action in notification.actions\"\n                :key=\"action.label\"\n                @click=\"handleAction(notification, action)\"\n                class=\"text-xs font-medium px-2 py-1 rounded transition-colors\"\n                :class=\"action.primary \n                  ? 'bg-brand-primary-500 text-white hover:bg-brand-primary-600' \n                  : 'text-brand-primary-600 hover:text-brand-primary-700'\"\n              >\n                {{ action.label }}\n              </button>\n            </div>\n          </div>\n          \n          <!-- Close Button -->\n          <div class=\"ml-4 flex-shrink-0\">\n            <button\n              @click=\"appStore.hideNotification(notification.id)\"\n              class=\"text-brand-text-tertiary hover:text-brand-text-secondary transition-colors\"\n            >\n              <i class=\"fas fa-times h-4 w-4\"></i>\n            </button>\n          </div>\n        </div>\n        \n        <!-- Progress Bar (for timed notifications) -->\n        <div \n          v-if=\"notification.duration > 0\"\n          class=\"mt-2 h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden\"\n        >\n          <div \n            class=\"h-full bg-current opacity-30 transition-all ease-linear\"\n            :class=\"getProgressBarClass(notification.type)\"\n            :style=\"{ \n              width: '100%',\n              animation: `shrink ${notification.duration}ms linear forwards`\n            }\"\n          ></div>\n        </div>\n      </div>\n    </transition-group>\n  </div>\n</template>\n\n<script setup>\nimport { useAppStore } from '../../stores/app.js'\n\n// Store\nconst appStore = useAppStore()\n\n// Methods\nfunction getNotificationClasses(type) {\n  const classes = {\n    success: 'border-brand-success-500 bg-brand-success-50 dark:bg-brand-success-900/20',\n    error: 'border-brand-error-500 bg-brand-error-50 dark:bg-brand-error-900/20',\n    warning: 'border-brand-warning-500 bg-brand-warning-50 dark:bg-brand-warning-900/20',\n    info: 'border-brand-primary-500 bg-brand-primary-50 dark:bg-brand-primary-900/20'\n  }\n  return classes[type] || classes.info\n}\n\nfunction getNotificationIcon(type) {\n  const icons = {\n    success: 'fas fa-check-circle text-brand-success-500',\n    error: 'fas fa-exclamation-circle text-brand-error-500',\n    warning: 'fas fa-exclamation-triangle text-brand-warning-500',\n    info: 'fas fa-info-circle text-brand-primary-500'\n  }\n  return icons[type] || icons.info\n}\n\nfunction getProgressBarClass(type) {\n  const classes = {\n    success: 'text-brand-success-500',\n    error: 'text-brand-error-500',\n    warning: 'text-brand-warning-500',\n    info: 'text-brand-primary-500'\n  }\n  return classes[type] || classes.info\n}\n\nfunction handleAction(notification, action) {\n  if (action.handler) {\n    action.handler(notification)\n  }\n  \n  if (action.closeOnClick !== false) {\n    appStore.hideNotification(notification.id)\n  }\n}\n</script>\n\n<style scoped>\n/* Notification animations */\n.notification-enter-active {\n  transition: all 0.3s ease-out;\n}\n\n.notification-leave-active {\n  transition: all 0.3s ease-in;\n}\n\n.notification-enter-from {\n  opacity: 0;\n  transform: translateX(100%);\n}\n\n.notification-leave-to {\n  opacity: 0;\n  transform: translateX(100%);\n}\n\n.notification-move {\n  transition: transform 0.3s ease;\n}\n\n/* Progress bar shrink animation */\n@keyframes shrink {\n  from {\n    width: 100%;\n  }\n  to {\n    width: 0%;\n  }\n}\n\n/* Responsive adjustments */\n@media (max-width: 640px) {\n  .fixed.top-4.right-4 {\n    top: 1rem;\n    right: 1rem;\n    left: 1rem;\n    max-width: none;\n  }\n}\n\n/* Hover effects */\n.bg-white:hover {\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n}\n\n.dark .bg-white:hover {\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);\n}\n\n/* Focus styles */\nbutton:focus {\n  outline: 2px solid var(--brand-primary-500);\n  outline-offset: 2px;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .notification-enter-active,\n  .notification-leave-active,\n  .notification-move,\n  .transition-all {\n    transition: none;\n  }\n  \n  @keyframes shrink {\n    from, to {\n      width: 100%;\n    }\n  }\n}\n</style>\n"}