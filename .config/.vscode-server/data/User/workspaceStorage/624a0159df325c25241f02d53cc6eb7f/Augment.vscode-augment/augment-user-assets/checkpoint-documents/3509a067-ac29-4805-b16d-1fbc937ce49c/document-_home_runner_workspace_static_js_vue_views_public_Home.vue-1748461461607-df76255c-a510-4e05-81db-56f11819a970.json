{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/Home.vue"}, "originalCode": "<template>\n  <div class=\"min-h-screen bg-white dark:bg-gray-900\">\n    <!-- Navigation -->\n    <PublicNavigation />\n\n    <!-- Hero Section -->\n    <section class=\"relative bg-gradient-to-r from-brand-primary-600 to-brand-secondary-600 text-white\">\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24\">\n        <div class=\"text-center\">\n          <h1 class=\"text-4xl md:text-6xl font-bold mb-6\">\n            {{ tenantStore.getPageContent('home', 'hero', 'title') }}\n          </h1>\n          <p class=\"text-xl md:text-2xl mb-8 text-brand-primary-100\">\n            {{ tenantStore.getPageContent('home', 'hero', 'subtitle') }}\n          </p>\n          <div class=\"space-x-4\">\n            <router-link\n              to=\"/services\"\n              class=\"inline-block bg-white text-brand-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\"\n            >\n              {{ tenantStore.getPageContent('home', 'hero', 'cta_primary') }}\n            </router-link>\n            <router-link\n              to=\"/contact\"\n              class=\"inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-brand-primary-600 transition-colors\"\n            >\n              {{ tenantStore.getPageContent('home', 'hero', 'cta_secondary') }}\n            </router-link>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Services Preview -->\n    <section class=\"py-16 bg-gray-50 dark:bg-gray-800\">\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center mb-12\">\n          <h2 class=\"text-3xl font-bold text-brand-text-primary mb-4\">\n            {{ tenantStore.getPageContent('home', 'services_section', 'title') }}\n          </h2>\n          <p class=\"text-lg text-brand-text-secondary\">\n            {{ tenantStore.getPageContent('home', 'services_section', 'subtitle') }}\n          </p>\n        </div>\n\n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          <div\n            v-for=\"service in featuredServices\"\n            :key=\"service.id\"\n            class=\"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow\"\n          >\n            <div class=\"text-brand-primary-500 mb-4\">\n              <i :class=\"service.icon\" class=\"text-3xl\"></i>\n            </div>\n            <h3 class=\"text-xl font-semibold text-brand-text-primary mb-2\">\n              {{ service.name }}\n            </h3>\n            <p class=\"text-brand-text-secondary mb-4\">\n              {{ service.description }}\n            </p>\n            <router-link\n              :to=\"`/services/${service.id}`\"\n              class=\"text-brand-primary-600 hover:text-brand-primary-700 font-medium\"\n            >\n              Scopri di più →\n            </router-link>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- News Section -->\n    <section class=\"py-16\">\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center mb-12\">\n          <h2 class=\"text-3xl font-bold text-brand-text-primary mb-4\">\n            Ultime notizie\n          </h2>\n        </div>\n\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          <article\n            v-for=\"news in recentNews\"\n            :key=\"news.id\"\n            class=\"bg-white dark:bg-gray-700 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\"\n          >\n            <div class=\"p-6\">\n              <div class=\"text-sm text-brand-text-tertiary mb-2\">\n                {{ formatDate(news.created_at) }}\n              </div>\n              <h3 class=\"text-xl font-semibold text-brand-text-primary mb-3\">\n                {{ news.title }}\n              </h3>\n              <p class=\"text-brand-text-secondary\">\n                {{ news.excerpt }}\n              </p>\n            </div>\n          </article>\n        </div>\n      </div>\n    </section>\n\n    <!-- CTA Section -->\n    <section class=\"bg-brand-primary-600 text-white py-16\">\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <h2 class=\"text-3xl font-bold mb-4\">\n          Pronto a innovare la tua azienda?\n        </h2>\n        <p class=\"text-xl mb-8 text-brand-primary-100\">\n          Contattaci per una consulenza gratuita\n        </p>\n        <router-link\n          to=\"/contact\"\n          class=\"inline-block bg-white text-brand-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\"\n        >\n          Inizia ora\n        </router-link>\n      </div>\n    </section>\n\n    <!-- Footer -->\n    <PublicFooter />\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport PublicNavigation from '../../components/public/PublicNavigation.vue'\nimport PublicFooter from '../../components/public/PublicFooter.vue'\n\n// State\nconst featuredServices = ref([])\nconst recentNews = ref([])\nconst isLoading = ref(false)\n\n// Methods\nasync function loadData() {\n  isLoading.value = true\n  try {\n    // Load featured services\n    const servicesResponse = await fetch('/api/public/services/featured')\n    if (servicesResponse.ok) {\n      const servicesData = await servicesResponse.json()\n      featuredServices.value = servicesData.data || []\n    }\n\n    // Load recent news\n    const newsResponse = await fetch('/api/public/news/recent')\n    if (newsResponse.ok) {\n      const newsData = await newsResponse.json()\n      recentNews.value = newsData.data || []\n    }\n  } catch (error) {\n    console.error('Failed to load home data:', error)\n  } finally {\n    isLoading.value = false\n  }\n}\n\nfunction formatDate(date) {\n  return new Date(date).toLocaleDateString('it-IT', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\n// Lifecycle\nonMounted(() => {\n  loadData()\n})\n</script>\n\n<style scoped>\n/* Hero gradient animation */\n.bg-gradient-to-r {\n  background-size: 200% 200%;\n  animation: gradientShift 8s ease infinite;\n}\n\n@keyframes gradientShift {\n  0% { background-position: 0% 50%; }\n  50% { background-position: 100% 50%; }\n  100% { background-position: 0% 50%; }\n}\n\n/* Card hover effects */\n.hover\\\\:shadow-lg:hover {\n  transform: translateY(-2px);\n}\n\n/* Smooth transitions */\n.transition-shadow,\n.transition-colors {\n  transition-duration: 0.3s;\n}\n\n/* Responsive adjustments */\n@media (max-width: 640px) {\n  .text-4xl.md\\\\:text-6xl {\n    font-size: 2.5rem;\n  }\n\n  .text-xl.md\\\\:text-2xl {\n    font-size: 1.25rem;\n  }\n}\n</style>\n", "modifiedCode": "<template>\n  <div class=\"min-h-screen bg-white dark:bg-gray-900\">\n    <!-- Navigation -->\n    <PublicNavigation />\n\n    <!-- Hero Section -->\n    <section class=\"relative bg-gradient-to-r from-brand-primary-600 to-brand-secondary-600 text-white\">\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24\">\n        <div class=\"text-center\">\n          <h1 class=\"text-4xl md:text-6xl font-bold mb-6\">\n            {{ tenantStore.getPageContent('home', 'hero', 'title') }}\n          </h1>\n          <p class=\"text-xl md:text-2xl mb-8 text-brand-primary-100\">\n            {{ tenantStore.getPageContent('home', 'hero', 'subtitle') }}\n          </p>\n          <div class=\"space-x-4\">\n            <router-link\n              to=\"/services\"\n              class=\"inline-block bg-white text-brand-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\"\n            >\n              {{ tenantStore.getPageContent('home', 'hero', 'cta_primary') }}\n            </router-link>\n            <router-link\n              to=\"/contact\"\n              class=\"inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-brand-primary-600 transition-colors\"\n            >\n              {{ tenantStore.getPageContent('home', 'hero', 'cta_secondary') }}\n            </router-link>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Services Preview -->\n    <section class=\"py-16 bg-gray-50 dark:bg-gray-800\">\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center mb-12\">\n          <h2 class=\"text-3xl font-bold text-brand-text-primary mb-4\">\n            {{ tenantStore.getPageContent('home', 'services_section', 'title') }}\n          </h2>\n          <p class=\"text-lg text-brand-text-secondary\">\n            {{ tenantStore.getPageContent('home', 'services_section', 'subtitle') }}\n          </p>\n        </div>\n\n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          <div\n            v-for=\"service in featuredServices\"\n            :key=\"service.id\"\n            class=\"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow\"\n          >\n            <div class=\"text-brand-primary-500 mb-4\">\n              <i :class=\"service.icon\" class=\"text-3xl\"></i>\n            </div>\n            <h3 class=\"text-xl font-semibold text-brand-text-primary mb-2\">\n              {{ service.name }}\n            </h3>\n            <p class=\"text-brand-text-secondary mb-4\">\n              {{ service.description }}\n            </p>\n            <router-link\n              :to=\"`/services/${service.id}`\"\n              class=\"text-brand-primary-600 hover:text-brand-primary-700 font-medium\"\n            >\n              Scopri di più →\n            </router-link>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- News Section -->\n    <section class=\"py-16\">\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center mb-12\">\n          <h2 class=\"text-3xl font-bold text-brand-text-primary mb-4\">\n            Ultime notizie\n          </h2>\n        </div>\n\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          <article\n            v-for=\"news in recentNews\"\n            :key=\"news.id\"\n            class=\"bg-white dark:bg-gray-700 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\"\n          >\n            <div class=\"p-6\">\n              <div class=\"text-sm text-brand-text-tertiary mb-2\">\n                {{ formatDate(news.created_at) }}\n              </div>\n              <h3 class=\"text-xl font-semibold text-brand-text-primary mb-3\">\n                {{ news.title }}\n              </h3>\n              <p class=\"text-brand-text-secondary\">\n                {{ news.excerpt }}\n              </p>\n            </div>\n          </article>\n        </div>\n      </div>\n    </section>\n\n    <!-- CTA Section -->\n    <section class=\"bg-brand-primary-600 text-white py-16\">\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <h2 class=\"text-3xl font-bold mb-4\">\n          Pronto a innovare la tua azienda?\n        </h2>\n        <p class=\"text-xl mb-8 text-brand-primary-100\">\n          Contattaci per una consulenza gratuita\n        </p>\n        <router-link\n          to=\"/contact\"\n          class=\"inline-block bg-white text-brand-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\"\n        >\n          Inizia ora\n        </router-link>\n      </div>\n    </section>\n\n    <!-- Footer -->\n    <PublicFooter />\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { useTenantStore } from '../../stores/tenant.js'\nimport PublicNavigation from '../../components/public/PublicNavigation.vue'\nimport PublicFooter from '../../components/public/PublicFooter.vue'\n\n// Store\nconst tenantStore = useTenantStore()\n\n// State\nconst featuredServices = ref([])\nconst recentNews = ref([])\nconst isLoading = ref(false)\n\n// Methods\nasync function loadData() {\n  isLoading.value = true\n  try {\n    // Load featured services\n    const servicesResponse = await fetch('/api/public/services/featured')\n    if (servicesResponse.ok) {\n      const servicesData = await servicesResponse.json()\n      featuredServices.value = servicesData.data || []\n    }\n\n    // Load recent news\n    const newsResponse = await fetch('/api/public/news/recent')\n    if (newsResponse.ok) {\n      const newsData = await newsResponse.json()\n      recentNews.value = newsData.data || []\n    }\n  } catch (error) {\n    console.error('Failed to load home data:', error)\n  } finally {\n    isLoading.value = false\n  }\n}\n\nfunction formatDate(date) {\n  return new Date(date).toLocaleDateString('it-IT', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\n// Lifecycle\nonMounted(() => {\n  loadData()\n})\n</script>\n\n<style scoped>\n/* Hero gradient animation */\n.bg-gradient-to-r {\n  background-size: 200% 200%;\n  animation: gradientShift 8s ease infinite;\n}\n\n@keyframes gradientShift {\n  0% { background-position: 0% 50%; }\n  50% { background-position: 100% 50%; }\n  100% { background-position: 0% 50%; }\n}\n\n/* Card hover effects */\n.hover\\\\:shadow-lg:hover {\n  transform: translateY(-2px);\n}\n\n/* Smooth transitions */\n.transition-shadow,\n.transition-colors {\n  transition-duration: 0.3s;\n}\n\n/* Responsive adjustments */\n@media (max-width: 640px) {\n  .text-4xl.md\\\\:text-6xl {\n    font-size: 2.5rem;\n  }\n\n  .text-xl.md\\\\:text-2xl {\n    font-size: 1.25rem;\n  }\n}\n</style>\n"}