{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/router/index.js"}, "originalCode": "/**\n * Vue Router Configuration\n * Definisce tutte le route dell'applicazione SPA\n */\n\n// Lazy load components for better performance\nconst Dashboard = () => import('../views/Dashboard.vue')\nconst Personnel = () => import('../views/Personnel.vue')\nconst Projects = () => import('../views/Projects.vue')\nconst ProjectDetail = () => import('../views/ProjectDetail.vue')\nconst Performance = () => import('../views/Performance.vue')\nconst NotFound = () => import('../views/NotFound.vue')\n\nexport const routes = [\n  // === DASHBOARD ===\n  {\n    path: '/',\n    name: 'dashboard',\n    component: Dashboard,\n    meta: {\n      title: 'Dashboard',\n      requiresAuth: true,\n      permissions: ['view_dashboard'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' }\n      ]\n    }\n  },\n\n  // === PERSONNEL ===\n  {\n    path: '/personnel',\n    name: 'personnel',\n    component: Personnel,\n    meta: {\n      title: 'Personale',\n      requiresAuth: true,\n      permissions: ['view_personnel'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Personale', path: '/personnel' }\n      ]\n    }\n  },\n  {\n    path: '/personnel/directory',\n    name: 'personnel-directory',\n    component: () => import('../views/PersonnelDirectory.vue'),\n    meta: {\n      title: 'Directory Personale',\n      requiresAuth: true,\n      permissions: ['view_personnel'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Personale', path: '/personnel' },\n        { name: 'Directory', path: '/personnel/directory' }\n      ]\n    }\n  },\n  {\n    path: '/personnel/orgchart',\n    name: 'personnel-orgchart',\n    component: () => import('../views/PersonnelOrgChart.vue'),\n    meta: {\n      title: 'Organigramma',\n      requiresAuth: true,\n      permissions: ['view_personnel'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Personale', path: '/personnel' },\n        { name: 'Organigramma', path: '/personnel/orgchart' }\n      ]\n    }\n  },\n  {\n    path: '/personnel/skills',\n    name: 'personnel-skills',\n    component: () => import('../views/PersonnelSkills.vue'),\n    meta: {\n      title: 'Competenze',\n      requiresAuth: true,\n      permissions: ['view_personnel'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Personale', path: '/personnel' },\n        { name: 'Competenze', path: '/personnel/skills' }\n      ]\n    }\n  },\n  {\n    path: '/personnel/departments',\n    name: 'personnel-departments',\n    component: () => import('../views/PersonnelDepartments.vue'),\n    meta: {\n      title: 'Dipartimenti',\n      requiresAuth: true,\n      permissions: ['manage_departments'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Personale', path: '/personnel' },\n        { name: 'Dipartimenti', path: '/personnel/departments' }\n      ]\n    }\n  },\n\n  // === PROJECTS ===\n  {\n    path: '/projects',\n    name: 'projects',\n    component: Projects,\n    meta: {\n      title: 'Progetti',\n      requiresAuth: true,\n      permissions: ['view_projects'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Progetti', path: '/projects' }\n      ]\n    }\n  },\n  {\n    path: '/projects/:id',\n    name: 'project-detail',\n    component: ProjectDetail,\n    meta: {\n      title: 'Dettaglio Progetto',\n      requiresAuth: true,\n      permissions: ['view_projects'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Progetti', path: '/projects' },\n        { name: 'Dettaglio', path: '' } // Will be updated dynamically\n      ]\n    }\n  },\n\n  // === CRM ===\n  {\n    path: '/crm',\n    name: 'crm',\n    component: () => import('../views/CRM.vue'),\n    meta: {\n      title: 'CRM',\n      requiresAuth: true,\n      permissions: ['view_crm'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'CRM', path: '/crm' }\n      ]\n    }\n  },\n  {\n    path: '/crm/clients',\n    name: 'crm-clients',\n    component: () => import('../views/CRMClients.vue'),\n    meta: {\n      title: 'Clienti',\n      requiresAuth: true,\n      permissions: ['view_crm'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'CRM', path: '/crm' },\n        { name: 'Clienti', path: '/crm/clients' }\n      ]\n    }\n  },\n\n  // === PRODUCTS ===\n  {\n    path: '/products',\n    name: 'products',\n    component: () => import('../views/Products.vue'),\n    meta: {\n      title: 'Prodotti e Servizi',\n      requiresAuth: true,\n      permissions: ['view_products'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Prodotti', path: '/products' }\n      ]\n    }\n  },\n\n  // === PERFORMANCE ===\n  {\n    path: '/performance',\n    name: 'performance',\n    component: Performance,\n    meta: {\n      title: 'Performance',\n      requiresAuth: true,\n      permissions: ['view_performance'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Performance', path: '/performance' }\n      ]\n    }\n  },\n  {\n    path: '/performance/kpi',\n    name: 'performance-kpi',\n    component: () => import('../views/PerformanceKPI.vue'),\n    meta: {\n      title: 'KPI',\n      requiresAuth: true,\n      permissions: ['view_performance'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Performance', path: '/performance' },\n        { name: 'KPI', path: '/performance/kpi' }\n      ]\n    }\n  },\n\n  // === COMMUNICATIONS ===\n  {\n    path: '/communications',\n    name: 'communications',\n    component: () => import('../views/Communications.vue'),\n    meta: {\n      title: 'Comunicazione',\n      requiresAuth: true,\n      permissions: ['view_communications'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Comunicazione', path: '/communications' }\n      ]\n    }\n  },\n  {\n    path: '/communications/news',\n    name: 'communications-news',\n    component: () => import('../views/CommunicationsNews.vue'),\n    meta: {\n      title: 'News',\n      requiresAuth: true,\n      permissions: ['view_communications'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Comunicazione', path: '/communications' },\n        { name: 'News', path: '/communications/news' }\n      ]\n    }\n  },\n\n  // === FUNDING ===\n  {\n    path: '/funding',\n    name: 'funding',\n    component: () => import('../views/Funding.vue'),\n    meta: {\n      title: 'Finanziamenti',\n      requiresAuth: true,\n      permissions: ['view_funding'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Finanziamenti', path: '/funding' }\n      ]\n    }\n  },\n  {\n    path: '/funding/opportunities',\n    name: 'funding-opportunities',\n    component: () => import('../views/FundingOpportunities.vue'),\n    meta: {\n      title: 'Opportunità',\n      requiresAuth: true,\n      permissions: ['view_funding'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Finanziamenti', path: '/funding' },\n        { name: 'Opportunità', path: '/funding/opportunities' }\n      ]\n    }\n  },\n\n  // === REPORTING ===\n  {\n    path: '/reporting',\n    name: 'reporting',\n    component: () => import('../views/Reporting.vue'),\n    meta: {\n      title: 'Rendicontazione',\n      requiresAuth: true,\n      permissions: ['view_reporting'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Rendicontazione', path: '/reporting' }\n      ]\n    }\n  },\n\n  // === STARTUP ===\n  {\n    path: '/startup',\n    name: 'startup',\n    component: () => import('../views/Startup.vue'),\n    meta: {\n      title: 'Startup',\n      requiresAuth: true,\n      permissions: ['view_startup'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Startup', path: '/startup' }\n      ]\n    }\n  },\n\n  // === ADMIN ===\n  {\n    path: '/admin',\n    name: 'admin',\n    component: () => import('../views/Admin.vue'),\n    meta: {\n      title: 'Amministrazione',\n      requiresAuth: true,\n      permissions: ['admin'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Admin', path: '/admin' }\n      ]\n    }\n  },\n  {\n    path: '/admin/kpi-templates',\n    name: 'admin-kpi-templates',\n    component: () => import('../views/AdminKPITemplates.vue'),\n    meta: {\n      title: 'Template KPI',\n      requiresAuth: true,\n      permissions: ['admin'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Admin', path: '/admin' },\n        { name: 'Template KPI', path: '/admin/kpi-templates' }\n      ]\n    }\n  },\n\n  // === PROFILE ===\n  {\n    path: '/profile',\n    name: 'profile',\n    component: () => import('../views/Profile.vue'),\n    meta: {\n      title: 'Profilo',\n      requiresAuth: true,\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Profilo', path: '/profile' }\n      ]\n    }\n  },\n\n  // === SETTINGS ===\n  {\n    path: '/settings',\n    name: 'settings',\n    component: () => import('../views/Settings.vue'),\n    meta: {\n      title: 'Impostazioni',\n      requiresAuth: true,\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Impostazioni', path: '/settings' }\n      ]\n    }\n  },\n\n  // === BRAND SETTINGS (Admin only) ===\n  {\n    path: '/settings/brand',\n    name: 'settings-brand',\n    component: () => import('../views/SettingsBrand.vue'),\n    meta: {\n      title: 'Impostazioni Brand',\n      requiresAuth: true,\n      permissions: ['admin'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Impostazioni', path: '/settings' },\n        { name: 'Brand', path: '/settings/brand' }\n      ]\n    }\n  },\n\n  // === ERROR PAGES ===\n  {\n    path: '/404',\n    name: 'not-found',\n    component: NotFound,\n    meta: {\n      title: 'Pagina non trovata',\n      requiresAuth: false\n    }\n  },\n\n  // === CATCH ALL ===\n  {\n    path: '/:pathMatch(.*)*',\n    redirect: '/404'\n  }\n]\n\nexport default routes\n", "modifiedCode": "/**\n * Vue Router Configuration\n * Definisce tutte le route dell'applicazione SPA\n */\n\n// Lazy load components for better performance\n// PUBLIC PAGES\nconst Home = () => import('../views/public/Home.vue')\nconst Services = () => import('../views/public/Services.vue')\nconst ServiceDetail = () => import('../views/public/ServiceDetail.vue')\nconst About = () => import('../views/public/About.vue')\nconst Contact = () => import('../views/public/Contact.vue')\nconst Privacy = () => import('../views/public/Privacy.vue')\n\n// PRIVATE PAGES\nconst Dashboard = () => import('../views/Dashboard.vue')\nconst Personnel = () => import('../views/Personnel.vue')\nconst Projects = () => import('../views/Projects.vue')\nconst ProjectDetail = () => import('../views/ProjectDetail.vue')\nconst Performance = () => import('../views/Performance.vue')\nconst NotFound = () => import('../views/NotFound.vue')\n\nexport const routes = [\n  // === PUBLIC ROUTES ===\n  {\n    path: '/',\n    name: 'home',\n    component: Home,\n    meta: {\n      title: 'DatVinci - Innovazione per il futuro',\n      requiresAuth: false,\n      layout: 'public'\n    }\n  },\n  {\n    path: '/services',\n    name: 'services',\n    component: Services,\n    meta: {\n      title: 'Servizi - DatVinci',\n      requiresAuth: false,\n      layout: 'public'\n    }\n  },\n  {\n    path: '/services/:id',\n    name: 'service-detail',\n    component: ServiceDetail,\n    meta: {\n      title: 'Dettaglio Servizio - DatVinci',\n      requiresAuth: false,\n      layout: 'public'\n    }\n  },\n  {\n    path: '/about',\n    name: 'about',\n    component: About,\n    meta: {\n      title: 'Chi Siamo - DatVinci',\n      requiresAuth: false,\n      layout: 'public'\n    }\n  },\n  {\n    path: '/contact',\n    name: 'contact',\n    component: Contact,\n    meta: {\n      title: 'Contatti - DatVinci',\n      requiresAuth: false,\n      layout: 'public'\n    }\n  },\n  {\n    path: '/privacy',\n    name: 'privacy',\n    component: Privacy,\n    meta: {\n      title: 'Privacy Policy - DatVinci',\n      requiresAuth: false,\n      layout: 'public'\n    }\n  },\n\n  // === PRIVATE ROUTES ===\n  {\n    path: '/dashboard',\n    name: 'dashboard',\n    component: Dashboard,\n    meta: {\n      title: 'Dashboard',\n      requiresAuth: true,\n      layout: 'private',\n      permissions: ['view_dashboard'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/dashboard' }\n      ]\n    }\n  },\n\n  // === PERSONNEL ===\n  {\n    path: '/personnel',\n    name: 'personnel',\n    component: Personnel,\n    meta: {\n      title: 'Personale',\n      requiresAuth: true,\n      permissions: ['view_personnel'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Personale', path: '/personnel' }\n      ]\n    }\n  },\n  {\n    path: '/personnel/directory',\n    name: 'personnel-directory',\n    component: () => import('../views/PersonnelDirectory.vue'),\n    meta: {\n      title: 'Directory Personale',\n      requiresAuth: true,\n      permissions: ['view_personnel'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Personale', path: '/personnel' },\n        { name: 'Directory', path: '/personnel/directory' }\n      ]\n    }\n  },\n  {\n    path: '/personnel/orgchart',\n    name: 'personnel-orgchart',\n    component: () => import('../views/PersonnelOrgChart.vue'),\n    meta: {\n      title: 'Organigramma',\n      requiresAuth: true,\n      permissions: ['view_personnel'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Personale', path: '/personnel' },\n        { name: 'Organigramma', path: '/personnel/orgchart' }\n      ]\n    }\n  },\n  {\n    path: '/personnel/skills',\n    name: 'personnel-skills',\n    component: () => import('../views/PersonnelSkills.vue'),\n    meta: {\n      title: 'Competenze',\n      requiresAuth: true,\n      permissions: ['view_personnel'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Personale', path: '/personnel' },\n        { name: 'Competenze', path: '/personnel/skills' }\n      ]\n    }\n  },\n  {\n    path: '/personnel/departments',\n    name: 'personnel-departments',\n    component: () => import('../views/PersonnelDepartments.vue'),\n    meta: {\n      title: 'Dipartimenti',\n      requiresAuth: true,\n      permissions: ['manage_departments'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Personale', path: '/personnel' },\n        { name: 'Dipartimenti', path: '/personnel/departments' }\n      ]\n    }\n  },\n\n  // === PROJECTS ===\n  {\n    path: '/projects',\n    name: 'projects',\n    component: Projects,\n    meta: {\n      title: 'Progetti',\n      requiresAuth: true,\n      permissions: ['view_projects'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Progetti', path: '/projects' }\n      ]\n    }\n  },\n  {\n    path: '/projects/:id',\n    name: 'project-detail',\n    component: ProjectDetail,\n    meta: {\n      title: 'Dettaglio Progetto',\n      requiresAuth: true,\n      permissions: ['view_projects'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Progetti', path: '/projects' },\n        { name: 'Dettaglio', path: '' } // Will be updated dynamically\n      ]\n    }\n  },\n\n  // === CRM ===\n  {\n    path: '/crm',\n    name: 'crm',\n    component: () => import('../views/CRM.vue'),\n    meta: {\n      title: 'CRM',\n      requiresAuth: true,\n      permissions: ['view_crm'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'CRM', path: '/crm' }\n      ]\n    }\n  },\n  {\n    path: '/crm/clients',\n    name: 'crm-clients',\n    component: () => import('../views/CRMClients.vue'),\n    meta: {\n      title: 'Clienti',\n      requiresAuth: true,\n      permissions: ['view_crm'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'CRM', path: '/crm' },\n        { name: 'Clienti', path: '/crm/clients' }\n      ]\n    }\n  },\n\n  // === PRODUCTS ===\n  {\n    path: '/products',\n    name: 'products',\n    component: () => import('../views/Products.vue'),\n    meta: {\n      title: 'Prodotti e Servizi',\n      requiresAuth: true,\n      permissions: ['view_products'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Prodotti', path: '/products' }\n      ]\n    }\n  },\n\n  // === PERFORMANCE ===\n  {\n    path: '/performance',\n    name: 'performance',\n    component: Performance,\n    meta: {\n      title: 'Performance',\n      requiresAuth: true,\n      permissions: ['view_performance'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Performance', path: '/performance' }\n      ]\n    }\n  },\n  {\n    path: '/performance/kpi',\n    name: 'performance-kpi',\n    component: () => import('../views/PerformanceKPI.vue'),\n    meta: {\n      title: 'KPI',\n      requiresAuth: true,\n      permissions: ['view_performance'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Performance', path: '/performance' },\n        { name: 'KPI', path: '/performance/kpi' }\n      ]\n    }\n  },\n\n  // === COMMUNICATIONS ===\n  {\n    path: '/communications',\n    name: 'communications',\n    component: () => import('../views/Communications.vue'),\n    meta: {\n      title: 'Comunicazione',\n      requiresAuth: true,\n      permissions: ['view_communications'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Comunicazione', path: '/communications' }\n      ]\n    }\n  },\n  {\n    path: '/communications/news',\n    name: 'communications-news',\n    component: () => import('../views/CommunicationsNews.vue'),\n    meta: {\n      title: 'News',\n      requiresAuth: true,\n      permissions: ['view_communications'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Comunicazione', path: '/communications' },\n        { name: 'News', path: '/communications/news' }\n      ]\n    }\n  },\n\n  // === FUNDING ===\n  {\n    path: '/funding',\n    name: 'funding',\n    component: () => import('../views/Funding.vue'),\n    meta: {\n      title: 'Finanziamenti',\n      requiresAuth: true,\n      permissions: ['view_funding'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Finanziamenti', path: '/funding' }\n      ]\n    }\n  },\n  {\n    path: '/funding/opportunities',\n    name: 'funding-opportunities',\n    component: () => import('../views/FundingOpportunities.vue'),\n    meta: {\n      title: 'Opportunità',\n      requiresAuth: true,\n      permissions: ['view_funding'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Finanziamenti', path: '/funding' },\n        { name: 'Opportunità', path: '/funding/opportunities' }\n      ]\n    }\n  },\n\n  // === REPORTING ===\n  {\n    path: '/reporting',\n    name: 'reporting',\n    component: () => import('../views/Reporting.vue'),\n    meta: {\n      title: 'Rendicontazione',\n      requiresAuth: true,\n      permissions: ['view_reporting'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Rendicontazione', path: '/reporting' }\n      ]\n    }\n  },\n\n  // === STARTUP ===\n  {\n    path: '/startup',\n    name: 'startup',\n    component: () => import('../views/Startup.vue'),\n    meta: {\n      title: 'Startup',\n      requiresAuth: true,\n      permissions: ['view_startup'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Startup', path: '/startup' }\n      ]\n    }\n  },\n\n  // === ADMIN ===\n  {\n    path: '/admin',\n    name: 'admin',\n    component: () => import('../views/Admin.vue'),\n    meta: {\n      title: 'Amministrazione',\n      requiresAuth: true,\n      permissions: ['admin'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Admin', path: '/admin' }\n      ]\n    }\n  },\n  {\n    path: '/admin/kpi-templates',\n    name: 'admin-kpi-templates',\n    component: () => import('../views/AdminKPITemplates.vue'),\n    meta: {\n      title: 'Template KPI',\n      requiresAuth: true,\n      permissions: ['admin'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Admin', path: '/admin' },\n        { name: 'Template KPI', path: '/admin/kpi-templates' }\n      ]\n    }\n  },\n\n  // === PROFILE ===\n  {\n    path: '/profile',\n    name: 'profile',\n    component: () => import('../views/Profile.vue'),\n    meta: {\n      title: 'Profilo',\n      requiresAuth: true,\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Profilo', path: '/profile' }\n      ]\n    }\n  },\n\n  // === SETTINGS ===\n  {\n    path: '/settings',\n    name: 'settings',\n    component: () => import('../views/Settings.vue'),\n    meta: {\n      title: 'Impostazioni',\n      requiresAuth: true,\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Impostazioni', path: '/settings' }\n      ]\n    }\n  },\n\n  // === BRAND SETTINGS (Admin only) ===\n  {\n    path: '/settings/brand',\n    name: 'settings-brand',\n    component: () => import('../views/SettingsBrand.vue'),\n    meta: {\n      title: 'Impostazioni Brand',\n      requiresAuth: true,\n      permissions: ['admin'],\n      breadcrumbs: [\n        { name: 'Dashboard', path: '/' },\n        { name: 'Impostazioni', path: '/settings' },\n        { name: 'Brand', path: '/settings/brand' }\n      ]\n    }\n  },\n\n  // === ERROR PAGES ===\n  {\n    path: '/404',\n    name: 'not-found',\n    component: NotFound,\n    meta: {\n      title: 'Pagina non trovata',\n      requiresAuth: false\n    }\n  },\n\n  // === CATCH ALL ===\n  {\n    path: '/:pathMatch(.*)*',\n    redirect: '/404'\n  }\n]\n\nexport default routes\n"}