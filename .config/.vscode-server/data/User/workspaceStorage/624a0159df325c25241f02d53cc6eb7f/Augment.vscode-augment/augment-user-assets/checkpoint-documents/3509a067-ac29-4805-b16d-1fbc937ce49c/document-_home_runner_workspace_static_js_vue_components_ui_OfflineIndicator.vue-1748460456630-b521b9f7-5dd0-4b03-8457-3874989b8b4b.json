{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/ui/OfflineIndicator.vue"}, "modifiedCode": "<template>\n  <div class=\"fixed bottom-4 left-4 bg-brand-warning-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-brand-slide-up\">\n    <div class=\"flex items-center\">\n      <i class=\"fas fa-wifi-slash mr-2\"></i>\n      <span class=\"text-sm font-medium\">Connessione assente</span>\n    </div>\n  </div>\n</template>\n\n<script setup>\n// Simple offline indicator component\n</script>\n\n<style scoped>\n/* Slide up animation */\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.animate-brand-slide-up {\n  animation: slideUp 0.3s ease-out;\n}\n</style>\n"}