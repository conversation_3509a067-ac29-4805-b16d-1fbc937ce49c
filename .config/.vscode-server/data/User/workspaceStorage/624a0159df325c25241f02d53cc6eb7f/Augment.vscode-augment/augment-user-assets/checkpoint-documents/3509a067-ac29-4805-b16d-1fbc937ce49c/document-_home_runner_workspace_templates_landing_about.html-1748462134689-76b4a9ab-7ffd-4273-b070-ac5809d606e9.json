{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/landing/about.html"}, "originalCode": "{% extends 'base.html' %}\n\n{% block title %}Chi Siamo - DatPortal | DatVinci{% endblock %}\n\n{% block public_content %}\n<section class=\"bg-gradient-to-r from-primary-600 to-secondary-500 text-white py-12\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center\">\n            <h1 class=\"text-3xl font-bold mb-4\">Chi Siamo</h1>\n            <p class=\"text-xl max-w-3xl mx-auto\">\n                Scopri la storia, la missione e il team dietro DatVinci e la piattaforma DatPortal.\n            </p>\n        </div>\n    </div>\n</section>\n\n<section class=\"py-12 bg-white dark:bg-gray-800\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center mb-16\">\n            <div>\n                <h2 class=\"text-3xl font-bold text-gray-900 dark:text-white mb-6\">La nostra storia</h2>\n                <div class=\"prose prose-lg max-w-none text-gray-600 dark:text-gray-300\">\n                    <p>\n                        Fondata nel {{ company_info.founded }}, DatVinci è nata dalla visione di un gruppo di professionisti con esperienza nel settore tecnologico, consulenza aziendale e innovazione digitale.\n                    </p>\n                    <p class=\"mt-4\">\n                        L'idea iniziale era semplice ma ambiziosa: creare soluzioni tecnologiche che permettessero alle aziende italiane, in particolare PMI e startup innovative, di accelerare la loro crescita attraverso la digitalizzazione e l'accesso a opportunità di finanziamento.\n                    </p>\n                    <p class=\"mt-4\">\n                        Da allora, siamo cresciuti fino a diventare un punto di riferimento nel nostro settore, aiutando centinaia di aziende a trasformare la loro operatività e accedere a risorse strategiche per l'innovazione.\n                    </p>\n                </div>\n            </div>\n            <div class=\"relative\">\n                <div class=\"aspect-w-16 aspect-h-9 rounded-lg overflow-hidden shadow-xl\">\n                    <div class=\"w-full h-full bg-gradient-to-br from-primary-400 to-secondary-500 flex items-center justify-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-24 w-24 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n                        </svg>\n                    </div>\n                </div>\n                <div class=\"absolute -bottom-6 -right-6 w-32 h-32 bg-primary-100 dark:bg-primary-900 rounded-lg z-0\"></div>\n            </div>\n        </div>\n        \n        <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center mb-16\">\n            <div class=\"order-2 lg:order-1 relative\">\n                <div class=\"aspect-w-4 aspect-h-3 rounded-lg overflow-hidden shadow-xl\">\n                    <div class=\"w-full h-full bg-gradient-to-br from-secondary-400 to-primary-500 flex items-center justify-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-24 w-24 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                        </svg>\n                    </div>\n                </div>\n                <div class=\"absolute -top-6 -left-6 w-32 h-32 bg-secondary-100 dark:bg-secondary-900 rounded-lg z-0\"></div>\n            </div>\n            <div class=\"order-1 lg:order-2\">\n                <h2 class=\"text-3xl font-bold text-gray-900 dark:text-white mb-6\">Missione e visione</h2>\n                <div class=\"prose prose-lg max-w-none text-gray-600 dark:text-gray-300\">\n                    <p>\n                        <strong class=\"text-primary-600 dark:text-primary-400\">La nostra missione</strong> è {{ company_info.mission }}\n                    </p>\n                    <p class=\"mt-4\">\n                        <strong class=\"text-primary-600 dark:text-primary-400\">La nostra visione</strong> è {{ company_info.vision }}\n                    </p>\n                    <p class=\"mt-4\">\n                        Crediamo fermamente che la trasformazione digitale, combinata con l'accesso a finanziamenti e bandi dedicati all'innovazione, rappresenti la chiave per la competitività delle imprese italiane, in particolare PMI e startup.\n                    </p>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"mb-16\">\n            <h2 class=\"text-3xl font-bold text-gray-900 dark:text-white mb-6 text-center\">Le nostre aree di competenza</h2>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n                {% for expertise in company_info.expertise %}\n                <div class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300\">\n                    <div class=\"text-primary-500 mb-4\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-10 w-10\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                        </svg>\n                    </div>\n                    <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-2\">{{ expertise }}</h3>\n                </div>\n                {% endfor %}\n            </div>\n        </div>\n        \n        <div>\n            <h2 class=\"text-3xl font-bold text-gray-900 dark:text-white mb-6 text-center\">Il nostro team</h2>\n            <p class=\"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto text-center mb-10\">\n                Il team di DatVinci è composto da {{ company_info.team_size }} tra cui sviluppatori, consulenti, esperti di finanza agevolata e specialisti in innovazione.\n            </p>\n            \n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n                {% for member in team %}\n                <div class=\"bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md transition-all duration-300 hover:shadow-lg\">\n                    <div class=\"h-48 bg-gradient-to-br from-primary-400 to-secondary-500 flex items-center justify-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-24 w-24 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                        </svg>\n                    </div>\n                    <div class=\"p-6\">\n                        <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-2\">{{ member.name }}</h3>\n                        <p class=\"text-primary-600 dark:text-primary-400 font-medium mb-3\">{{ member.role }}</p>\n                        <p class=\"text-gray-600 dark:text-gray-300\">\n                            {{ member.bio }}\n                        </p>\n                    </div>\n                </div>\n                {% endfor %}\n            </div>\n        </div>\n    </div>\n</section>\n\n<!-- CTA Section -->\n<section class=\"py-12 bg-gray-50 dark:bg-gray-700\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"lg:flex lg:items-center lg:justify-between\">\n            <div class=\"lg:w-0 lg:flex-1\">\n                <h2 class=\"text-2xl font-extrabold tracking-tight text-gray-900 dark:text-white sm:text-3xl\">\n                    Vuoi entrare in contatto con noi?\n                </h2>\n                <p class=\"mt-3 max-w-3xl text-lg text-gray-600 dark:text-gray-300\">\n                    Siamo sempre alla ricerca di nuove sfide e opportunità di collaborazione. Contattaci per discutere di come possiamo aiutarti a trasformare la tua azienda.\n                </p>\n            </div>\n            <div class=\"mt-8 lg:mt-0 lg:ml-8\">\n                <div class=\"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4\">\n                    <a href=\"{{ url_for('landing.contact') }}\" class=\"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 transition duration-300\">\n                        Contattaci\n                    </a>\n                    <a href=\"{{ url_for('landing.services') }}\" class=\"inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-300\">\n                        I nostri servizi\n                    </a>\n                </div>\n            </div>\n        </div>\n    </div>\n</section>\n{% endblock %}"}