{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/ui/ModalContainer.vue"}, "modifiedCode": "<template>\n  <div>\n    <transition-group name=\"modal\" tag=\"div\">\n      <div\n        v-for=\"modal in appStore.activeModals\"\n        :key=\"modal.id\"\n        class=\"fixed inset-0 z-50 overflow-y-auto\"\n        @click=\"handleBackdropClick(modal)\"\n      >\n        <!-- Backdrop -->\n        <div class=\"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n          <div class=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\"></div>\n          \n          <!-- Modal Content -->\n          <div\n            class=\"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\"\n            :class=\"getModalSizeClass(modal.options.size)\"\n            @click.stop\n          >\n            <!-- Header -->\n            <div \n              v-if=\"modal.options.title || modal.options.closable\"\n              class=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4 border-b border-gray-200 dark:border-gray-700\"\n            >\n              <div class=\"flex items-center justify-between\">\n                <h3 \n                  v-if=\"modal.options.title\"\n                  class=\"text-lg leading-6 font-medium text-brand-text-primary\"\n                >\n                  {{ modal.options.title }}\n                </h3>\n                <button\n                  v-if=\"modal.options.closable\"\n                  @click=\"appStore.hideModal(modal.id)\"\n                  class=\"text-brand-text-tertiary hover:text-brand-text-secondary transition-colors\"\n                >\n                  <i class=\"fas fa-times h-5 w-5\"></i>\n                </button>\n              </div>\n            </div>\n            \n            <!-- Body -->\n            <div class=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6\">\n              <component \n                :is=\"modal.component\" \n                v-bind=\"modal.props\"\n                @close=\"appStore.hideModal(modal.id)\"\n                @confirm=\"handleConfirm(modal, $event)\"\n                @cancel=\"handleCancel(modal)\"\n              />\n            </div>\n            \n            <!-- Footer (if actions are provided) -->\n            <div \n              v-if=\"modal.options.actions\"\n              class=\"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\"\n            >\n              <button\n                v-for=\"action in modal.options.actions\"\n                :key=\"action.label\"\n                @click=\"handleActionClick(modal, action)\"\n                class=\"w-full inline-flex justify-center rounded-md border px-4 py-2 text-base font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm transition-colors\"\n                :class=\"getActionClasses(action.type)\"\n              >\n                <i v-if=\"action.icon\" :class=\"[action.icon, 'mr-2 h-4 w-4']\"></i>\n                {{ action.label }}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </transition-group>\n  </div>\n</template>\n\n<script setup>\nimport { useAppStore } from '../../stores/app.js'\n\n// Store\nconst appStore = useAppStore()\n\n// Methods\nfunction getModalSizeClass(size) {\n  const sizes = {\n    sm: 'sm:max-w-sm',\n    md: 'sm:max-w-md',\n    lg: 'sm:max-w-lg',\n    xl: 'sm:max-w-xl',\n    '2xl': 'sm:max-w-2xl',\n    '3xl': 'sm:max-w-3xl',\n    '4xl': 'sm:max-w-4xl',\n    '5xl': 'sm:max-w-5xl',\n    '6xl': 'sm:max-w-6xl',\n    full: 'sm:max-w-full sm:m-4'\n  }\n  return sizes[size] || sizes.lg\n}\n\nfunction getActionClasses(type) {\n  const classes = {\n    primary: 'border-transparent text-white bg-brand-primary-600 hover:bg-brand-primary-700 focus:ring-brand-primary-500',\n    secondary: 'border-gray-300 dark:border-gray-600 text-brand-text-primary bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-brand-primary-500',\n    danger: 'border-transparent text-white bg-brand-error-600 hover:bg-brand-error-700 focus:ring-brand-error-500',\n    success: 'border-transparent text-white bg-brand-success-600 hover:bg-brand-success-700 focus:ring-brand-success-500',\n    warning: 'border-transparent text-white bg-brand-warning-600 hover:bg-brand-warning-700 focus:ring-brand-warning-500'\n  }\n  return classes[type] || classes.secondary\n}\n\nfunction handleBackdropClick(modal) {\n  if (modal.options.backdrop && modal.options.closable) {\n    appStore.hideModal(modal.id)\n  }\n}\n\nfunction handleConfirm(modal, data) {\n  if (modal.options.onConfirm) {\n    modal.options.onConfirm(data)\n  }\n  appStore.hideModal(modal.id)\n}\n\nfunction handleCancel(modal) {\n  if (modal.options.onCancel) {\n    modal.options.onCancel()\n  }\n  appStore.hideModal(modal.id)\n}\n\nfunction handleActionClick(modal, action) {\n  if (action.handler) {\n    action.handler(modal)\n  }\n  \n  if (action.closeModal !== false) {\n    appStore.hideModal(modal.id)\n  }\n}\n</script>\n\n<style scoped>\n/* Modal animations */\n.modal-enter-active {\n  transition: opacity 0.3s ease-out;\n}\n\n.modal-leave-active {\n  transition: opacity 0.3s ease-in;\n}\n\n.modal-enter-from,\n.modal-leave-to {\n  opacity: 0;\n}\n\n.modal-enter-active .inline-block {\n  transition: all 0.3s ease-out;\n}\n\n.modal-leave-active .inline-block {\n  transition: all 0.3s ease-in;\n}\n\n.modal-enter-from .inline-block {\n  opacity: 0;\n  transform: translateY(-20px) scale(0.95);\n}\n\n.modal-leave-to .inline-block {\n  opacity: 0;\n  transform: translateY(-20px) scale(0.95);\n}\n\n/* Backdrop animation */\n.modal-enter-active .fixed.inset-0.bg-gray-500 {\n  transition: opacity 0.3s ease-out;\n}\n\n.modal-leave-active .fixed.inset-0.bg-gray-500 {\n  transition: opacity 0.3s ease-in;\n}\n\n.modal-enter-from .fixed.inset-0.bg-gray-500,\n.modal-leave-to .fixed.inset-0.bg-gray-500 {\n  opacity: 0;\n}\n\n/* Focus trap */\n.modal-container {\n  isolation: isolate;\n}\n\n/* Scrollbar styling for modal content */\n.overflow-y-auto::-webkit-scrollbar {\n  width: 6px;\n}\n\n.overflow-y-auto::-webkit-scrollbar-track {\n  background: var(--brand-bg-tertiary);\n}\n\n.overflow-y-auto::-webkit-scrollbar-thumb {\n  background: var(--brand-border-secondary);\n  border-radius: var(--brand-radius-full);\n}\n\n.overflow-y-auto::-webkit-scrollbar-thumb:hover {\n  background: var(--brand-primary-400);\n}\n\n/* Responsive adjustments */\n@media (max-width: 640px) {\n  .sm\\\\:max-w-lg {\n    max-width: calc(100vw - 2rem);\n    margin: 1rem;\n  }\n  \n  .sm\\\\:flex-row-reverse {\n    flex-direction: column;\n  }\n  \n  .sm\\\\:ml-3 {\n    margin-left: 0;\n    margin-top: 0.75rem;\n  }\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .modal-enter-active,\n  .modal-leave-active,\n  .transition-all,\n  .transition-colors,\n  .transition-opacity {\n    transition: none;\n  }\n}\n\n/* High contrast mode */\n@media (prefers-contrast: high) {\n  .border-gray-300 {\n    border-color: #000;\n  }\n  \n  .bg-gray-50 {\n    background-color: #f0f0f0;\n  }\n}\n</style>\n"}