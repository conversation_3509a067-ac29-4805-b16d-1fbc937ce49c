{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/conftest.py"}, "originalCode": "import pytest\nimport sys\nimport os\nfrom datetime import datetime, timedelta\nsys.path.insert(0, os.path.abspath(os.path.dirname(__file__) + '/../'))\nfrom app import create_app\nfrom extensions import db\nfrom models import User, Project, Client, Task, KPI, ProjectKPI, Department, Skill, UserSkill, UserProfile\n# Importa altri modelli se necessario per i test\n\****************(scope='session')\ndef app():\n    \"\"\"Crea e configura una nuova istanza dell'app per ogni sessione di test.\"\"\"\n    app_instance = create_app(config_overrides={\n        'TESTING': True,\n        'SQLALCHEMY_DATABASE_URI': 'sqlite:///:memory:',\n        'WTF_CSRF_ENABLED': False,\n        'LOGIN_DISABLED': False,\n        'SERVER_NAME': 'localhost.localdomain',\n        'PASSWORD_RESET_TOKEN_EXPIRATION_SECONDS': 60 # Valore basso per testare la scadenza\n    })\n\n    with app_instance.app_context():\n        db.create_all()\n        yield app_instance\n        db.session.remove()\n        db.drop_all()\n\****************(scope='function')\ndef client(app):\n    \"\"\"Un client di test Flask per l'app.\"\"\"\n    return app.test_client()\n\****************(scope='function')\ndef runner(app):\n    \"\"\"Un test runner per i comandi CLI dell'app.\"\"\"\n    return app.test_cli_runner()\n\****************(scope='function')\ndef init_database(app):\n    \"\"\"Pulisce i dati dalla tabella User prima di ogni test per evitare conflitti di unicità.\"\"\"\n    with app.app_context():\n        # Importa User qui per evitare import circolari e assicurarti che sia nel contesto app\n        from models import User\n        # Cancella tutti gli utenti\n        # Questo è un approccio semplice. Per app più complesse potresti dover\n        # gestire l'ordine di cancellazione o disabilitare temporaneamente i vincoli FK.\n        db.session.query(User).delete()\n        db.session.commit()\n    # Il resto della creazione/distruzione del DB è gestito dalla fixture 'app' session-scoped.\n\****************(scope='function')\ndef new_user_data():\n    \"\"\"Dati per un nuovo utente di test.\"\"\"\n    return {\n        'username': 'testuser',\n        'email': '<EMAIL>',\n        'password': 'testpassword',\n        'first_name': 'Test',\n        'last_name': 'User'\n    }\n\****************(scope='function')\ndef created_user(app, new_user_data, init_database):\n    \"\"\"Fixture per creare e salvare un utente di test nel db. Restituisce l'ID dell'utente.\"\"\"\n    with app.app_context():\n        user = User(\n            username=new_user_data['username'],\n            email=new_user_data['email'],\n            first_name=new_user_data['first_name'],\n            last_name=new_user_data['last_name'],\n            is_active=True\n        )\n        user.set_password(new_user_data['password'])\n        db.session.add(user)\n        db.session.commit()\n        user_id = user.id # Salva l'ID prima che la sessione potenziale venga chiusa\n        return user_id\n\****************(scope='function')\ndef test_user_id(created_user):\n    \"\"\"Alias per created_user, per compatibilità con i test esistenti.\"\"\"\n    return created_user\n\****************(scope='function')\ndef logged_in_client(client, app, created_user_id, new_user_data): # Rinominato created_user in created_user_id\n    \"\"\"Un client di test Flask con un utente loggato.\"\"\"\n    with app.app_context(): # Assicurati di essere in un contesto app per query DB\n        user = db.session.get(User, created_user_id) # Recupera l'utente dal DB\n        if user:\n            client.post('/auth/login', data=dict(\n                username=user.username, # Usa l'username dell'oggetto recuperato\n                password=new_user_data['password']\n            ), follow_redirects=True)\n        else:\n            pytest.fail(f\"User with id {created_user_id} not found for login in fixture.\")\n    return client\n\****************(scope='function')\ndef db_session(app):\n    \"\"\"Restituisce la sessione del db per i test che la richiedono.\"\"\"\n    from extensions import db\n    yield db.session\n    db.session.rollback()\n\****************(scope='function')\ndef test_user(app, created_user): # Usa created_user invece di created_user_id\n    \"\"\"Alias per compatibilità con i test che richiedono test_user.\"\"\"\n    with app.app_context():\n        user = db.session.get(User, created_user)\n        if not user:\n            pytest.fail(f\"test_user (id: {created_user}) not found in fixture.\")\n        return user\n\****************(scope='function')\ndef admin_user(app, init_database):\n    \"\"\"Crea e restituisce un utente admin per i test. Restituisce l'ID dell'utente.\"\"\"\n    from models import User # Assicurati che User sia importato\n    with app.app_context():\n        admin = User(\n            username='adminuser',\n            email='<EMAIL>',\n            first_name='Admin',\n            last_name='User',\n            is_active=True,\n            role='admin'\n        )\n        admin.set_password('adminpassword')\n        # from app import db # db dovrebbe essere già importato a livello di modulo\n        db.session.add(admin)\n        db.session.commit()\n        admin_id = admin.id # Salva l'ID\n        return admin_id\n\****************(scope='function')\ndef admin_user_id(admin_user):\n    \"\"\"Alias per admin_user.\"\"\"\n    return admin_user\n\****************(scope='function')\ndef auth(client, new_user_data, created_user, sample_users):\n    \"\"\"Helper per autenticare un utente nei test.\"\"\"\n    class AuthActions:\n        def login(self, username=None, password=None):\n            # Se non specificato, usa l'utente admin di default per i test API\n            default_username = 'admin' if sample_users else new_user_data['username']\n            default_password = 'password' if sample_users else new_user_data['password']\n\n            return client.post('/auth/login', data={\n                'username': username or default_username,\n                'password': password or default_password\n            }, follow_redirects=True)\n\n        def logout(self):\n            return client.get('/auth/logout', follow_redirects=True)\n\n    return AuthActions()\n\****************(scope='function')\ndef test_client(app, init_database):\n    \"\"\"Fixture per creare un client di test nel db.\"\"\"\n    with app.app_context():\n        client = Client(\n            name='Test Client',\n            industry='Technology',\n            description='A test client for API testing',\n            website='https://example.com'\n        )\n        db.session.add(client)\n        db.session.commit()\n        client_id = client.id\n        return client_id\n\****************(scope='function')\ndef test_project(app, init_database, test_client, test_user):\n    \"\"\"Fixture per creare un progetto di test nel db.\"\"\"\n    with app.app_context():\n        project = Project(\n            name='Test Project',\n            description='A test project for API testing',\n            client_id=test_client,  # test_client ora è un ID\n            start_date=datetime.utcnow().date(),\n            end_date=(datetime.utcnow() + timedelta(days=30)).date(),\n            status='active',\n            budget=10000.0,\n            expenses=2000.0\n        )\n        db.session.add(project)\n        db.session.commit()\n        project_id = project.id\n        return project_id\n\****************(scope='function')\ndef test_task(app, init_database, test_project, test_user):\n    \"\"\"Fixture per creare un task di test nel db.\"\"\"\n    with app.app_context():\n        task = Task(\n            name='Test Task',\n            description='A test task for API testing',\n            project_id=test_project.id,\n            assignee_id=test_user.id,\n            status='todo',\n            priority='medium',\n            due_date=(datetime.utcnow() + timedelta(days=7)).date()\n        )\n        db.session.add(task)\n        db.session.commit()\n        return task\n\****************(scope='function')\ndef test_kpi(app, init_database):\n    \"\"\"Fixture per creare un KPI di test nel db.\"\"\"\n    with app.app_context():\n        kpi = KPI(\n            name='Test KPI',\n            description='A test KPI for API testing',\n            category='Test',\n            target_value=100.0,\n            current_value=50.0,\n            unit='%',\n            frequency='monthly'\n        )\n        db.session.add(kpi)\n        db.session.commit()\n        kpi_id = kpi.id\n        return kpi_id\n\****************(scope='function')\ndef test_project_kpi(app, init_database, test_project, test_kpi):\n    \"\"\"Fixture per creare un ProjectKPI di test nel db.\"\"\"\n    with app.app_context():\n        project_kpi = ProjectKPI(\n            project_id=test_project,  # test_project ora è un ID\n            kpi_id=test_kpi,  # test_kpi ora è un ID\n            target_value=200.0,\n            current_value=100.0\n        )\n        db.session.add(project_kpi)\n        db.session.commit()\n        project_kpi_id = project_kpi.id\n        return project_kpi_id\n\****************(scope='function')\ndef admin_auth(client, admin_user):\n    \"\"\"Helper per autenticare un admin nei test.\"\"\"\n    class AdminAuthActions:\n        def login(self):\n            # Prima fai logout se c'è una sessione attiva\n            client.get('/auth/logout', follow_redirects=True)\n            # Poi fai login come admin\n            return client.post('/auth/login', data={\n                'username': 'adminuser',\n                'password': 'adminpassword'\n            }, follow_redirects=True)\n\n        def logout(self):\n            return client.get('/auth/logout', follow_redirects=True)\n\n    return AdminAuthActions()\n\n# Personnel-related fixtures\n\****************(scope='function')\ndef sample_departments(app, init_database):\n    \"\"\"Fixture per creare dipartimenti di test nel db.\"\"\"\n    with app.app_context():\n        # Check if departments already exist\n        dept_names = ['Engineering', 'Marketing', 'Human Resources', 'Sales']\n        departments = []\n\n        for name in dept_names:\n            dept = Department.query.filter_by(name=name).first()\n            if not dept:\n                if name == 'Engineering':\n                    dept = Department(name=name, description='Software development and engineering')\n                elif name == 'Marketing':\n                    dept = Department(name=name, description='Marketing and communications')\n                elif name == 'Human Resources':\n                    dept = Department(name=name, description='HR and people operations')\n                elif name == 'Sales':\n                    dept = Department(name=name, description='Sales and business development')\n                db.session.add(dept)\n            departments.append(dept)\n\n        db.session.commit()\n        return departments\n\****************(scope='function')\ndef sample_skills(app, init_database):\n    \"\"\"Fixture per creare competenze di test nel db.\"\"\"\n    with app.app_context():\n        skills = [\n            Skill(\n                name='Python',\n                category='Programming',\n                description='Python programming language'\n            ),\n            Skill(\n                name='JavaScript',\n                category='Programming',\n                description='JavaScript programming language'\n            ),\n            Skill(\n                name='Project Management',\n                category='Management',\n                description='Project planning and execution'\n            ),\n            Skill(\n                name='Digital Marketing',\n                category='Marketing',\n                description='Online marketing and social media'\n            ),\n            Skill(\n                name='Data Analysis',\n                category='Analytics',\n                description='Data analysis and visualization'\n            )\n        ]\n\n        for skill in skills:\n            db.session.add(skill)\n        db.session.commit()\n\n        return skills\n\****************(scope='function')\ndef sample_users(app, init_database):\n    \"\"\"Fixture per creare utenti di test nel db con dipartimenti.\"\"\"\n    with app.app_context():\n        # Create departments first (check if they exist)\n        dept_names = ['Engineering', 'Marketing', 'Human Resources', 'Sales']\n        departments = []\n\n        for name in dept_names:\n            dept = Department.query.filter_by(name=name).first()\n            if not dept:\n                if name == 'Engineering':\n                    dept = Department(name=name, description='Software development and engineering')\n                elif name == 'Marketing':\n                    dept = Department(name=name, description='Marketing and communications')\n                elif name == 'Human Resources':\n                    dept = Department(name=name, description='HR and people operations')\n                elif name == 'Sales':\n                    dept = Department(name=name, description='Sales and business development')\n                db.session.add(dept)\n            departments.append(dept)\n\n        db.session.flush()  # Get IDs without committing\n\n        users = [\n            User(\n                username='admin',\n                email='<EMAIL>',\n                first_name='Admin',\n                last_name='User',\n                role='admin',\n                department_id=departments[0].id,  # Engineering\n                position='System Administrator',\n                hire_date=datetime.utcnow().date() - timedelta(days=365),\n                phone='+1234567890',\n                is_active=True\n            ),\n            User(\n                username='manager',\n                email='<EMAIL>',\n                first_name='Manager',\n                last_name='User',\n                role='manager',\n                department_id=departments[0].id,  # Engineering\n                position='Engineering Manager',\n                hire_date=datetime.utcnow().date() - timedelta(days=200),\n                phone='+1234567891',\n                is_active=True\n            ),\n            User(\n                username='employee',\n                email='<EMAIL>',\n                first_name='Employee',\n                last_name='User',\n                role='employee',\n                department_id=departments[1].id,  # Marketing\n                position='Marketing Specialist',\n                hire_date=datetime.utcnow().date() - timedelta(days=100),\n                phone='+1234567892',\n                is_active=True\n            ),\n            User(\n                username='hr_user',\n                email='<EMAIL>',\n                first_name='HR',\n                last_name='User',\n                role='human_resources',\n                department_id=departments[2].id,  # HR\n                position='HR Specialist',\n                hire_date=datetime.utcnow().date() - timedelta(days=150),\n                phone='+1234567893',\n                is_active=True\n            )\n        ]\n\n        for user in users:\n            user.set_password('password')\n            db.session.add(user)\n\n        db.session.commit()\n        return users\n\****************(scope='function')\ndef sample_user_skills(app, sample_users, sample_skills):\n    \"\"\"Fixture per creare competenze utente di test nel db.\"\"\"\n    with app.app_context():\n        # Get fresh instances from database\n        users = User.query.order_by(User.id).all()\n        skills = Skill.query.order_by(Skill.id).all()\n\n        if len(users) >= 3 and len(skills) >= 4:\n            user_skills = [\n                UserSkill(\n                    user_id=users[0].id,  # Admin\n                    skill_id=skills[0].id,  # Python\n                    proficiency_level=5,\n                    years_experience=5,\n                    is_certified=True\n                ),\n                UserSkill(\n                    user_id=users[0].id,  # Admin\n                    skill_id=skills[2].id,  # Project Management\n                    proficiency_level=4,\n                    years_experience=3,\n                    is_certified=False\n                ),\n                UserSkill(\n                    user_id=users[1].id,  # Manager\n                    skill_id=skills[1].id,  # JavaScript\n                    proficiency_level=4,\n                    years_experience=4,\n                    is_certified=True\n                ),\n                UserSkill(\n                    user_id=users[2].id,  # Employee\n                    skill_id=skills[3].id,  # Digital Marketing\n                    proficiency_level=3,\n                    years_experience=2,\n                    is_certified=False\n                )\n            ]\n\n            for user_skill in user_skills:\n                db.session.add(user_skill)\n\n            db.session.commit()\n            return user_skills\n\n        return []\n\****************(scope='function')\ndef sample_user_profiles(app, sample_users):\n    \"\"\"Fixture per creare profili utente di test nel db.\"\"\"\n    with app.app_context():\n        # Get fresh instances from database\n        users = User.query.order_by(User.id).all()\n\n        if len(users) >= 2:\n            profiles = [\n                UserProfile(\n                    user_id=users[0].id,  # Admin\n                    emergency_contact_name='Emergency Contact 1',\n                    emergency_contact_phone='+1234567800',\n                    address='123 Admin Street',\n                    profile_completion=95.0,\n                    notes='Admin user profile'\n                ),\n                UserProfile(\n                    user_id=users[1].id,  # Manager\n                    emergency_contact_name='Emergency Contact 2',\n                    emergency_contact_phone='+1234567801',\n                    address='456 Manager Avenue',\n                    profile_completion=80.0,\n                    notes='Manager user profile'\n                )\n            ]\n\n            for profile in profiles:\n                db.session.add(profile)\n\n            db.session.commit()\n            return profiles\n\n        return []", "modifiedCode": "import pytest\nimport sys\nimport os\nfrom datetime import datetime, timedelta\nsys.path.insert(0, os.path.abspath(os.path.dirname(__file__) + '/../'))\nfrom app import create_app\nfrom extensions import db\nfrom models import User, Project, Client, Task, KPI, ProjectKPI, Department, Skill, UserSkill, UserProfile\n# Importa altri modelli se necessario per i test\n\****************(scope='session')\ndef app():\n    \"\"\"Crea e configura una nuova istanza dell'app per ogni sessione di test.\"\"\"\n    app_instance = create_app(config_overrides={\n        'TESTING': True,\n        'SQLALCHEMY_DATABASE_URI': 'sqlite:///:memory:',\n        'WTF_CSRF_ENABLED': False,\n        'LOGIN_DISABLED': False,\n        'SERVER_NAME': 'localhost.localdomain',\n        'PASSWORD_RESET_TOKEN_EXPIRATION_SECONDS': 60 # Valore basso per testare la scadenza\n    })\n\n    with app_instance.app_context():\n        db.create_all()\n        yield app_instance\n        db.session.remove()\n        db.drop_all()\n\****************(scope='function')\ndef client(app):\n    \"\"\"Un client di test Flask per l'app.\"\"\"\n    return app.test_client()\n\****************(scope='function')\ndef runner(app):\n    \"\"\"Un test runner per i comandi CLI dell'app.\"\"\"\n    return app.test_cli_runner()\n\****************(scope='function')\ndef init_database(app):\n    \"\"\"Pulisce i dati dalla tabella User prima di ogni test per evitare conflitti di unicità.\"\"\"\n    with app.app_context():\n        # Importa User qui per evitare import circolari e assicurarti che sia nel contesto app\n        from models import User\n        # Cancella tutti gli utenti\n        # Questo è un approccio semplice. Per app più complesse potresti dover\n        # gestire l'ordine di cancellazione o disabilitare temporaneamente i vincoli FK.\n        db.session.query(User).delete()\n        db.session.commit()\n    # Il resto della creazione/distruzione del DB è gestito dalla fixture 'app' session-scoped.\n\****************(scope='function')\ndef new_user_data():\n    \"\"\"Dati per un nuovo utente di test.\"\"\"\n    return {\n        'username': 'testuser',\n        'email': '<EMAIL>',\n        'password': 'testpassword',\n        'first_name': 'Test',\n        'last_name': 'User'\n    }\n\****************(scope='function')\ndef created_user(app, new_user_data, init_database):\n    \"\"\"Fixture per creare e salvare un utente di test nel db. Restituisce l'ID dell'utente.\"\"\"\n    with app.app_context():\n        user = User(\n            username=new_user_data['username'],\n            email=new_user_data['email'],\n            first_name=new_user_data['first_name'],\n            last_name=new_user_data['last_name'],\n            is_active=True\n        )\n        user.set_password(new_user_data['password'])\n        db.session.add(user)\n        db.session.commit()\n        user_id = user.id # Salva l'ID prima che la sessione potenziale venga chiusa\n        return user_id\n\****************(scope='function')\ndef test_user_id(created_user):\n    \"\"\"Alias per created_user, per compatibilità con i test esistenti.\"\"\"\n    return created_user\n\****************(scope='function')\ndef logged_in_client(client, app, created_user_id, new_user_data): # Rinominato created_user in created_user_id\n    \"\"\"Un client di test Flask con un utente loggato.\"\"\"\n    with app.app_context(): # Assicurati di essere in un contesto app per query DB\n        user = db.session.get(User, created_user_id) # Recupera l'utente dal DB\n        if user:\n            client.post('/auth/login', data=dict(\n                username=user.username, # Usa l'username dell'oggetto recuperato\n                password=new_user_data['password']\n            ), follow_redirects=True)\n        else:\n            pytest.fail(f\"User with id {created_user_id} not found for login in fixture.\")\n    return client\n\****************(scope='function')\ndef db_session(app):\n    \"\"\"Restituisce la sessione del db per i test che la richiedono.\"\"\"\n    from extensions import db\n    yield db.session\n    db.session.rollback()\n\****************(scope='function')\ndef test_user(app, created_user): # Usa created_user invece di created_user_id\n    \"\"\"Alias per compatibilità con i test che richiedono test_user.\"\"\"\n    with app.app_context():\n        user = db.session.get(User, created_user)\n        if not user:\n            pytest.fail(f\"test_user (id: {created_user}) not found in fixture.\")\n        return user\n\****************(scope='function')\ndef admin_user(app, init_database):\n    \"\"\"Crea e restituisce un utente admin per i test. Restituisce l'ID dell'utente.\"\"\"\n    from models import User # Assicurati che User sia importato\n    with app.app_context():\n        admin = User(\n            username='adminuser',\n            email='<EMAIL>',\n            first_name='Admin',\n            last_name='User',\n            is_active=True,\n            role='admin'\n        )\n        admin.set_password('adminpassword')\n        # from app import db # db dovrebbe essere già importato a livello di modulo\n        db.session.add(admin)\n        db.session.commit()\n        admin_id = admin.id # Salva l'ID\n        return admin_id\n\****************(scope='function')\ndef admin_user_id(admin_user):\n    \"\"\"Alias per admin_user.\"\"\"\n    return admin_user\n\****************(scope='function')\ndef auth(client, new_user_data, created_user, sample_users):\n    \"\"\"Helper per autenticare un utente nei test.\"\"\"\n    class AuthActions:\n        def login(self, username=None, password=None):\n            # Se non specificato, usa l'utente admin di default per i test API\n            default_username = 'admin' if sample_users else new_user_data['username']\n            default_password = 'password' if sample_users else new_user_data['password']\n\n            return client.post('/auth/login', data={\n                'username': username or default_username,\n                'password': password or default_password\n            }, follow_redirects=True)\n\n        def logout(self):\n            return client.get('/auth/logout', follow_redirects=True)\n\n    return AuthActions()\n\****************(scope='function')\ndef test_client(app, init_database):\n    \"\"\"Fixture per creare un client di test nel db.\"\"\"\n    with app.app_context():\n        client = Client(\n            name='Test Client',\n            industry='Technology',\n            description='A test client for API testing',\n            website='https://example.com'\n        )\n        db.session.add(client)\n        db.session.commit()\n        client_id = client.id\n        return client_id\n\****************(scope='function')\ndef test_project(app, init_database, test_client, test_user):\n    \"\"\"Fixture per creare un progetto di test nel db.\"\"\"\n    with app.app_context():\n        project = Project(\n            name='Test Project',\n            description='A test project for API testing',\n            client_id=test_client,  # test_client ora è un ID\n            start_date=datetime.utcnow().date(),\n            end_date=(datetime.utcnow() + timedelta(days=30)).date(),\n            status='active',\n            budget=10000.0,\n            expenses=2000.0\n        )\n        db.session.add(project)\n        db.session.commit()\n        project_id = project.id\n        return project_id\n\****************(scope='function')\ndef test_task(app, init_database, test_project, test_user):\n    \"\"\"Fixture per creare un task di test nel db.\"\"\"\n    with app.app_context():\n        task = Task(\n            name='Test Task',\n            description='A test task for API testing',\n            project_id=test_project.id,\n            assignee_id=test_user.id,\n            status='todo',\n            priority='medium',\n            due_date=(datetime.utcnow() + timedelta(days=7)).date()\n        )\n        db.session.add(task)\n        db.session.commit()\n        return task\n\****************(scope='function')\ndef test_kpi(app, init_database):\n    \"\"\"Fixture per creare un KPI di test nel db.\"\"\"\n    with app.app_context():\n        kpi = KPI(\n            name='Test KPI',\n            description='A test KPI for API testing',\n            category='Test',\n            target_value=100.0,\n            current_value=50.0,\n            unit='%',\n            frequency='monthly'\n        )\n        db.session.add(kpi)\n        db.session.commit()\n        kpi_id = kpi.id\n        return kpi_id\n\****************(scope='function')\ndef test_project_kpi(app, init_database, test_project, test_kpi):\n    \"\"\"Fixture per creare un ProjectKPI di test nel db.\"\"\"\n    with app.app_context():\n        project_kpi = ProjectKPI(\n            project_id=test_project,  # test_project ora è un ID\n            kpi_id=test_kpi,  # test_kpi ora è un ID\n            target_value=200.0,\n            current_value=100.0\n        )\n        db.session.add(project_kpi)\n        db.session.commit()\n        project_kpi_id = project_kpi.id\n        return project_kpi_id\n\****************(scope='function')\ndef admin_auth(client, admin_user):\n    \"\"\"Helper per autenticare un admin nei test.\"\"\"\n    class AdminAuthActions:\n        def login(self):\n            # Prima fai logout se c'è una sessione attiva\n            client.get('/auth/logout', follow_redirects=True)\n            # Poi fai login come admin\n            return client.post('/auth/login', data={\n                'username': 'adminuser',\n                'password': 'adminpassword'\n            }, follow_redirects=True)\n\n        def logout(self):\n            return client.get('/auth/logout', follow_redirects=True)\n\n    return AdminAuthActions()\n\n# Personnel-related fixtures\n\****************(scope='function')\ndef sample_departments(app, init_database):\n    \"\"\"Fixture per creare dipartimenti di test nel db.\"\"\"\n    with app.app_context():\n        # Check if departments already exist\n        dept_names = ['Engineering', 'Marketing', 'Human Resources', 'Sales']\n        departments = []\n\n        for name in dept_names:\n            dept = Department.query.filter_by(name=name).first()\n            if not dept:\n                if name == 'Engineering':\n                    dept = Department(name=name, description='Software development and engineering')\n                elif name == 'Marketing':\n                    dept = Department(name=name, description='Marketing and communications')\n                elif name == 'Human Resources':\n                    dept = Department(name=name, description='HR and people operations')\n                elif name == 'Sales':\n                    dept = Department(name=name, description='Sales and business development')\n                db.session.add(dept)\n            departments.append(dept)\n\n        db.session.commit()\n        return departments\n\****************(scope='function')\ndef sample_skills(app, init_database):\n    \"\"\"Fixture per creare competenze di test nel db.\"\"\"\n    with app.app_context():\n        # Check if skills already exist\n        skill_data = [\n            ('Python', 'Programming', 'Python programming language'),\n            ('JavaScript', 'Programming', 'JavaScript programming language'),\n            ('Project Management', 'Management', 'Project planning and execution'),\n            ('Digital Marketing', 'Marketing', 'Online marketing and social media'),\n            ('Data Analysis', 'Analytics', 'Data analysis and visualization')\n        ]\n\n        skills = []\n        for name, category, description in skill_data:\n            skill = Skill.query.filter_by(name=name).first()\n            if not skill:\n                skill = Skill(name=name, category=category, description=description)\n                db.session.add(skill)\n            skills.append(skill)\n\n        db.session.commit()\n        return skills\n\****************(scope='function')\ndef sample_users(app, init_database):\n    \"\"\"Fixture per creare utenti di test nel db con dipartimenti.\"\"\"\n    with app.app_context():\n        # Create departments first (check if they exist)\n        dept_names = ['Engineering', 'Marketing', 'Human Resources', 'Sales']\n        departments = []\n\n        for name in dept_names:\n            dept = Department.query.filter_by(name=name).first()\n            if not dept:\n                if name == 'Engineering':\n                    dept = Department(name=name, description='Software development and engineering')\n                elif name == 'Marketing':\n                    dept = Department(name=name, description='Marketing and communications')\n                elif name == 'Human Resources':\n                    dept = Department(name=name, description='HR and people operations')\n                elif name == 'Sales':\n                    dept = Department(name=name, description='Sales and business development')\n                db.session.add(dept)\n            departments.append(dept)\n\n        db.session.flush()  # Get IDs without committing\n\n        users = [\n            User(\n                username='admin',\n                email='<EMAIL>',\n                first_name='Admin',\n                last_name='User',\n                role='admin',\n                department_id=departments[0].id,  # Engineering\n                position='System Administrator',\n                hire_date=datetime.utcnow().date() - timedelta(days=365),\n                phone='+1234567890',\n                is_active=True\n            ),\n            User(\n                username='manager',\n                email='<EMAIL>',\n                first_name='Manager',\n                last_name='User',\n                role='manager',\n                department_id=departments[0].id,  # Engineering\n                position='Engineering Manager',\n                hire_date=datetime.utcnow().date() - timedelta(days=200),\n                phone='+1234567891',\n                is_active=True\n            ),\n            User(\n                username='employee',\n                email='<EMAIL>',\n                first_name='Employee',\n                last_name='User',\n                role='employee',\n                department_id=departments[1].id,  # Marketing\n                position='Marketing Specialist',\n                hire_date=datetime.utcnow().date() - timedelta(days=100),\n                phone='+1234567892',\n                is_active=True\n            ),\n            User(\n                username='hr_user',\n                email='<EMAIL>',\n                first_name='HR',\n                last_name='User',\n                role='human_resources',\n                department_id=departments[2].id,  # HR\n                position='HR Specialist',\n                hire_date=datetime.utcnow().date() - timedelta(days=150),\n                phone='+1234567893',\n                is_active=True\n            )\n        ]\n\n        for user in users:\n            user.set_password('password')\n            db.session.add(user)\n\n        db.session.commit()\n        return users\n\****************(scope='function')\ndef sample_user_skills(app, sample_users, sample_skills):\n    \"\"\"Fixture per creare competenze utente di test nel db.\"\"\"\n    with app.app_context():\n        # Get fresh instances from database\n        users = User.query.order_by(User.id).all()\n        skills = Skill.query.order_by(Skill.id).all()\n\n        if len(users) >= 3 and len(skills) >= 4:\n            user_skills = [\n                UserSkill(\n                    user_id=users[0].id,  # Admin\n                    skill_id=skills[0].id,  # Python\n                    proficiency_level=5,\n                    years_experience=5,\n                    is_certified=True\n                ),\n                UserSkill(\n                    user_id=users[0].id,  # Admin\n                    skill_id=skills[2].id,  # Project Management\n                    proficiency_level=4,\n                    years_experience=3,\n                    is_certified=False\n                ),\n                UserSkill(\n                    user_id=users[1].id,  # Manager\n                    skill_id=skills[1].id,  # JavaScript\n                    proficiency_level=4,\n                    years_experience=4,\n                    is_certified=True\n                ),\n                UserSkill(\n                    user_id=users[2].id,  # Employee\n                    skill_id=skills[3].id,  # Digital Marketing\n                    proficiency_level=3,\n                    years_experience=2,\n                    is_certified=False\n                )\n            ]\n\n            for user_skill in user_skills:\n                db.session.add(user_skill)\n\n            db.session.commit()\n            return user_skills\n\n        return []\n\****************(scope='function')\ndef sample_user_profiles(app, sample_users):\n    \"\"\"Fixture per creare profili utente di test nel db.\"\"\"\n    with app.app_context():\n        # Get fresh instances from database\n        users = User.query.order_by(User.id).all()\n\n        if len(users) >= 2:\n            profiles = [\n                UserProfile(\n                    user_id=users[0].id,  # Admin\n                    emergency_contact_name='Emergency Contact 1',\n                    emergency_contact_phone='+1234567800',\n                    address='123 Admin Street',\n                    profile_completion=95.0,\n                    notes='Admin user profile'\n                ),\n                UserProfile(\n                    user_id=users[1].id,  # Manager\n                    emergency_contact_name='Emergency Contact 2',\n                    emergency_contact_phone='+1234567801',\n                    address='456 Manager Avenue',\n                    profile_completion=80.0,\n                    notes='Manager user profile'\n                )\n            ]\n\n            for profile in profiles:\n                db.session.add(profile)\n\n            db.session.commit()\n            return profiles\n\n        return []"}