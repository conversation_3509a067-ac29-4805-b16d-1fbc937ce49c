{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "app.py"}, "originalCode": "import os\nimport logging\nfrom flask import Flask, session, redirect, url_for, request, flash\nfrom flask_login import logout_user, current_user\nfrom werkzeug.middleware.proxy_fix import ProxyFix\nfrom datetime import datetime, timedelta\nfrom config import Config\nimport time\nfrom extensions import db, login_manager, migrate, csrf\n\n# Configure logging\nlogging.basicConfig(level=logging.INFO) # Modificato INFO per produzione, DEBUG per sviluppo\nlogger = logging.getLogger(__name__)\n\nPUBLIC_ENDPOINTS = [\n    'auth.login', 'auth.register', 'auth.forgot_password', 'auth.reset_password_with_token',\n    'landing.home', 'landing.services', 'landing.about', 'landing.contact', 'landing.privacy',\n    'static', 'password_reset.forgot_password', 'password_reset.reset_token',\n    'public_api.get_public_config', 'public_api.get_featured_services',\n    'public_api.get_services', 'public_api.get_service_detail',\n    'spa'  # SPA catch-all route\n]\n\ndef create_app(config_object='config.Config', config_overrides=None):\n    \"\"\"Factory function to create and configure the Flask app.\"\"\"\n    app = Flask(__name__)\n    app.secret_key = os.environ.get(\"SESSION_SECRET\", os.urandom(24))\n    app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)\n\n    # Load configuration\n    app.config.from_object(config_object)\n    if config_overrides:\n        app.config.from_mapping(config_overrides)\n\n    # Initialize extensions with app\n    db.init_app(app)\n    login_manager.init_app(app)\n    login_manager.login_view = 'auth.login'  # Assicurati che 'auth.login' sia il nome corretto della tua route di login\n    migrate.init_app(app, db)\n\n    # Configure CSRF protection\n    csrf.init_app(app)\n    # Make csrf_token available in templates without function call\n    app.jinja_env.globals['csrf_token'] = lambda: csrf.generate_csrf()\n\n    with app.app_context():\n        # Import models first to ensure they're registered\n        from models import User # Spostato import qui per evitare importazioni circolari\n\n        # Add datetime utility for templates\n        @app.context_processor\n        def utility_processor():\n            return {'current_year': datetime.now().year}\n\n        # Import blueprints\n        from blueprints.auth import auth_bp\n        from blueprints.dashboard import dashboard_bp\n        from blueprints.personnel import personnel_bp\n        from blueprints.projects import projects_bp\n        from blueprints.crm import crm_bp\n        from blueprints.products import products_bp\n        from blueprints.performance import performance_bp\n        from blueprints.communications import communications_bp\n        from blueprints.funding import funding_bp\n        from blueprints.reporting import reporting_bp\n        from blueprints.startup import startup_bp\n        from blueprints.landing import landing_bp\n        from blueprints.admin import admin_bp\n        from blueprints.api import api_bp\n        from blueprints.api.public import public_api_bp\n        from blueprints.swagger import register_swagger_blueprints\n\n        # Register blueprints\n        app.register_blueprint(auth_bp, url_prefix='/auth')\n        app.register_blueprint(dashboard_bp, url_prefix='/dashboard')\n        app.register_blueprint(personnel_bp)\n        app.register_blueprint(projects_bp)\n        app.register_blueprint(crm_bp)\n        app.register_blueprint(products_bp)\n        app.register_blueprint(performance_bp)\n        app.register_blueprint(communications_bp)\n        app.register_blueprint(funding_bp)\n        app.register_blueprint(reporting_bp)\n        app.register_blueprint(startup_bp)\n        app.register_blueprint(landing_bp)\n        app.register_blueprint(admin_bp)\n        app.register_blueprint(api_bp, url_prefix='/api')\n        app.register_blueprint(public_api_bp)\n\n        # Register Swagger blueprints\n        register_swagger_blueprints(app)\n\n        # SPA Route - Catch-all for Vue.js routing\n        @app.route('/')\n        @app.route('/<path:path>')\n        def spa(path=''):\n            \"\"\"\n            Serve the Vue.js SPA for all routes except API and auth routes.\n            This allows Vue Router to handle client-side routing.\n            \"\"\"\n            # Don't serve SPA for API routes\n            if path.startswith('api/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for auth routes (keep traditional auth)\n            if path.startswith('auth/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for static files\n            if path.startswith('static/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for swagger routes\n            if path.startswith('swagger/') or path.startswith('docs/'):\n                from flask import abort\n                abort(404)\n\n            # Serve the SPA template for all other routes\n            from flask import render_template\n            return render_template('spa.html')\n\n        # Setup user loader for Flask-Login\n        @login_manager.user_loader\n        def load_user(user_id):\n            return User.query.get(int(user_id))\n\n        # Create database tables if they don't exist\n        # db.create_all() # Spostato in conftest.py per i test, e in un comando di init per produzione\n\n        logger.info(\"Flask app created and configured.\")\n\n        @app.before_request\n        def session_management():\n            if not current_user.is_authenticated:\n                return\n            now = time.time()\n            # Idle timeout\n            last_activity = session.get('last_activity')\n            if last_activity and now - last_activity > Config.PERMANENT_SESSION_LIFETIME.total_seconds():\n                logout_user()\n                session.clear()\n                flash('Sessione scaduta per inattività. Effettua nuovamente il login.', 'warning')\n                return redirect(url_for('auth.login'))\n            session['last_activity'] = now\n            # Absolute timeout\n            login_time = session.get('login_time')\n            if login_time and now - login_time > Config.ABSOLUTE_SESSION_LIFETIME:\n                logout_user()\n                session.clear()\n                flash('Sessione scaduta. Effettua nuovamente il login.', 'warning')\n                return redirect(url_for('auth.login'))\n\n        @app.before_request\n        def global_auth_enforcement():\n            endpoint = request.endpoint\n            # Log ogni accesso\n            user = getattr(current_user, 'username', 'anonymous')\n            logger.info(f\"Accesso: user={user}, endpoint={endpoint}, ip={request.remote_addr}\")\n            # Enforcement autenticazione globale\n            if endpoint and not endpoint.startswith('static') and endpoint not in PUBLIC_ENDPOINTS:\n                if not current_user.is_authenticated:\n                    logger.warning(f\"Tentativo accesso non autenticato a {endpoint} da IP {request.remote_addr}\")\n\n                    # Per le API, restituisci JSON 401 invece di redirect\n                    if endpoint and endpoint.startswith('api.'):\n                        from flask import jsonify\n                        return jsonify({\n                            'success': False,\n                            'message': 'Autenticazione richiesta'\n                        }), 401\n\n                    # Per le pagine web, fai redirect\n                    flash('Devi essere autenticato per accedere a questa pagina.', 'warning')\n                    return redirect(url_for('auth.login', next=request.url))\n\n        @app.errorhandler(403)\n        def forbidden(e):\n            user = getattr(current_user, 'username', 'anonymous')\n            logger.warning(f\"403 Forbidden: user={user}, endpoint={request.endpoint}, ip={request.remote_addr}\")\n            flash('Accesso negato: non hai i permessi necessari.', 'danger')\n            return redirect(url_for('dashboard.index'))\n\n        # Registra i filtri personalizzati\n        from utils.filters import register_filters\n        register_filters(app)\n\n    return app\n\n# Rimosso:\n# app = Flask(__name__)\n# app.secret_key = os.environ.get(\"SESSION_SECRET\", os.urandom(24))\n# app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)\n# app.config.from_object('config.Config')\n# db.init_app(app)\n# login_manager.init_app(app)\n# login_manager.login_view = 'auth.login'\n# migrate.init_app(app, db)\n# ... e tutta la logica di registrazione blueprint e user_loader che è ora in create_app()\n", "modifiedCode": "import os\nimport logging\nfrom flask import Flask, session, redirect, url_for, request, flash\nfrom flask_login import logout_user, current_user\nfrom werkzeug.middleware.proxy_fix import ProxyFix\nfrom datetime import datetime, timedelta\nfrom config import Config\nimport time\nfrom extensions import db, login_manager, migrate, csrf\n\n# Configure logging\nlogging.basicConfig(level=logging.INFO) # Modificato INFO per produzione, DEBUG per sviluppo\nlogger = logging.getLogger(__name__)\n\nPUBLIC_ENDPOINTS = [\n    'auth.login', 'auth.register', 'auth.forgot_password', 'auth.reset_password_with_token',\n    'landing.spa_routes', 'static', 'password_reset.forgot_password', 'password_reset.reset_token',\n    'public_api.get_public_config', 'public_api.get_featured_services',\n    'public_api.get_services', 'public_api.get_service_detail',\n    'spa'  # SPA catch-all route\n]\n\ndef create_app(config_object='config.Config', config_overrides=None):\n    \"\"\"Factory function to create and configure the Flask app.\"\"\"\n    app = Flask(__name__)\n    app.secret_key = os.environ.get(\"SESSION_SECRET\", os.urandom(24))\n    app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)\n\n    # Load configuration\n    app.config.from_object(config_object)\n    if config_overrides:\n        app.config.from_mapping(config_overrides)\n\n    # Initialize extensions with app\n    db.init_app(app)\n    login_manager.init_app(app)\n    login_manager.login_view = 'auth.login'  # Assicurati che 'auth.login' sia il nome corretto della tua route di login\n    migrate.init_app(app, db)\n\n    # Configure CSRF protection\n    csrf.init_app(app)\n    # Make csrf_token available in templates without function call\n    app.jinja_env.globals['csrf_token'] = lambda: csrf.generate_csrf()\n\n    with app.app_context():\n        # Import models first to ensure they're registered\n        from models import User # Spostato import qui per evitare importazioni circolari\n\n        # Add datetime utility for templates\n        @app.context_processor\n        def utility_processor():\n            return {'current_year': datetime.now().year}\n\n        # Import blueprints\n        from blueprints.auth import auth_bp\n        from blueprints.dashboard import dashboard_bp\n        from blueprints.personnel import personnel_bp\n        from blueprints.projects import projects_bp\n        from blueprints.crm import crm_bp\n        from blueprints.products import products_bp\n        from blueprints.performance import performance_bp\n        from blueprints.communications import communications_bp\n        from blueprints.funding import funding_bp\n        from blueprints.reporting import reporting_bp\n        from blueprints.startup import startup_bp\n        from blueprints.landing import landing_bp\n        from blueprints.admin import admin_bp\n        from blueprints.api import api_bp\n        from blueprints.api.public import public_api_bp\n        from blueprints.swagger import register_swagger_blueprints\n\n        # Register blueprints\n        app.register_blueprint(auth_bp, url_prefix='/auth')\n        app.register_blueprint(dashboard_bp, url_prefix='/dashboard')\n        app.register_blueprint(personnel_bp)\n        app.register_blueprint(projects_bp)\n        app.register_blueprint(crm_bp)\n        app.register_blueprint(products_bp)\n        app.register_blueprint(performance_bp)\n        app.register_blueprint(communications_bp)\n        app.register_blueprint(funding_bp)\n        app.register_blueprint(reporting_bp)\n        app.register_blueprint(startup_bp)\n        app.register_blueprint(landing_bp)\n        app.register_blueprint(admin_bp)\n        app.register_blueprint(api_bp, url_prefix='/api')\n        app.register_blueprint(public_api_bp)\n\n        # Register Swagger blueprints\n        register_swagger_blueprints(app)\n\n        # SPA Route - Catch-all for Vue.js routing\n        @app.route('/')\n        @app.route('/<path:path>')\n        def spa(path=''):\n            \"\"\"\n            Serve the Vue.js SPA for all routes except API and auth routes.\n            This allows Vue Router to handle client-side routing.\n            \"\"\"\n            # Don't serve SPA for API routes\n            if path.startswith('api/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for auth routes (keep traditional auth)\n            if path.startswith('auth/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for static files\n            if path.startswith('static/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for swagger routes\n            if path.startswith('swagger/') or path.startswith('docs/'):\n                from flask import abort\n                abort(404)\n\n            # Serve the SPA template for all other routes\n            from flask import render_template\n            return render_template('spa.html')\n\n        # Setup user loader for Flask-Login\n        @login_manager.user_loader\n        def load_user(user_id):\n            return User.query.get(int(user_id))\n\n        # Create database tables if they don't exist\n        # db.create_all() # Spostato in conftest.py per i test, e in un comando di init per produzione\n\n        logger.info(\"Flask app created and configured.\")\n\n        @app.before_request\n        def session_management():\n            if not current_user.is_authenticated:\n                return\n            now = time.time()\n            # Idle timeout\n            last_activity = session.get('last_activity')\n            if last_activity and now - last_activity > Config.PERMANENT_SESSION_LIFETIME.total_seconds():\n                logout_user()\n                session.clear()\n                flash('Sessione scaduta per inattività. Effettua nuovamente il login.', 'warning')\n                return redirect(url_for('auth.login'))\n            session['last_activity'] = now\n            # Absolute timeout\n            login_time = session.get('login_time')\n            if login_time and now - login_time > Config.ABSOLUTE_SESSION_LIFETIME:\n                logout_user()\n                session.clear()\n                flash('Sessione scaduta. Effettua nuovamente il login.', 'warning')\n                return redirect(url_for('auth.login'))\n\n        @app.before_request\n        def global_auth_enforcement():\n            endpoint = request.endpoint\n            # Log ogni accesso\n            user = getattr(current_user, 'username', 'anonymous')\n            logger.info(f\"Accesso: user={user}, endpoint={endpoint}, ip={request.remote_addr}\")\n            # Enforcement autenticazione globale\n            if endpoint and not endpoint.startswith('static') and endpoint not in PUBLIC_ENDPOINTS:\n                if not current_user.is_authenticated:\n                    logger.warning(f\"Tentativo accesso non autenticato a {endpoint} da IP {request.remote_addr}\")\n\n                    # Per le API, restituisci JSON 401 invece di redirect\n                    if endpoint and endpoint.startswith('api.'):\n                        from flask import jsonify\n                        return jsonify({\n                            'success': False,\n                            'message': 'Autenticazione richiesta'\n                        }), 401\n\n                    # Per le pagine web, fai redirect\n                    flash('Devi essere autenticato per accedere a questa pagina.', 'warning')\n                    return redirect(url_for('auth.login', next=request.url))\n\n        @app.errorhandler(403)\n        def forbidden(e):\n            user = getattr(current_user, 'username', 'anonymous')\n            logger.warning(f\"403 Forbidden: user={user}, endpoint={request.endpoint}, ip={request.remote_addr}\")\n            flash('Accesso negato: non hai i permessi necessari.', 'danger')\n            return redirect(url_for('dashboard.index'))\n\n        # Registra i filtri personalizzati\n        from utils.filters import register_filters\n        register_filters(app)\n\n    return app\n\n# Rimosso:\n# app = Flask(__name__)\n# app.secret_key = os.environ.get(\"SESSION_SECRET\", os.urandom(24))\n# app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)\n# app.config.from_object('config.Config')\n# db.init_app(app)\n# login_manager.init_app(app)\n# login_manager.login_view = 'auth.login'\n# migrate.init_app(app, db)\n# ... e tutta la logica di registrazione blueprint e user_loader che è ora in create_app()\n"}