{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/landing/contact.html"}, "originalCode": "{% extends 'base.html' %}\n\n{% block title %}Contatti - DatPortal | DatVinci{% endblock %}\n\n{% block public_content %}\n<section class=\"bg-gradient-to-r from-primary-600 to-secondary-500 text-white py-12\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center\">\n            <h1 class=\"text-3xl font-bold mb-4\">Contattaci</h1>\n            <p class=\"text-xl max-w-3xl mx-auto\">\n                Hai domande sul DatPortal o vuoi richiedere una demo? Compila il modulo sottostante e ti risponderemo al più presto.\n            </p>\n        </div>\n    </div>\n</section>\n\n<section class=\"py-12 bg-white dark:bg-gray-800\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n            <!-- <PERSON><PERSON><PERSON> di contatto -->\n            <div>\n                <h2 class=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">Inviaci un messaggio</h2>\n                \n                <form action=\"{{ url_for('landing.contact') }}\" method=\"POST\" class=\"space-y-6\">\n                    <div>\n                        <label for=\"name\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                            Nome completo\n                        </label>\n                        <div class=\"mt-1\">\n                            <input type=\"text\" name=\"name\" id=\"name\" required\n                                class=\"shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\">\n                        </div>\n                    </div>\n                    \n                    <div>\n                        <label for=\"email\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                            Email\n                        </label>\n                        <div class=\"mt-1\">\n                            <input type=\"email\" name=\"email\" id=\"email\" required\n                                class=\"shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\">\n                        </div>\n                    </div>\n                    \n                    <div>\n                        <label for=\"subject\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                            Oggetto\n                        </label>\n                        <div class=\"mt-1\">\n                            <select id=\"subject\" name=\"subject\"\n                                class=\"shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\">\n                                <option value=\"info\">Richiesta informazioni</option>\n                                <option value=\"demo\">Richiesta demo</option>\n                                <option value=\"support\">Supporto tecnico</option>\n                                <option value=\"other\">Altro</option>\n                            </select>\n                        </div>\n                    </div>\n                    \n                    <div>\n                        <label for=\"message\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                            Messaggio\n                        </label>\n                        <div class=\"mt-1\">\n                            <textarea id=\"message\" name=\"message\" rows=\"4\" required\n                                class=\"shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"></textarea>\n                        </div>\n                    </div>\n                    \n                    <div class=\"flex items-start\">\n                        <div class=\"flex items-center h-5\">\n                            <input id=\"privacy\" name=\"privacy\" type=\"checkbox\" required\n                                class=\"focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 dark:border-gray-600 rounded\">\n                        </div>\n                        <div class=\"ml-3 text-sm\">\n                            <label for=\"privacy\" class=\"font-medium text-gray-700 dark:text-gray-300\">\n                                Acconsento al trattamento dei miei dati personali\n                            </label>\n                            <p class=\"text-gray-500 dark:text-gray-400\">\n                                Letta l'<a href=\"{{ url_for('landing.privacy') }}\" class=\"text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300\">informativa privacy</a>\n                            </p>\n                        </div>\n                    </div>\n                    \n                    <div>\n                        <button type=\"submit\"\n                            class=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800\">\n                            Invia messaggio\n                        </button>\n                    </div>\n                </form>\n            </div>\n            \n            <!-- Informazioni di contatto -->\n            <div>\n                <h2 class=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">Informazioni di contatto</h2>\n                \n                <div class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 shadow-md\">\n                    <div class=\"space-y-6\">\n                        <div class=\"flex items-start\">\n                            <div class=\"flex-shrink-0\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                </svg>\n                            </div>\n                            <div class=\"ml-3\">\n                                <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Indirizzo</h3>\n                                <p class=\"mt-1 text-gray-600 dark:text-gray-300\">\n                                    {{ company_info.address }}\n                                </p>\n                            </div>\n                        </div>\n                        \n                        <div class=\"flex items-start\">\n                            <div class=\"flex-shrink-0\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                                </svg>\n                            </div>\n                            <div class=\"ml-3\">\n                                <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Email</h3>\n                                <p class=\"mt-1 text-gray-600 dark:text-gray-300\">\n                                    <a href=\"mailto:{{ company_info.email }}\" class=\"text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300\">\n                                        {{ company_info.email }}\n                                    </a>\n                                </p>\n                            </div>\n                        </div>\n                        \n                        <div class=\"flex items-start\">\n                            <div class=\"flex-shrink-0\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                                </svg>\n                            </div>\n                            <div class=\"ml-3\">\n                                <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Telefono</h3>\n                                <p class=\"mt-1 text-gray-600 dark:text-gray-300\">\n                                    <a href=\"tel:{{ company_info.phone }}\" class=\"text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300\">\n                                        {{ company_info.phone }}\n                                    </a>\n                                </p>\n                            </div>\n                        </div>\n                        \n                        <div class=\"flex items-start\">\n                            <div class=\"flex-shrink-0\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                                </svg>\n                            </div>\n                            <div class=\"ml-3\">\n                                <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Orari</h3>\n                                <p class=\"mt-1 text-gray-600 dark:text-gray-300\">\n                                    {{ company_info.hours }}\n                                </p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                \n                <!-- Mappa o immagine dell'ufficio -->\n                <div class=\"mt-8\">\n                    <div class=\"bg-gray-200 dark:bg-gray-600 rounded-lg overflow-hidden h-64 shadow-md\">\n                        <div class=\"w-full h-full flex items-center justify-center text-gray-500 dark:text-gray-400\">\n                            <div class=\"text-center\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-16 w-16 mx-auto\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                </svg>\n                                <p class=\"mt-2 text-sm\">Mappa interattiva</p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n</section>\n\n<!-- FAQ Section -->\n<section class=\"py-12 bg-gray-50 dark:bg-gray-700\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center mb-12\">\n            <h2 class=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">Domande frequenti</h2>\n            <p class=\"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\">\n                Trova rapidamente risposte alle domande più comuni su DatPortal.\n            </p>\n        </div>\n        \n        <div class=\"max-w-3xl mx-auto\">\n            <div class=\"space-y-4\">\n                <!-- FAQ 1 -->\n                <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\" x-data=\"{ expanded: false }\">\n                    <button @click=\"expanded = !expanded\" class=\"flex justify-between items-center w-full px-6 py-4 text-left\">\n                        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                            Cos'è DatPortal e a chi è rivolto?\n                        </h3>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" :class=\"{'rotate-180': expanded}\" class=\"h-5 w-5 text-gray-500 dark:text-gray-400 transform transition-transform duration-200\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </button>\n                    <div x-show=\"expanded\" x-collapse>\n                        <div class=\"px-6 pb-4 text-gray-600 dark:text-gray-300\">\n                            <p>DatPortal è una piattaforma intranet aziendale completa progettata specificamente per PMI, startup e aziende innovative. Integra gestione progetti, CRM, documentale e supporto per bandi e finanziamenti in un'unica soluzione, potenziata dall'intelligenza artificiale.</p>\n                        </div>\n                    </div>\n                </div>\n                \n                <!-- FAQ 2 -->\n                <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\" x-data=\"{ expanded: false }\">\n                    <button @click=\"expanded = !expanded\" class=\"flex justify-between items-center w-full px-6 py-4 text-left\">\n                        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                            Come posso ottenere una demo di DatPortal?\n                        </h3>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" :class=\"{'rotate-180': expanded}\" class=\"h-5 w-5 text-gray-500 dark:text-gray-400 transform transition-transform duration-200\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </button>\n                    <div x-show=\"expanded\" x-collapse>\n                        <div class=\"px-6 pb-4 text-gray-600 dark:text-gray-300\">\n                            <p>È possibile richiedere una demo compilando il modulo di contatto su questa pagina, selezionando \"Richiesta demo\" come oggetto. Un nostro consulente ti contatterà per organizzare una presentazione personalizzata della piattaforma.</p>\n                        </div>\n                    </div>\n                </div>\n                \n                <!-- FAQ 3 -->\n                <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\" x-data=\"{ expanded: false }\">\n                    <button @click=\"expanded = !expanded\" class=\"flex justify-between items-center w-full px-6 py-4 text-left\">\n                        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                            DatPortal può essere personalizzato per le esigenze specifiche della mia azienda?\n                        </h3>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" :class=\"{'rotate-180': expanded}\" class=\"h-5 w-5 text-gray-500 dark:text-gray-400 transform transition-transform duration-200\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </button>\n                    <div x-show=\"expanded\" x-collapse>\n                        <div class=\"px-6 pb-4 text-gray-600 dark:text-gray-300\">\n                            <p>Assolutamente sì! DatPortal è una piattaforma altamente personalizzabile. Possiamo adattare funzionalità, flussi di lavoro e interfaccia in base alle specifiche esigenze della tua azienda. Contattaci per discutere le tue necessità e ricevere una proposta su misura.</p>\n                        </div>\n                    </div>\n                </div>\n                \n                <!-- FAQ 4 -->\n                <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\" x-data=\"{ expanded: false }\">\n                    <button @click=\"expanded = !expanded\" class=\"flex justify-between items-center w-full px-6 py-4 text-left\">\n                        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                            Che supporto offrite dopo l'implementazione?\n                        </h3>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" :class=\"{'rotate-180': expanded}\" class=\"h-5 w-5 text-gray-500 dark:text-gray-400 transform transition-transform duration-200\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </button>\n                    <div x-show=\"expanded\" x-collapse>\n                        <div class=\"px-6 pb-4 text-gray-600 dark:text-gray-300\">\n                            <p>Offriamo un supporto completo che include formazione per gli utenti, assistenza tecnica continua, manutenzione periodica e aggiornamenti regolari. Disponiamo di diversi piani di supporto tra cui scegliere in base alle tue esigenze, inclusa l'opzione di assistenza premium con tempi di risposta garantiti.</p>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n</section>\n{% endblock %}", "modifiedCode": "{% extends 'base.html' %}\n\n{% block title %}Contatti - DatPortal | DatVinci{% endblock %}\n\n{% block public_content %}\n<section class=\"bg-gradient-to-r from-primary-600 to-secondary-500 text-white py-12\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center\">\n            <h1 class=\"text-3xl font-bold mb-4\">Contattaci</h1>\n            <p class=\"text-xl max-w-3xl mx-auto\">\n                Hai domande sul DatPortal o vuoi richiedere una demo? Compila il modulo sottostante e ti risponderemo al più presto.\n            </p>\n        </div>\n    </div>\n</section>\n\n<section class=\"py-12 bg-white dark:bg-gray-800\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n            <!-- <PERSON><PERSON><PERSON> di contatto -->\n            <div>\n                <h2 class=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">Inviaci un messaggio</h2>\n                \n                <form action=\"{{ url_for('landing.contact') }}\" method=\"POST\" class=\"space-y-6\">\n                    <div>\n                        <label for=\"name\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                            Nome completo\n                        </label>\n                        <div class=\"mt-1\">\n                            <input type=\"text\" name=\"name\" id=\"name\" required\n                                class=\"shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\">\n                        </div>\n                    </div>\n                    \n                    <div>\n                        <label for=\"email\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                            Email\n                        </label>\n                        <div class=\"mt-1\">\n                            <input type=\"email\" name=\"email\" id=\"email\" required\n                                class=\"shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\">\n                        </div>\n                    </div>\n                    \n                    <div>\n                        <label for=\"subject\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                            Oggetto\n                        </label>\n                        <div class=\"mt-1\">\n                            <select id=\"subject\" name=\"subject\"\n                                class=\"shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\">\n                                <option value=\"info\">Richiesta informazioni</option>\n                                <option value=\"demo\">Richiesta demo</option>\n                                <option value=\"support\">Supporto tecnico</option>\n                                <option value=\"other\">Altro</option>\n                            </select>\n                        </div>\n                    </div>\n                    \n                    <div>\n                        <label for=\"message\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                            Messaggio\n                        </label>\n                        <div class=\"mt-1\">\n                            <textarea id=\"message\" name=\"message\" rows=\"4\" required\n                                class=\"shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"></textarea>\n                        </div>\n                    </div>\n                    \n                    <div class=\"flex items-start\">\n                        <div class=\"flex items-center h-5\">\n                            <input id=\"privacy\" name=\"privacy\" type=\"checkbox\" required\n                                class=\"focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 dark:border-gray-600 rounded\">\n                        </div>\n                        <div class=\"ml-3 text-sm\">\n                            <label for=\"privacy\" class=\"font-medium text-gray-700 dark:text-gray-300\">\n                                Acconsento al trattamento dei miei dati personali\n                            </label>\n                            <p class=\"text-gray-500 dark:text-gray-400\">\n                                Letta l'<a href=\"{{ url_for('landing.privacy') }}\" class=\"text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300\">informativa privacy</a>\n                            </p>\n                        </div>\n                    </div>\n                    \n                    <div>\n                        <button type=\"submit\"\n                            class=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800\">\n                            Invia messaggio\n                        </button>\n                    </div>\n                </form>\n            </div>\n            \n            <!-- Informazioni di contatto -->\n            <div>\n                <h2 class=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">Informazioni di contatto</h2>\n                \n                <div class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 shadow-md\">\n                    <div class=\"space-y-6\">\n                        <div class=\"flex items-start\">\n                            <div class=\"flex-shrink-0\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                </svg>\n                            </div>\n                            <div class=\"ml-3\">\n                                <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Indirizzo</h3>\n                                <p class=\"mt-1 text-gray-600 dark:text-gray-300\">\n                                    {{ company_info.address }}\n                                </p>\n                            </div>\n                        </div>\n                        \n                        <div class=\"flex items-start\">\n                            <div class=\"flex-shrink-0\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                                </svg>\n                            </div>\n                            <div class=\"ml-3\">\n                                <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Email</h3>\n                                <p class=\"mt-1 text-gray-600 dark:text-gray-300\">\n                                    <a href=\"mailto:{{ company_info.email }}\" class=\"text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300\">\n                                        {{ company_info.email }}\n                                    </a>\n                                </p>\n                            </div>\n                        </div>\n                        \n                        <div class=\"flex items-start\">\n                            <div class=\"flex-shrink-0\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                                </svg>\n                            </div>\n                            <div class=\"ml-3\">\n                                <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Telefono</h3>\n                                <p class=\"mt-1 text-gray-600 dark:text-gray-300\">\n                                    <a href=\"tel:{{ company_info.phone }}\" class=\"text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300\">\n                                        {{ company_info.phone }}\n                                    </a>\n                                </p>\n                            </div>\n                        </div>\n                        \n                        <div class=\"flex items-start\">\n                            <div class=\"flex-shrink-0\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                                </svg>\n                            </div>\n                            <div class=\"ml-3\">\n                                <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Orari</h3>\n                                <p class=\"mt-1 text-gray-600 dark:text-gray-300\">\n                                    {{ company_info.hours }}\n                                </p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                \n                <!-- Mappa o immagine dell'ufficio -->\n                <div class=\"mt-8\">\n                    <div class=\"bg-gray-200 dark:bg-gray-600 rounded-lg overflow-hidden h-64 shadow-md\">\n                        <div class=\"w-full h-full flex items-center justify-center text-gray-500 dark:text-gray-400\">\n                            <div class=\"text-center\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-16 w-16 mx-auto\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                </svg>\n                                <p class=\"mt-2 text-sm\">Mappa interattiva</p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n</section>\n\n<!-- FAQ Section -->\n<section class=\"py-12 bg-gray-50 dark:bg-gray-700\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center mb-12\">\n            <h2 class=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">Domande frequenti</h2>\n            <p class=\"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\">\n                Trova rapidamente risposte alle domande più comuni su DatPortal.\n            </p>\n        </div>\n        \n        <div class=\"max-w-3xl mx-auto\">\n            <div class=\"space-y-4\">\n                <!-- FAQ 1 -->\n                <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\" x-data=\"{ expanded: false }\">\n                    <button @click=\"expanded = !expanded\" class=\"flex justify-between items-center w-full px-6 py-4 text-left\">\n                        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                            Cos'è DatPortal e a chi è rivolto?\n                        </h3>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" :class=\"{'rotate-180': expanded}\" class=\"h-5 w-5 text-gray-500 dark:text-gray-400 transform transition-transform duration-200\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </button>\n                    <div x-show=\"expanded\" x-collapse>\n                        <div class=\"px-6 pb-4 text-gray-600 dark:text-gray-300\">\n                            <p>DatPortal è una piattaforma intranet aziendale completa progettata specificamente per PMI, startup e aziende innovative. Integra gestione progetti, CRM, documentale e supporto per bandi e finanziamenti in un'unica soluzione, potenziata dall'intelligenza artificiale.</p>\n                        </div>\n                    </div>\n                </div>\n                \n                <!-- FAQ 2 -->\n                <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\" x-data=\"{ expanded: false }\">\n                    <button @click=\"expanded = !expanded\" class=\"flex justify-between items-center w-full px-6 py-4 text-left\">\n                        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                            Come posso ottenere una demo di DatPortal?\n                        </h3>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" :class=\"{'rotate-180': expanded}\" class=\"h-5 w-5 text-gray-500 dark:text-gray-400 transform transition-transform duration-200\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </button>\n                    <div x-show=\"expanded\" x-collapse>\n                        <div class=\"px-6 pb-4 text-gray-600 dark:text-gray-300\">\n                            <p>È possibile richiedere una demo compilando il modulo di contatto su questa pagina, selezionando \"Richiesta demo\" come oggetto. Un nostro consulente ti contatterà per organizzare una presentazione personalizzata della piattaforma.</p>\n                        </div>\n                    </div>\n                </div>\n                \n                <!-- FAQ 3 -->\n                <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\" x-data=\"{ expanded: false }\">\n                    <button @click=\"expanded = !expanded\" class=\"flex justify-between items-center w-full px-6 py-4 text-left\">\n                        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                            DatPortal può essere personalizzato per le esigenze specifiche della mia azienda?\n                        </h3>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" :class=\"{'rotate-180': expanded}\" class=\"h-5 w-5 text-gray-500 dark:text-gray-400 transform transition-transform duration-200\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </button>\n                    <div x-show=\"expanded\" x-collapse>\n                        <div class=\"px-6 pb-4 text-gray-600 dark:text-gray-300\">\n                            <p>Assolutamente sì! DatPortal è una piattaforma altamente personalizzabile. Possiamo adattare funzionalità, flussi di lavoro e interfaccia in base alle specifiche esigenze della tua azienda. Contattaci per discutere le tue necessità e ricevere una proposta su misura.</p>\n                        </div>\n                    </div>\n                </div>\n                \n                <!-- FAQ 4 -->\n                <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\" x-data=\"{ expanded: false }\">\n                    <button @click=\"expanded = !expanded\" class=\"flex justify-between items-center w-full px-6 py-4 text-left\">\n                        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                            Che supporto offrite dopo l'implementazione?\n                        </h3>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" :class=\"{'rotate-180': expanded}\" class=\"h-5 w-5 text-gray-500 dark:text-gray-400 transform transition-transform duration-200\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </button>\n                    <div x-show=\"expanded\" x-collapse>\n                        <div class=\"px-6 pb-4 text-gray-600 dark:text-gray-300\">\n                            <p>Offriamo un supporto completo che include formazione per gli utenti, assistenza tecnica continua, manutenzione periodica e aggiornamenti regolari. Disponiamo di diversi piani di supporto tra cui scegliere in base alle tue esigenze, inclusa l'opzione di assistenza premium con tempi di risposta garantiti.</p>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n</section>\n{% endblock %}"}