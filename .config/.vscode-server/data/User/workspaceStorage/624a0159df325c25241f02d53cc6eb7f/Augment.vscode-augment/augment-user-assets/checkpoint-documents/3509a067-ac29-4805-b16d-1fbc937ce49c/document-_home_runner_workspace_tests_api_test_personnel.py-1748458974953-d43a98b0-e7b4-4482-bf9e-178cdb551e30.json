{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/api/test_personnel.py"}, "originalCode": "\"\"\"\nTest suite for Personnel API endpoints.\nTests for users, departments, and skills management.\n\"\"\"\n\nimport pytest\nimport json\nfrom datetime import datetime, date\nfrom flask import url_for\n\nfrom models import User, Department, Skill, UserSkill, UserProfile\nfrom extensions import db\n\n\nclass TestPersonnelUsersAPI:\n    \"\"\"Test cases for /api/personnel/users endpoints.\"\"\"\n\n    def test_get_users_success(self, client, auth, sample_users):\n        \"\"\"Test successful retrieval of users list.\"\"\"\n        # Login as admin to have proper permissions\n        auth.login(username='admin', password='password')\n\n        response = client.get('/api/personnel/users')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'users' in data['data']\n        assert 'pagination' in data['data']\n        assert len(data['data']['users']) > 0\n\n        # Check user structure\n        user = data['data']['users'][0]\n        required_fields = [\n            'id', 'username', 'email', 'first_name', 'last_name',\n            'full_name', 'role', 'is_active'\n        ]\n        for field in required_fields:\n            assert field in user\n\n    def test_get_users_with_pagination(self, client, auth, sample_users):\n        \"\"\"Test users list with pagination parameters.\"\"\"\n        auth.login(username='admin', password='password')\n\n        response = client.get('/api/personnel/users?page=1&per_page=2')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert len(data['data']['users']) <= 2\n        assert data['data']['pagination']['per_page'] == 2\n        assert data['data']['pagination']['page'] == 1\n\n    def test_get_users_with_search(self, client, auth, sample_users):\n        \"\"\"Test users list with search parameter.\"\"\"\n        auth.login(username='admin', password='password')\n\n        # Search for a specific user\n        response = client.get('/api/personnel/users?search=admin')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # Check that search results contain the search term\n        for user in data['data']['users']:\n            user_text = f\"{user['username']} {user['first_name']} {user['last_name']} {user['email']}\".lower()\n            assert 'admin' in user_text\n\n    def test_get_users_with_department_filter(self, client, auth, sample_users, sample_departments):\n        \"\"\"Test users list filtered by department.\"\"\"\n        auth.login(username='admin', password='password')\n\n        # Get first department ID from database to avoid DetachedInstanceError\n        from models import Department\n        dept = Department.query.first()\n        if not dept:\n            pytest.skip(\"No departments found in database\")\n\n        response = client.get(f'/api/personnel/users?department_id={dept.id}')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # All users should belong to the specified department\n        for user in data['data']['users']:\n            if user['department_id'] is not None:\n                assert user['department_id'] == dept.id\n\n    def test_get_users_with_role_filter(self, client, auth, sample_users):\n        \"\"\"Test users list filtered by role.\"\"\"\n        auth.login(username='admin', password='password')\n\n        response = client.get('/api/personnel/users?role=admin')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # All users should have admin role\n        for user in data['data']['users']:\n            assert user['role'] == 'admin'\n\n    def test_get_users_with_active_filter(self, client, auth, sample_users):\n        \"\"\"Test users list filtered by active status.\"\"\"\n        auth.login(username='admin', password='password')\n\n        response = client.get('/api/personnel/users?is_active=true')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # All users should be active\n        for user in data['data']['users']:\n            assert user['is_active'] is True\n\n    def test_get_users_with_skills_filter(self, client, auth, sample_users, sample_skills):\n        \"\"\"Test users list filtered by skills.\"\"\"\n        auth.login(username='admin', password='password')\n\n        # Get first skill ID from database to avoid DetachedInstanceError\n        from models import Skill\n        skill = Skill.query.first()\n        if not skill:\n            pytest.skip(\"No skills found in database\")\n\n        response = client.get(f'/api/personnel/users?skills={skill.id}')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # Users should have the specified skill\n        for user in data['data']['users']:\n            skill_ids = [s['id'] for s in user['skills']]\n            if skill_ids:  # Only check if user has skills\n                assert skill.id in skill_ids\n\n    def test_get_users_with_ordering(self, client, auth, sample_users):\n        \"\"\"Test users list with custom ordering.\"\"\"\n        auth.login()\n\n        # Test ascending order by first name\n        response = client.get('/api/personnel/users?order_by=first_name&order_dir=asc')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # Check that results are ordered\n        if len(data['data']['users']) > 1:\n            first_names = [user['first_name'] for user in data['data']['users']]\n            assert first_names == sorted(first_names)\n\n    def test_get_users_unauthorized(self, client, auth):\n        \"\"\"Test users list access without authentication.\"\"\"\n        # Ensure user is logged out\n        auth.logout()\n\n        response = client.get('/api/personnel/users')\n        assert response.status_code == 401\n\n    def test_get_users_forbidden(self, client, auth):\n        \"\"\"Test users list access without proper permissions.\"\"\"\n        # Login as user without VIEW_PERSONNEL_DATA permission\n        auth.login(username='employee', password='password')\n\n        response = client.get('/api/personnel/users')\n        assert response.status_code == 403\n\n    def test_get_user_by_id_success(self, client, auth, sample_users):\n        \"\"\"Test successful retrieval of specific user.\"\"\"\n        auth.login()\n\n        # Get first user ID from database to avoid DetachedInstanceError\n        from models import User\n        user = User.query.first()\n        if not user:\n            pytest.skip(\"No users found in database\")\n\n        response = client.get(f'/api/personnel/users/{user.id}')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'user' in data['data']\n\n        user_data = data['data']['user']\n        assert user_data['id'] == user.id\n        assert user_data['username'] == user.username\n        assert user_data['email'] == user.email\n\n        # Check detailed fields\n        detailed_fields = [\n            'bio', 'dark_mode', 'created_at', 'skills', 'projects', 'profile'\n        ]\n        for field in detailed_fields:\n            assert field in user_data\n\n    def test_get_user_by_id_not_found(self, client, auth):\n        \"\"\"Test retrieval of non-existent user.\"\"\"\n        auth.login()\n\n        response = client.get('/api/personnel/users/99999')\n        assert response.status_code == 404\n\n    def test_get_user_by_id_unauthorized(self, client, auth, sample_users):\n        \"\"\"Test user retrieval without authentication.\"\"\"\n        # Ensure user is logged out\n        auth.logout()\n\n        # Get first user ID from database to avoid DetachedInstanceError\n        from models import User\n        user = User.query.first()\n        if not user:\n            pytest.skip(\"No users found in database\")\n\n        response = client.get(f'/api/personnel/users/{user.id}')\n        assert response.status_code == 401\n\n\nclass TestPersonnelDepartmentsAPI:\n    \"\"\"Test cases for /api/personnel/departments endpoints.\"\"\"\n\n    def test_get_departments_success(self, client, auth, sample_departments):\n        \"\"\"Test successful retrieval of departments list.\"\"\"\n        auth.login()\n\n        response = client.get('/api/personnel/departments')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'departments' in data['data']\n        assert len(data['data']['departments']) > 0\n\n        # Check department structure\n        dept = data['data']['departments'][0]\n        required_fields = [\n            'id', 'name', 'description', 'manager_id', 'user_count', 'users'\n        ]\n        for field in required_fields:\n            assert field in dept\n\n    def test_get_departments_with_manager_info(self, client, auth, sample_departments, sample_users):\n        \"\"\"Test departments list includes manager information.\"\"\"\n        auth.login()\n\n        # Set a manager for a department\n        dept = sample_departments[0]\n        manager = sample_users[0]\n        dept.manager_id = manager.id\n        db.session.commit()\n\n        response = client.get('/api/personnel/departments')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        dept_data = next(d for d in data['data']['departments'] if d['id'] == dept.id)\n\n        assert dept_data['manager_id'] == manager.id\n        assert dept_data['manager'] is not None\n        assert dept_data['manager']['id'] == manager.id\n        assert dept_data['manager']['full_name'] == manager.full_name\n\n    def test_get_departments_with_users(self, client, auth, sample_departments, sample_users):\n        \"\"\"Test departments list includes user information.\"\"\"\n        auth.login()\n\n        # Assign users to department\n        dept = sample_departments[0]\n        user = sample_users[0]\n        user.department_id = dept.id\n        db.session.commit()\n\n        response = client.get('/api/personnel/departments')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        dept_data = next(d for d in data['data']['departments'] if d['id'] == dept.id)\n\n        assert dept_data['user_count'] >= 1\n        assert len(dept_data['users']) >= 1\n\n        user_data = next(u for u in dept_data['users'] if u['id'] == user.id)\n        assert user_data['full_name'] == user.full_name\n        assert user_data['position'] == user.position\n\n    def test_get_departments_unauthorized(self, client, auth):\n        \"\"\"Test departments list access without authentication.\"\"\"\n        # Ensure user is logged out\n        auth.logout()\n\n        response = client.get('/api/personnel/departments')\n        assert response.status_code == 401\n\n\nclass TestPersonnelSkillsAPI:\n    \"\"\"Test cases for /api/personnel/skills endpoints.\"\"\"\n\n    def test_get_skills_success(self, client, auth, sample_skills):\n        \"\"\"Test successful retrieval of skills list.\"\"\"\n        auth.login()\n\n        response = client.get('/api/personnel/skills')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'skills' in data['data']\n        assert 'categories' in data['data']\n        assert len(data['data']['skills']) > 0\n\n        # Check skill structure\n        skill = data['data']['skills'][0]\n        required_fields = [\n            'id', 'name', 'category', 'description', 'user_count', 'users'\n        ]\n        for field in required_fields:\n            assert field in skill\n\n    def test_get_skills_with_category_filter(self, client, auth, sample_skills):\n        \"\"\"Test skills list filtered by category.\"\"\"\n        auth.login()\n\n        # Get first skill's category\n        skill = sample_skills[0]\n        category = skill.category\n\n        response = client.get(f'/api/personnel/skills?category={category}')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # All skills should belong to the specified category\n        for skill_data in data['data']['skills']:\n            assert skill_data['category'] == category\n\n    def test_get_skills_with_search(self, client, auth, sample_skills):\n        \"\"\"Test skills list with search parameter.\"\"\"\n        auth.login()\n\n        # Search for a specific skill\n        skill = sample_skills[0]\n        search_term = skill.name[:3]  # First 3 characters\n\n        response = client.get(f'/api/personnel/skills?search={search_term}')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # Check that search results contain the search term\n        for skill_data in data['data']['skills']:\n            skill_text = f\"{skill_data['name']} {skill_data['description'] or ''}\".lower()\n            assert search_term.lower() in skill_text\n\n    def test_get_skills_categories(self, client, auth, sample_skills):\n        \"\"\"Test that skills endpoint returns available categories.\"\"\"\n        auth.login()\n\n        response = client.get('/api/personnel/skills')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'categories' in data['data']\n        assert isinstance(data['data']['categories'], list)\n\n        # Categories should match skills categories\n        skill_categories = set(skill.category for skill in sample_skills if skill.category)\n        returned_categories = set(data['data']['categories'])\n        assert skill_categories.issubset(returned_categories)\n\n    def test_get_skills_unauthorized(self, client, auth):\n        \"\"\"Test skills list access without authentication.\"\"\"\n        # Ensure user is logged out\n        auth.logout()\n\n        response = client.get('/api/personnel/skills')\n        assert response.status_code == 401\n\n    def test_get_skills_forbidden(self, client, auth):\n        \"\"\"Test skills list access without proper permissions.\"\"\"\n        # Login as user without VIEW_PERSONNEL_DATA permission\n        auth.login(username='employee', password='password')\n\n        response = client.get('/api/personnel/skills')\n        assert response.status_code == 403\n", "modifiedCode": "\"\"\"\nTest suite for Personnel API endpoints.\nTests for users, departments, and skills management.\n\"\"\"\n\nimport pytest\nimport json\nfrom datetime import datetime, date\nfrom flask import url_for\n\nfrom models import User, Department, Skill, UserSkill, UserProfile\nfrom extensions import db\n\n\nclass TestPersonnelUsersAPI:\n    \"\"\"Test cases for /api/personnel/users endpoints.\"\"\"\n\n    def test_get_users_success(self, client, auth, sample_users):\n        \"\"\"Test successful retrieval of users list.\"\"\"\n        # Login as admin to have proper permissions\n        auth.login(username='admin', password='password')\n\n        response = client.get('/api/personnel/users')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'users' in data['data']\n        assert 'pagination' in data['data']\n        assert len(data['data']['users']) > 0\n\n        # Check user structure\n        user = data['data']['users'][0]\n        required_fields = [\n            'id', 'username', 'email', 'first_name', 'last_name',\n            'full_name', 'role', 'is_active'\n        ]\n        for field in required_fields:\n            assert field in user\n\n    def test_get_users_with_pagination(self, client, auth, sample_users):\n        \"\"\"Test users list with pagination parameters.\"\"\"\n        auth.login(username='admin', password='password')\n\n        response = client.get('/api/personnel/users?page=1&per_page=2')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert len(data['data']['users']) <= 2\n        assert data['data']['pagination']['per_page'] == 2\n        assert data['data']['pagination']['page'] == 1\n\n    def test_get_users_with_search(self, client, auth, sample_users):\n        \"\"\"Test users list with search parameter.\"\"\"\n        auth.login(username='admin', password='password')\n\n        # Search for a specific user\n        response = client.get('/api/personnel/users?search=admin')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # Check that search results contain the search term\n        for user in data['data']['users']:\n            user_text = f\"{user['username']} {user['first_name']} {user['last_name']} {user['email']}\".lower()\n            assert 'admin' in user_text\n\n    def test_get_users_with_department_filter(self, client, auth, sample_users, sample_departments):\n        \"\"\"Test users list filtered by department.\"\"\"\n        auth.login(username='admin', password='password')\n\n        # Get first department ID from database to avoid DetachedInstanceError\n        from models import Department\n        dept = Department.query.first()\n        if not dept:\n            pytest.skip(\"No departments found in database\")\n\n        response = client.get(f'/api/personnel/users?department_id={dept.id}')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # All users should belong to the specified department\n        for user in data['data']['users']:\n            if user['department_id'] is not None:\n                assert user['department_id'] == dept.id\n\n    def test_get_users_with_role_filter(self, client, auth, sample_users):\n        \"\"\"Test users list filtered by role.\"\"\"\n        auth.login(username='admin', password='password')\n\n        response = client.get('/api/personnel/users?role=admin')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # All users should have admin role\n        for user in data['data']['users']:\n            assert user['role'] == 'admin'\n\n    def test_get_users_with_active_filter(self, client, auth, sample_users):\n        \"\"\"Test users list filtered by active status.\"\"\"\n        auth.login(username='admin', password='password')\n\n        response = client.get('/api/personnel/users?is_active=true')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # All users should be active\n        for user in data['data']['users']:\n            assert user['is_active'] is True\n\n    def test_get_users_with_skills_filter(self, client, auth, sample_users, sample_skills):\n        \"\"\"Test users list filtered by skills.\"\"\"\n        auth.login(username='admin', password='password')\n\n        # Get first skill ID from database to avoid DetachedInstanceError\n        from models import Skill\n        skill = Skill.query.first()\n        if not skill:\n            pytest.skip(\"No skills found in database\")\n\n        response = client.get(f'/api/personnel/users?skills={skill.id}')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # Users should have the specified skill\n        for user in data['data']['users']:\n            skill_ids = [s['id'] for s in user['skills']]\n            if skill_ids:  # Only check if user has skills\n                assert skill.id in skill_ids\n\n    def test_get_users_with_ordering(self, client, auth, sample_users):\n        \"\"\"Test users list with custom ordering.\"\"\"\n        auth.login(username='admin', password='password')\n\n        # Test ascending order by first name\n        response = client.get('/api/personnel/users?order_by=first_name&order_dir=asc')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # Check that results are ordered\n        if len(data['data']['users']) > 1:\n            first_names = [user['first_name'] for user in data['data']['users']]\n            assert first_names == sorted(first_names)\n\n    def test_get_users_unauthorized(self, client, auth):\n        \"\"\"Test users list access without authentication.\"\"\"\n        # Ensure user is logged out\n        auth.logout()\n\n        response = client.get('/api/personnel/users')\n        assert response.status_code == 401\n\n    def test_get_users_forbidden(self, client, auth):\n        \"\"\"Test users list access without proper permissions.\"\"\"\n        # Login as user without VIEW_PERSONNEL_DATA permission\n        auth.login(username='employee', password='password')\n\n        response = client.get('/api/personnel/users')\n        assert response.status_code == 403\n\n    def test_get_user_by_id_success(self, client, auth, sample_users):\n        \"\"\"Test successful retrieval of specific user.\"\"\"\n        auth.login()\n\n        # Get first user ID from database to avoid DetachedInstanceError\n        from models import User\n        user = User.query.first()\n        if not user:\n            pytest.skip(\"No users found in database\")\n\n        response = client.get(f'/api/personnel/users/{user.id}')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'user' in data['data']\n\n        user_data = data['data']['user']\n        assert user_data['id'] == user.id\n        assert user_data['username'] == user.username\n        assert user_data['email'] == user.email\n\n        # Check detailed fields\n        detailed_fields = [\n            'bio', 'dark_mode', 'created_at', 'skills', 'projects', 'profile'\n        ]\n        for field in detailed_fields:\n            assert field in user_data\n\n    def test_get_user_by_id_not_found(self, client, auth):\n        \"\"\"Test retrieval of non-existent user.\"\"\"\n        auth.login()\n\n        response = client.get('/api/personnel/users/99999')\n        assert response.status_code == 404\n\n    def test_get_user_by_id_unauthorized(self, client, auth, sample_users):\n        \"\"\"Test user retrieval without authentication.\"\"\"\n        # Ensure user is logged out\n        auth.logout()\n\n        # Get first user ID from database to avoid DetachedInstanceError\n        from models import User\n        user = User.query.first()\n        if not user:\n            pytest.skip(\"No users found in database\")\n\n        response = client.get(f'/api/personnel/users/{user.id}')\n        assert response.status_code == 401\n\n\nclass TestPersonnelDepartmentsAPI:\n    \"\"\"Test cases for /api/personnel/departments endpoints.\"\"\"\n\n    def test_get_departments_success(self, client, auth, sample_departments):\n        \"\"\"Test successful retrieval of departments list.\"\"\"\n        auth.login()\n\n        response = client.get('/api/personnel/departments')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'departments' in data['data']\n        assert len(data['data']['departments']) > 0\n\n        # Check department structure\n        dept = data['data']['departments'][0]\n        required_fields = [\n            'id', 'name', 'description', 'manager_id', 'user_count', 'users'\n        ]\n        for field in required_fields:\n            assert field in dept\n\n    def test_get_departments_with_manager_info(self, client, auth, sample_departments, sample_users):\n        \"\"\"Test departments list includes manager information.\"\"\"\n        auth.login()\n\n        # Set a manager for a department\n        dept = sample_departments[0]\n        manager = sample_users[0]\n        dept.manager_id = manager.id\n        db.session.commit()\n\n        response = client.get('/api/personnel/departments')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        dept_data = next(d for d in data['data']['departments'] if d['id'] == dept.id)\n\n        assert dept_data['manager_id'] == manager.id\n        assert dept_data['manager'] is not None\n        assert dept_data['manager']['id'] == manager.id\n        assert dept_data['manager']['full_name'] == manager.full_name\n\n    def test_get_departments_with_users(self, client, auth, sample_departments, sample_users):\n        \"\"\"Test departments list includes user information.\"\"\"\n        auth.login()\n\n        # Assign users to department\n        dept = sample_departments[0]\n        user = sample_users[0]\n        user.department_id = dept.id\n        db.session.commit()\n\n        response = client.get('/api/personnel/departments')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        dept_data = next(d for d in data['data']['departments'] if d['id'] == dept.id)\n\n        assert dept_data['user_count'] >= 1\n        assert len(dept_data['users']) >= 1\n\n        user_data = next(u for u in dept_data['users'] if u['id'] == user.id)\n        assert user_data['full_name'] == user.full_name\n        assert user_data['position'] == user.position\n\n    def test_get_departments_unauthorized(self, client, auth):\n        \"\"\"Test departments list access without authentication.\"\"\"\n        # Ensure user is logged out\n        auth.logout()\n\n        response = client.get('/api/personnel/departments')\n        assert response.status_code == 401\n\n\nclass TestPersonnelSkillsAPI:\n    \"\"\"Test cases for /api/personnel/skills endpoints.\"\"\"\n\n    def test_get_skills_success(self, client, auth, sample_skills):\n        \"\"\"Test successful retrieval of skills list.\"\"\"\n        auth.login()\n\n        response = client.get('/api/personnel/skills')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'skills' in data['data']\n        assert 'categories' in data['data']\n        assert len(data['data']['skills']) > 0\n\n        # Check skill structure\n        skill = data['data']['skills'][0]\n        required_fields = [\n            'id', 'name', 'category', 'description', 'user_count', 'users'\n        ]\n        for field in required_fields:\n            assert field in skill\n\n    def test_get_skills_with_category_filter(self, client, auth, sample_skills):\n        \"\"\"Test skills list filtered by category.\"\"\"\n        auth.login()\n\n        # Get first skill's category\n        skill = sample_skills[0]\n        category = skill.category\n\n        response = client.get(f'/api/personnel/skills?category={category}')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # All skills should belong to the specified category\n        for skill_data in data['data']['skills']:\n            assert skill_data['category'] == category\n\n    def test_get_skills_with_search(self, client, auth, sample_skills):\n        \"\"\"Test skills list with search parameter.\"\"\"\n        auth.login()\n\n        # Search for a specific skill\n        skill = sample_skills[0]\n        search_term = skill.name[:3]  # First 3 characters\n\n        response = client.get(f'/api/personnel/skills?search={search_term}')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n\n        # Check that search results contain the search term\n        for skill_data in data['data']['skills']:\n            skill_text = f\"{skill_data['name']} {skill_data['description'] or ''}\".lower()\n            assert search_term.lower() in skill_text\n\n    def test_get_skills_categories(self, client, auth, sample_skills):\n        \"\"\"Test that skills endpoint returns available categories.\"\"\"\n        auth.login()\n\n        response = client.get('/api/personnel/skills')\n        assert response.status_code == 200\n\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'categories' in data['data']\n        assert isinstance(data['data']['categories'], list)\n\n        # Categories should match skills categories\n        skill_categories = set(skill.category for skill in sample_skills if skill.category)\n        returned_categories = set(data['data']['categories'])\n        assert skill_categories.issubset(returned_categories)\n\n    def test_get_skills_unauthorized(self, client, auth):\n        \"\"\"Test skills list access without authentication.\"\"\"\n        # Ensure user is logged out\n        auth.logout()\n\n        response = client.get('/api/personnel/skills')\n        assert response.status_code == 401\n\n    def test_get_skills_forbidden(self, client, auth):\n        \"\"\"Test skills list access without proper permissions.\"\"\"\n        # Login as user without VIEW_PERSONNEL_DATA permission\n        auth.login(username='employee', password='password')\n\n        response = client.get('/api/personnel/skills')\n        assert response.status_code == 403\n"}