{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/landing.py"}, "originalCode": "from flask import Blueprint, render_template\n\nlanding_bp = Blueprint('landing', __name__)\n\n@landing_bp.route('/')\ndef home():\n    \"\"\"Home page - serve Vue.js SPA\"\"\"\n    return render_template('spa.html')\n\n@landing_bp.route('/services')\n@landing_bp.route('/services/<path:path>')\ndef services():\n    \"\"\"Services pages - serve Vue.js SPA\"\"\"\n    return render_template('spa.html')\n\n@landing_bp.route('/about')\ndef about():\n    \"\"\"About page - serve Vue.js SPA\"\"\"\n    return render_template('spa.html')\n\n@landing_bp.route('/contact')\ndef contact():\n    \"\"\"Contact page - serve Vue.js SPA\"\"\"\n    return render_template('spa.html')\n\n@landing_bp.route('/privacy')\ndef privacy():\n    \"\"\"Privacy page - serve Vue.js SPA\"\"\"\n    return render_template('spa.html')\n", "modifiedCode": "from flask import Blueprint, render_template\n\nlanding_bp = Blueprint('landing', __name__)\n\n@landing_bp.route('/')\n@landing_bp.route('/services')\n@landing_bp.route('/services/<path:path>')\n@landing_bp.route('/about')\n@landing_bp.route('/contact')\n@landing_bp.route('/privacy')\ndef spa_routes():\n    \"\"\"Serve Vue.js SPA for all public routes\"\"\"\n    return render_template('spa.html')\n"}