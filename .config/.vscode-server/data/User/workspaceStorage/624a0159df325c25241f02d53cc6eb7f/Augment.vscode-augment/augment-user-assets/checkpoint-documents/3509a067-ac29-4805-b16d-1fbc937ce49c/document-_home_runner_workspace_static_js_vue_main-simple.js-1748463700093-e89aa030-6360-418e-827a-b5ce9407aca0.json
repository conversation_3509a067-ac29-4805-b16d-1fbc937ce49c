{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/main-simple.js"}, "originalCode": "/**\n * Vue.js 3 Simple Entry Point - IIFE Version\n * Temporary solution to get the app running\n */\n\n(function() {\n    'use strict';\n    \n    // Check if Vue.js is loaded\n    if (typeof Vue === 'undefined') {\n        console.error('Vue.js is not loaded');\n        return;\n    }\n    \n    const { createApp, ref, onMounted } = Vue;\n    const { createRouter, createWebHistory } = VueRouter;\n    const { createPinia, defineStore } = Pinia;\n\n    // Simple routes\n    const routes = [\n        { \n            path: '/', \n            component: {\n                template: `\n                    <div class=\"min-h-screen bg-white\">\n                        <nav class=\"bg-blue-600 text-white p-4\">\n                            <div class=\"container mx-auto\">\n                                <h1 class=\"text-2xl font-bold\">{{ tenantName }}</h1>\n                            </div>\n                        </nav>\n                        <main class=\"container mx-auto p-8\">\n                            <h2 class=\"text-3xl font-bold mb-6\">{{ heroTitle }}</h2>\n                            <p class=\"text-lg text-gray-600 mb-8\">{{ heroSubtitle }}</p>\n                            <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                                <div class=\"bg-gray-50 p-6 rounded-lg\">\n                                    <h3 class=\"text-xl font-semibold mb-2\">Servizi</h3>\n                                    <p>Scopri i nostri servizi</p>\n                                </div>\n                                <div class=\"bg-gray-50 p-6 rounded-lg\">\n                                    <h3 class=\"text-xl font-semibold mb-2\">Chi Siamo</h3>\n                                    <p>La nostra storia</p>\n                                </div>\n                                <div class=\"bg-gray-50 p-6 rounded-lg\">\n                                    <h3 class=\"text-xl font-semibold mb-2\">Contatti</h3>\n                                    <p>Mettiti in contatto</p>\n                                </div>\n                            </div>\n                        </main>\n                    </div>\n                `,\n                setup() {\n                    const tenantName = ref('DatVinci');\n                    const heroTitle = ref('Innovazione per il futuro');\n                    const heroSubtitle = ref('Supportiamo le aziende nel loro percorso di crescita');\n                    \n                    // Load tenant config\n                    onMounted(async () => {\n                        try {\n                            const response = await axios.get('/api/public/config');\n                            if (response.data.success) {\n                                const config = response.data.data;\n                                tenantName.value = config.company?.name || 'DatVinci';\n                                heroTitle.value = config.pages?.home?.hero?.title || 'Innovazione per il futuro';\n                                heroSubtitle.value = config.pages?.home?.hero?.subtitle || 'Supportiamo le aziende nel loro percorso di crescita';\n                            }\n                        } catch (error) {\n                            console.error('Failed to load tenant config:', error);\n                        }\n                    });\n                    \n                    return {\n                        tenantName,\n                        heroTitle,\n                        heroSubtitle\n                    };\n                }\n            }\n        },\n        { \n            path: '/services', \n            component: {\n                template: `\n                    <div class=\"min-h-screen bg-white\">\n                        <nav class=\"bg-blue-600 text-white p-4\">\n                            <div class=\"container mx-auto\">\n                                <h1 class=\"text-2xl font-bold\">DatVinci</h1>\n                            </div>\n                        </nav>\n                        <main class=\"container mx-auto p-8\">\n                            <h2 class=\"text-3xl font-bold mb-6\">I nostri servizi</h2>\n                            <p class=\"text-lg text-gray-600\">Soluzioni innovative per ogni esigenza aziendale</p>\n                        </main>\n                    </div>\n                `\n            }\n        },\n        { \n            path: '/about', \n            component: {\n                template: `\n                    <div class=\"min-h-screen bg-white\">\n                        <nav class=\"bg-blue-600 text-white p-4\">\n                            <div class=\"container mx-auto\">\n                                <h1 class=\"text-2xl font-bold\">DatVinci</h1>\n                            </div>\n                        </nav>\n                        <main class=\"container mx-auto p-8\">\n                            <h2 class=\"text-3xl font-bold mb-6\">Chi Siamo</h2>\n                            <p class=\"text-lg text-gray-600\">La nostra storia e i nostri valori</p>\n                        </main>\n                    </div>\n                `\n            }\n        },\n        { \n            path: '/contact', \n            component: {\n                template: `\n                    <div class=\"min-h-screen bg-white\">\n                        <nav class=\"bg-blue-600 text-white p-4\">\n                            <div class=\"container mx-auto\">\n                                <h1 class=\"text-2xl font-bold\">DatVinci</h1>\n                            </div>\n                        </nav>\n                        <main class=\"container mx-auto p-8\">\n                            <h2 class=\"text-3xl font-bold mb-6\">Contattaci</h2>\n                            <p class=\"text-lg text-gray-600\">Siamo qui per aiutarti</p>\n                        </main>\n                    </div>\n                `\n            }\n        },\n        { \n            path: '/privacy', \n            component: {\n                template: `\n                    <div class=\"min-h-screen bg-white\">\n                        <nav class=\"bg-blue-600 text-white p-4\">\n                            <div class=\"container mx-auto\">\n                                <h1 class=\"text-2xl font-bold\">DatVinci</h1>\n                            </div>\n                        </nav>\n                        <main class=\"container mx-auto p-8\">\n                            <h2 class=\"text-3xl font-bold mb-6\">Privacy Policy</h2>\n                            <p class=\"text-lg text-gray-600\">Informativa sulla privacy</p>\n                        </main>\n                    </div>\n                `\n            }\n        }\n    ];\n\n    // Create router\n    const router = createRouter({\n        history: createWebHistory(),\n        routes\n    });\n\n    // Create Pinia store\n    const pinia = createPinia();\n\n    // Create Vue app\n    const app = createApp({\n        template: '<router-view></router-view>'\n    });\n\n    // Use plugins\n    app.use(router);\n    app.use(pinia);\n\n    // Mount app\n    app.mount('#app');\n\n    console.log('🎉 Vue.js SPA loaded successfully!');\n    \n})();\n"}