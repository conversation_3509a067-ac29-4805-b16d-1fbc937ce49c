{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/Services.vue"}, "modifiedCode": "<template>\n  <div class=\"min-h-screen bg-white dark:bg-gray-900\">\n    <PublicNavigation />\n    \n    <!-- Hero Section -->\n    <section class=\"bg-brand-primary-600 text-white py-16\">\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <h1 class=\"text-4xl font-bold mb-4\">\n          {{ tenantStore.getPageContent('services', 'hero', 'title') || 'I nostri servizi' }}\n        </h1>\n        <p class=\"text-xl text-brand-primary-100\">\n          {{ tenantStore.getPageContent('services', 'hero', 'subtitle') || 'Soluzioni complete per la tua azienda' }}\n        </p>\n      </div>\n    </section>\n\n    <!-- Services Grid -->\n    <section class=\"py-16\">\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          <div \n            v-for=\"service in services\" \n            :key=\"service.id\"\n            class=\"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow\"\n          >\n            <div class=\"text-brand-primary-500 mb-4\">\n              <i :class=\"service.icon\" class=\"text-3xl\"></i>\n            </div>\n            <h3 class=\"text-xl font-semibold text-brand-text-primary mb-2\">\n              {{ service.name }}\n            </h3>\n            <p class=\"text-brand-text-secondary mb-4\">\n              {{ service.description }}\n            </p>\n            <router-link\n              :to=\"`/services/${service.id}`\"\n              class=\"text-brand-primary-600 hover:text-brand-primary-700 font-medium\"\n            >\n              Scopri di più →\n            </router-link>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <PublicFooter />\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { useTenantStore } from '../../stores/tenant.js'\nimport PublicNavigation from '../../components/public/PublicNavigation.vue'\nimport PublicFooter from '../../components/public/PublicFooter.vue'\n\nconst tenantStore = useTenantStore()\nconst services = ref([])\n\nasync function loadServices() {\n  try {\n    const response = await fetch('/api/public/services')\n    if (response.ok) {\n      const data = await response.json()\n      services.value = data.data || []\n    }\n  } catch (error) {\n    console.error('Failed to load services:', error)\n  }\n}\n\nonMounted(() => {\n  loadServices()\n})\n</script>\n"}