{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/layout/NotificationDropdown.vue"}, "modifiedCode": "<template>\n  <div class=\"relative\" v-click-outside=\"closeDropdown\">\n    <!-- Notification Button -->\n    <button\n      @click=\"toggleDropdown\"\n      class=\"p-2 rounded-full text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-brand-primary-500 transition-colors relative\"\n    >\n      <i class=\"fas fa-bell h-5 w-5\"></i>\n      \n      <!-- Badge -->\n      <span \n        v-if=\"appStore.unreadNotifications > 0\"\n        class=\"absolute -top-1 -right-1 h-4 w-4 bg-brand-error-500 text-white text-xs rounded-full flex items-center justify-center\"\n      >\n        {{ appStore.unreadNotifications > 9 ? '9+' : appStore.unreadNotifications }}\n      </span>\n    </button>\n\n    <!-- Dropdown -->\n    <transition name=\"dropdown\">\n      <div \n        v-if=\"isOpen\"\n        class=\"absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\"\n      >\n        <!-- Header -->\n        <div class=\"px-4 py-3 border-b border-gray-200 dark:border-gray-700\">\n          <div class=\"flex items-center justify-between\">\n            <h3 class=\"text-sm font-medium text-brand-text-primary\">\n              Notifiche\n            </h3>\n            <button\n              v-if=\"appStore.notifications.length > 0\"\n              @click=\"clearAll\"\n              class=\"text-xs text-brand-primary-600 hover:text-brand-primary-700 transition-colors\"\n            >\n              Cancella tutto\n            </button>\n          </div>\n        </div>\n\n        <!-- Notifications List -->\n        <div class=\"max-h-64 overflow-y-auto\">\n          <div v-if=\"appStore.notifications.length === 0\" class=\"px-4 py-6 text-center\">\n            <i class=\"fas fa-bell-slash h-8 w-8 text-gray-400 mx-auto mb-2\"></i>\n            <p class=\"text-sm text-brand-text-secondary\">Nessuna notifica</p>\n          </div>\n          \n          <div v-else class=\"py-1\">\n            <div\n              v-for=\"notification in appStore.notifications\"\n              :key=\"notification.id\"\n              class=\"px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer\"\n              :class=\"{ 'bg-brand-primary-50 dark:bg-brand-primary-900/20': !notification.read }\"\n              @click=\"markAsRead(notification)\"\n            >\n              <div class=\"flex items-start\">\n                <div class=\"flex-shrink-0\">\n                  <i \n                    :class=\"getNotificationIcon(notification.type)\"\n                    class=\"h-4 w-4 mt-0.5\"\n                  ></i>\n                </div>\n                <div class=\"ml-3 flex-1 min-w-0\">\n                  <p class=\"text-sm text-brand-text-primary\">\n                    {{ notification.message }}\n                  </p>\n                  <p class=\"text-xs text-brand-text-tertiary mt-1\">\n                    {{ formatTime(notification.timestamp) }}\n                  </p>\n                </div>\n                <div v-if=\"!notification.read\" class=\"flex-shrink-0 ml-2\">\n                  <div class=\"h-2 w-2 bg-brand-primary-500 rounded-full\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Footer -->\n        <div v-if=\"appStore.notifications.length > 0\" class=\"px-4 py-3 border-t border-gray-200 dark:border-gray-700\">\n          <router-link\n            to=\"/notifications\"\n            class=\"text-sm text-brand-primary-600 hover:text-brand-primary-700 transition-colors\"\n            @click=\"closeDropdown\"\n          >\n            Vedi tutte le notifiche\n          </router-link>\n        </div>\n      </div>\n    </transition>\n  </div>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { useAppStore } from '../../stores/app.js'\n\n// Store\nconst appStore = useAppStore()\n\n// State\nconst isOpen = ref(false)\n\n// Methods\nfunction toggleDropdown() {\n  isOpen.value = !isOpen.value\n}\n\nfunction closeDropdown() {\n  isOpen.value = false\n}\n\nfunction markAsRead(notification) {\n  appStore.markNotificationRead(notification.id)\n}\n\nfunction clearAll() {\n  appStore.clearNotifications()\n  closeDropdown()\n}\n\nfunction getNotificationIcon(type) {\n  const icons = {\n    success: 'fas fa-check-circle text-brand-success-500',\n    error: 'fas fa-exclamation-circle text-brand-error-500',\n    warning: 'fas fa-exclamation-triangle text-brand-warning-500',\n    info: 'fas fa-info-circle text-brand-primary-500'\n  }\n  return icons[type] || icons.info\n}\n\nfunction formatTime(timestamp) {\n  const now = new Date()\n  const time = new Date(timestamp)\n  const diff = now - time\n  \n  if (diff < 60000) return 'Ora'\n  if (diff < 3600000) return `${Math.floor(diff / 60000)}m fa`\n  if (diff < 86400000) return `${Math.floor(diff / 3600000)}h fa`\n  return time.toLocaleDateString('it-IT')\n}\n</script>\n\n<style scoped>\n/* Dropdown animations */\n.dropdown-enter-active,\n.dropdown-leave-active {\n  transition: all 0.2s ease;\n}\n\n.dropdown-enter-from,\n.dropdown-leave-to {\n  opacity: 0;\n  transform: translateY(-10px);\n}\n\n/* Custom scrollbar */\n.overflow-y-auto::-webkit-scrollbar {\n  width: 4px;\n}\n\n.overflow-y-auto::-webkit-scrollbar-track {\n  background: var(--brand-bg-tertiary);\n}\n\n.overflow-y-auto::-webkit-scrollbar-thumb {\n  background: var(--brand-border-secondary);\n  border-radius: 2px;\n}\n</style>\n"}