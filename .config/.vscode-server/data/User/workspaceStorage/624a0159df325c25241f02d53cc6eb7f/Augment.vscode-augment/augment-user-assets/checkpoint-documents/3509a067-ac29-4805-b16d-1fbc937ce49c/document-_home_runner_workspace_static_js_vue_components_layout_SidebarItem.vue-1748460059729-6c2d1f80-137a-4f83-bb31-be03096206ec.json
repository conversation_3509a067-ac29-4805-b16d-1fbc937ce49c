{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/layout/SidebarItem.vue"}, "modifiedCode": "<template>\n  <router-link\n    :to=\"to\"\n    class=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-150\"\n    :class=\"[\n      isActive \n        ? 'text-white bg-brand-primary-800 dark:bg-gray-700 shadow-md' \n        : 'text-brand-primary-100 hover:bg-brand-primary-600 dark:text-gray-300 dark:hover:bg-gray-700',\n      collapsed ? 'justify-center' : ''\n    ]\"\n    @click=\"handleClick\"\n  >\n    <!-- Icon -->\n    <i \n      :class=\"[\n        icon,\n        collapsed ? 'mr-0' : 'mr-3',\n        'h-6 w-6 flex-shrink-0 transition-colors duration-150'\n      ]\"\n    ></i>\n    \n    <!-- Label -->\n    <span \n      class=\"truncate transition-all duration-150\"\n      :class=\"{ 'hidden': collapsed }\"\n    >\n      {{ label }}\n    </span>\n    \n    <!-- Badge (optional) -->\n    <span \n      v-if=\"badge && !collapsed\"\n      class=\"ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n      :class=\"badgeClass\"\n    >\n      {{ badge }}\n    </span>\n    \n    <!-- Tooltip for collapsed state -->\n    <div \n      v-if=\"collapsed\"\n      class=\"absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 pointer-events-none group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap\"\n    >\n      {{ label }}\n      <div class=\"absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-full\">\n        <div class=\"border-4 border-transparent border-r-gray-900\"></div>\n      </div>\n    </div>\n  </router-link>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\nimport { useRoute } from 'vue-router'\n\n// Props\nconst props = defineProps({\n  to: {\n    type: String,\n    required: true\n  },\n  icon: {\n    type: String,\n    required: true\n  },\n  label: {\n    type: String,\n    required: true\n  },\n  collapsed: {\n    type: Boolean,\n    default: false\n  },\n  badge: {\n    type: [String, Number],\n    default: null\n  },\n  badgeType: {\n    type: String,\n    default: 'primary',\n    validator: (value) => ['primary', 'secondary', 'success', 'warning', 'error'].includes(value)\n  }\n})\n\n// Emits\nconst emit = defineEmits(['click'])\n\n// Route\nconst route = useRoute()\n\n// Computed\nconst isActive = computed(() => {\n  if (props.to === '/') {\n    return route.path === '/'\n  }\n  return route.path.startsWith(props.to)\n})\n\nconst badgeClass = computed(() => {\n  const classes = {\n    primary: 'bg-brand-primary-500 text-white',\n    secondary: 'bg-brand-secondary-500 text-white',\n    success: 'bg-brand-success-500 text-white',\n    warning: 'bg-brand-warning-500 text-white',\n    error: 'bg-brand-error-500 text-white'\n  }\n  return classes[props.badgeType] || classes.primary\n})\n\n// Methods\nfunction handleClick() {\n  emit('click')\n}\n</script>\n\n<style scoped>\n/* Hover effects */\n.group:hover .transition-colors {\n  color: inherit;\n}\n\n/* Active state enhancements */\n.router-link-active {\n  position: relative;\n}\n\n.router-link-active::before {\n  content: '';\n  position: absolute;\n  left: -8px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 4px;\n  height: 20px;\n  background-color: var(--brand-secondary-400);\n  border-radius: 0 2px 2px 0;\n}\n\n/* Tooltip positioning */\n.group {\n  position: relative;\n}\n\n/* Animation for badge */\n.badge-enter-active,\n.badge-leave-active {\n  transition: all 0.2s ease;\n}\n\n.badge-enter-from,\n.badge-leave-to {\n  opacity: 0;\n  transform: scale(0.8);\n}\n\n/* Focus styles */\n.group:focus {\n  outline: 2px solid var(--brand-primary-300);\n  outline-offset: 2px;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .transition-all,\n  .transition-colors,\n  .transition-opacity {\n    transition: none;\n  }\n}\n</style>\n"}