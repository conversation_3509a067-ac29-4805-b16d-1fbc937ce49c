{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/main-simple.js"}, "modifiedCode": "/**\n * Vue.js 3 Main Application Entry Point - VERSIONE SEMPLIFICATA\n * DatPortal SPA Basic Setup\n */\n\nimport { createApp } from 'vue'\n\n// Semplice app Vue.js per testare che i file locali funzionano\nconst vueApp = createApp({\n    data() {\n        return {\n            message: 'Vue.js SPA Funziona! 🎉 (File Locali)',\n            isAuthenticated: window.APP_CONFIG?.isAuthenticated || false,\n            user: window.APP_CONFIG?.user || null\n        }\n    },\n    template: `\n        <div class=\"min-h-screen flex items-center justify-center bg-gray-50\">\n            <div class=\"text-center\">\n                <h1 class=\"text-4xl font-bold text-green-600 mb-4\">{{ message }}</h1>\n                <p class=\"text-gray-600 mb-4\">DatPortal Vue.js SPA è attivo con file locali</p>\n                <div v-if=\"isAuthenticated\" class=\"mb-4\">\n                    <p class=\"text-blue-600\">Benvenuto, {{ user?.first_name || 'Utente' }}!</p>\n                </div>\n                <div class=\"mt-8 space-x-4\">\n                    <a href=\"/auth/login\" class=\"bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600\">\n                        Vai al Login\n                    </a>\n                    <a href=\"/dashboard\" class=\"bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600\">\n                        Dashboard\n                    </a>\n                </div>\n            </div>\n        </div>\n    `\n})\n\n// Mount the app\nvueApp.mount('#app')\n\n// Make it available globally for debugging\nwindow.vueApp = vueApp\n\nconsole.log('🎉 Vue.js SPA caricato con successo dai file locali!')\n\nexport default vueApp\n"}