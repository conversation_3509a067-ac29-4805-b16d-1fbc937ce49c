{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/stores/auth.js"}, "modifiedCode": "/**\n * Auth Store - Pinia Store per gestione autenticazione\n * Gestisce stato utente, permessi e sessione\n */\n\nimport { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\n\nexport const useAuthStore = defineStore('auth', () => {\n  // === STATE ===\n  const user = ref(null)\n  const isAuthenticated = ref(false)\n  const permissions = ref([])\n  const sessionValid = ref(true)\n  const lastActivity = ref(null)\n\n  // === COMPUTED ===\n  const userFullName = computed(() => {\n    if (!user.value) return ''\n    return user.value.full_name || `${user.value.first_name || ''} ${user.value.last_name || ''}`.trim()\n  })\n\n  const userInitials = computed(() => {\n    if (!user.value) return ''\n    const firstName = user.value.first_name || ''\n    const lastName = user.value.last_name || ''\n    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()\n  })\n\n  const userRole = computed(() => user.value?.role || 'guest')\n\n  const isAdmin = computed(() => userRole.value === 'admin')\n  const isManager = computed(() => ['admin', 'manager'].includes(userRole.value))\n  const isHR = computed(() => ['admin', 'human_resources'].includes(userRole.value))\n\n  // === ACTIONS ===\n\n  /**\n   * Carica i dati dell'utente corrente\n   */\n  async function loadCurrentUser() {\n    try {\n      const response = await fetch('/api/auth/me', {\n        credentials: 'include'\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        user.value = data.data.user\n        permissions.value = data.data.user.permissions || []\n        isAuthenticated.value = true\n        lastActivity.value = new Date().toISOString()\n        \n        console.log('✅ User loaded:', userFullName.value)\n        return true\n      } else {\n        throw new Error('Failed to load user data')\n      }\n    } catch (error) {\n      console.error('Failed to load current user:', error)\n      logout()\n      return false\n    }\n  }\n\n  /**\n   * Verifica se l'utente ha uno o più permessi\n   * @param {string|Array} requiredPermissions - Permesso/i richiesti\n   * @returns {boolean}\n   */\n  function hasPermission(requiredPermissions) {\n    if (!isAuthenticated.value) return false\n    \n    const required = Array.isArray(requiredPermissions) \n      ? requiredPermissions \n      : [requiredPermissions]\n    \n    return required.some(permission => permissions.value.includes(permission))\n  }\n\n  /**\n   * Verifica se l'utente ha tutti i permessi richiesti\n   * @param {Array} requiredPermissions - Array di permessi richiesti\n   * @returns {boolean}\n   */\n  function hasAllPermissions(requiredPermissions) {\n    if (!isAuthenticated.value) return false\n    \n    return requiredPermissions.every(permission => \n      permissions.value.includes(permission)\n    )\n  }\n\n  /**\n   * Verifica se l'utente ha almeno uno dei permessi richiesti\n   * @param {Array} requiredPermissions - Array di permessi richiesti\n   * @returns {boolean}\n   */\n  function hasAnyPermission(requiredPermissions) {\n    if (!isAuthenticated.value) return false\n    \n    return requiredPermissions.some(permission => \n      permissions.value.includes(permission)\n    )\n  }\n\n  /**\n   * Verifica la validità della sessione\n   */\n  async function checkSession() {\n    try {\n      const response = await fetch('/api/auth/check-session', {\n        credentials: 'include'\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        sessionValid.value = data.data.valid\n        lastActivity.value = data.data.last_activity\n        return true\n      } else {\n        sessionValid.value = false\n        return false\n      }\n    } catch (error) {\n      console.error('Session check failed:', error)\n      sessionValid.value = false\n      return false\n    }\n  }\n\n  /**\n   * Aggiorna le preferenze utente\n   * @param {Object} preferences - Nuove preferenze\n   */\n  async function updatePreferences(preferences) {\n    try {\n      const response = await fetch('/api/auth/preferences', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': window.APP_CONFIG.csrfToken\n        },\n        credentials: 'include',\n        body: JSON.stringify(preferences)\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        \n        // Aggiorna i dati utente locali\n        if (user.value) {\n          user.value.preferences = { ...user.value.preferences, ...preferences }\n        }\n        \n        return data.data.preferences\n      } else {\n        throw new Error('Failed to update preferences')\n      }\n    } catch (error) {\n      console.error('Failed to update preferences:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Aggiorna il profilo utente\n   * @param {Object} profileData - Dati del profilo da aggiornare\n   */\n  async function updateProfile(profileData) {\n    try {\n      const response = await fetch('/api/auth/profile', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': window.APP_CONFIG.csrfToken\n        },\n        credentials: 'include',\n        body: JSON.stringify(profileData)\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        \n        // Aggiorna i dati utente locali\n        if (user.value) {\n          Object.assign(user.value, data.data.user)\n        }\n        \n        return data.data.user\n      } else {\n        throw new Error('Failed to update profile')\n      }\n    } catch (error) {\n      console.error('Failed to update profile:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Logout dell'utente\n   */\n  function logout() {\n    user.value = null\n    isAuthenticated.value = false\n    permissions.value = []\n    sessionValid.value = false\n    lastActivity.value = null\n    \n    // Redirect to login page\n    window.location.href = '/auth/logout'\n  }\n\n  /**\n   * Inizializza lo store con i dati dell'utente se autenticato\n   */\n  function initialize() {\n    // Se l'utente è già autenticato (da configurazione globale)\n    if (window.APP_CONFIG.isAuthenticated && window.APP_CONFIG.user) {\n      user.value = window.APP_CONFIG.user\n      isAuthenticated.value = true\n      permissions.value = window.APP_CONFIG.user.permissions || []\n      lastActivity.value = new Date().toISOString()\n    }\n  }\n\n  /**\n   * Aggiorna l'ultima attività\n   */\n  function updateLastActivity() {\n    lastActivity.value = new Date().toISOString()\n  }\n\n  /**\n   * Verifica se l'utente può accedere a una risorsa\n   * @param {string} resource - Nome della risorsa\n   * @param {string} action - Azione da verificare (view, create, edit, delete)\n   * @returns {boolean}\n   */\n  function canAccess(resource, action = 'view') {\n    if (!isAuthenticated.value) return false\n    \n    // Admin può fare tutto\n    if (isAdmin.value) return true\n    \n    // Costruisci il nome del permesso\n    const permissionName = `${action}_${resource}`\n    \n    return hasPermission(permissionName)\n  }\n\n  // Inizializza lo store\n  initialize()\n\n  return {\n    // State\n    user,\n    isAuthenticated,\n    permissions,\n    sessionValid,\n    lastActivity,\n\n    // Computed\n    userFullName,\n    userInitials,\n    userRole,\n    isAdmin,\n    isManager,\n    isHR,\n\n    // Actions\n    loadCurrentUser,\n    hasPermission,\n    hasAllPermissions,\n    hasAnyPermission,\n    checkSession,\n    updatePreferences,\n    updateProfile,\n    logout,\n    initialize,\n    updateLastActivity,\n    canAccess\n  }\n})\n"}