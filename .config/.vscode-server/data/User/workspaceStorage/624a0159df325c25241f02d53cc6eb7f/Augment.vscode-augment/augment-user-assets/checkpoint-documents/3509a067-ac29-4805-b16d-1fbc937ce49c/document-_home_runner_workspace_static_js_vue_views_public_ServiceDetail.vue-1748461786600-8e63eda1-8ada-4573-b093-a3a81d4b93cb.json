{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/ServiceDetail.vue"}, "modifiedCode": "<template>\n  <div class=\"min-h-screen bg-white dark:bg-gray-900\">\n    <PublicNavigation />\n    \n    <div v-if=\"service\">\n      <!-- Hero Section -->\n      <section class=\"bg-brand-primary-600 text-white py-16\">\n        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h1 class=\"text-4xl font-bold mb-4\">{{ service.name }}</h1>\n          <p class=\"text-xl text-brand-primary-100\">{{ service.category }}</p>\n        </div>\n      </section>\n\n      <!-- Service Details -->\n      <section class=\"py-16\">\n        <div class=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div class=\"prose prose-lg max-w-none\">\n            <p>{{ service.description }}</p>\n          </div>\n        </div>\n      </section>\n\n      <!-- Related Services -->\n      <section v-if=\"service.related_services?.length\" class=\"py-16 bg-gray-50\">\n        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <h2 class=\"text-3xl font-bold text-center mb-12\">Servizi correlati</h2>\n          <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div \n              v-for=\"related in service.related_services\" \n              :key=\"related.id\"\n              class=\"bg-white p-6 rounded-lg shadow-md\"\n            >\n              <h3 class=\"text-xl font-semibold mb-2\">{{ related.name }}</h3>\n              <p class=\"text-gray-600 mb-4\">{{ related.description }}</p>\n              <router-link\n                :to=\"`/services/${related.id}`\"\n                class=\"text-brand-primary-600 hover:text-brand-primary-700 font-medium\"\n              >\n                Scopri di più →\n              </router-link>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n\n    <PublicFooter />\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { useRoute } from 'vue-router'\nimport PublicNavigation from '../../components/public/PublicNavigation.vue'\nimport PublicFooter from '../../components/public/PublicFooter.vue'\n\nconst route = useRoute()\nconst service = ref(null)\n\nasync function loadService() {\n  try {\n    const response = await fetch(`/api/public/services/${route.params.id}`)\n    if (response.ok) {\n      const data = await response.json()\n      service.value = data.data\n    }\n  } catch (error) {\n    console.error('Failed to load service:', error)\n  }\n}\n\nonMounted(() => {\n  loadService()\n})\n</script>\n"}