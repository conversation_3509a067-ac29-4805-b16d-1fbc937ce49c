{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/layout/MobileSidebarItem.vue"}, "modifiedCode": "<template>\n  <router-link\n    :to=\"to\"\n    class=\"group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors duration-150\"\n    :class=\"[\n      isActive \n        ? 'text-white bg-brand-primary-800 dark:bg-gray-700' \n        : 'text-brand-primary-100 hover:bg-brand-primary-600 dark:text-gray-300 dark:hover:bg-gray-700'\n    ]\"\n    @click=\"$emit('click')\"\n  >\n    <i :class=\"[icon, 'mr-4 h-6 w-6']\"></i>\n    {{ label }}\n  </router-link>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\nimport { useRoute } from 'vue-router'\n\n// Props\nconst props = defineProps({\n  to: {\n    type: String,\n    required: true\n  },\n  icon: {\n    type: String,\n    required: true\n  },\n  label: {\n    type: String,\n    required: true\n  }\n})\n\n// Emits\ndefineEmits(['click'])\n\n// Route\nconst route = useRoute()\n\n// Computed\nconst isActive = computed(() => {\n  if (props.to === '/') {\n    return route.path === '/'\n  }\n  return route.path.startsWith(props.to)\n})\n</script>\n\n<style scoped>\n/* Active state indicator */\n.router-link-active {\n  position: relative;\n}\n\n.router-link-active::before {\n  content: '';\n  position: absolute;\n  left: -8px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 4px;\n  height: 20px;\n  background-color: var(--brand-secondary-400);\n  border-radius: 0 2px 2px 0;\n}\n</style>\n"}