{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/landing/home.html"}, "originalCode": "{% extends 'base.html' %}\n\n{% block title %}DatPortal | DatVinci - Piattaforma Intranet Aziendale{% endblock %}\n\n{% block public_content %}\n<!-- Hero Section -->\n<section class=\"bg-gradient-to-r from-primary-600 to-secondary-500 text-white py-16\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\">\n            <div>\n                <h1 class=\"text-4xl md:text-5xl font-bold mb-6 leading-tight\">La piattaforma intranet completa per la tua azienda</h1>\n                <p class=\"text-xl mb-8\">G<PERSON><PERSON>ci progetti, risorse, clienti, e opportunità di finanziamento in un unico spazio digitale potenziato dall'intelligenza artificiale.</p>\n                <div class=\"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4\">\n                    <a href=\"{{ dashboard_url }}\" class=\"bg-white text-primary-700 hover:bg-gray-100 font-bold py-3 px-6 rounded-lg shadow-md transition duration-300 text-center\">\n                        Accedi alla Piattaforma\n                    </a>\n                    <a href=\"{{ url_for('landing.contact') }}\" class=\"bg-transparent hover:bg-white/10 border-2 border-white font-bold py-3 px-6 rounded-lg transition duration-300 text-center\">\n                        Contattaci\n                    </a>\n                </div>\n            </div>\n            <div class=\"hidden md:block relative\">\n                <img src=\"{{ url_for('static', filename='img/dashboard-preview.svg') }}\" alt=\"DatPortal Dashboard Preview\" class=\"rounded-lg shadow-xl\">\n                <div class=\"absolute inset-0 bg-gradient-to-tr from-primary-900/30 to-transparent rounded-lg\"></div>\n            </div>\n        </div>\n    </div>\n</section>\n\n<!-- Features Section -->\n<section class=\"py-16 bg-white dark:bg-gray-800\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center mb-12\">\n            <h2 class=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">Tutto ciò di cui la tua azienda ha bisogno</h2>\n            <p class=\"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\">\n                DatPortal integra tutti gli strumenti essenziali per gestire la tua azienda in modo efficiente e innovativo.\n            </p>\n        </div>\n        \n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            <!-- Feature 1 -->\n            <div class=\"bg-gray-50 dark:bg-gray-700 p-8 rounded-lg transition-all duration-300 hover:shadow-lg\">\n                <div class=\"text-primary-500 mb-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">Gestione Progetti</h3>\n                <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                    Pianifica, esegui e monitora i tuoi progetti in tempo reale con strumenti avanzati di project management.\n                </p>\n                <ul class=\"text-gray-600 dark:text-gray-300 space-y-2\">\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Dashboard di progetto personalizzabile\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Gestione attività e assegnazioni\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Monitoraggio budget e tempi\n                    </li>\n                </ul>\n            </div>\n            \n            <!-- Feature 2 -->\n            <div class=\"bg-gray-50 dark:bg-gray-700 p-8 rounded-lg transition-all duration-300 hover:shadow-lg\">\n                <div class=\"text-primary-500 mb-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">Gestione Clienti e CRM</h3>\n                <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                    Centralizza tutte le informazioni sui clienti e ottimizza le relazioni commerciali.\n                </p>\n                <ul class=\"text-gray-600 dark:text-gray-300 space-y-2\">\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Gestione contatti e aziende\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Tracking opportunità e proposte\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Reportistica attività commerciali\n                    </li>\n                </ul>\n            </div>\n            \n            <!-- Feature 3 -->\n            <div class=\"bg-gray-50 dark:bg-gray-700 p-8 rounded-lg transition-all duration-300 hover:shadow-lg\">\n                <div class=\"text-primary-500 mb-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">Bandi e Finanziamenti</h3>\n                <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                    Trova e gestisci opportunità di finanziamento per la tua azienda con l'assistenza dell'AI.\n                </p>\n                <ul class=\"text-gray-600 dark:text-gray-300 space-y-2\">\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Monitoraggio bandi disponibili\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Assistenza AI per la candidatura\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Gestione rendicontazione\n                    </li>\n                </ul>\n            </div>\n            \n            <!-- Feature 4 -->\n            <div class=\"bg-gray-50 dark:bg-gray-700 p-8 rounded-lg transition-all duration-300 hover:shadow-lg\">\n                <div class=\"text-primary-500 mb-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">Gestione Documentale</h3>\n                <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                    Archivia e condividi documenti aziendali in modo sicuro e organizzato.\n                </p>\n                <ul class=\"text-gray-600 dark:text-gray-300 space-y-2\">\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Repository documentale centralizzato\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Controllo versioni e permessi\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Ricerca avanzata nei documenti\n                    </li>\n                </ul>\n            </div>\n            \n            <!-- Feature 5 -->\n            <div class=\"bg-gray-50 dark:bg-gray-700 p-8 rounded-lg transition-all duration-300 hover:shadow-lg\">\n                <div class=\"text-primary-500 mb-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z\" />\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">Performance e Analisi</h3>\n                <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                    Monitora KPI, misura performance e analizza i dati aziendali con dashboard personalizzate.\n                </p>\n                <ul class=\"text-gray-600 dark:text-gray-300 space-y-2\">\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Dashboard KPI personalizzabili\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Reportistica avanzata\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Analisi predittiva con AI\n                    </li>\n                </ul>\n            </div>\n            \n            <!-- Feature 6 -->\n            <div class=\"bg-gray-50 dark:bg-gray-700 p-8 rounded-lg transition-all duration-300 hover:shadow-lg\">\n                <div class=\"text-primary-500 mb-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">Innovazione con AI</h3>\n                <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                    Funzionalità di intelligenza artificiale integrate per automatizzare e ottimizzare i processi.\n                </p>\n                <ul class=\"text-gray-600 dark:text-gray-300 space-y-2\">\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Automazione dei processi\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Generazione di contenuti assistita\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Analisi predittiva del business\n                    </li>\n                </ul>\n            </div>\n        </div>\n    </div>\n</section>\n\n<!-- Latest News Section -->\n{% if recent_news %}\n<section class=\"py-16 bg-gray-50 dark:bg-gray-700\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center mb-12\">\n            <h2 class=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">Ultime Notizie</h2>\n            <p class=\"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\">\n                Rimani aggiornato sulle novità e gli sviluppi della piattaforma DatPortal.\n            </p>\n        </div>\n        \n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {% for news in recent_news %}\n            <div class=\"bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md transition-all duration-300 hover:shadow-lg\">\n                {% if news.image_url %}\n                <img src=\"{{ news.image_url }}\" alt=\"{{ news.title }}\" class=\"w-full h-48 object-cover\">\n                {% else %}\n                <div class=\"w-full h-48 bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-16 w-16 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M19 20a2 2 0 002-2V8a2 2 0 00-2-2h-1M8 7h1m0 0h1m0 0h1m-3 3h1m0 0h1m0 0h1M9 7v3m0-3V4\" />\n                    </svg>\n                </div>\n                {% endif %}\n                <div class=\"p-6\">\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400 mb-2\">\n                        {{ news.created_at.strftime('%d/%m/%Y') }}\n                    </div>\n                    <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">{{ news.title }}</h3>\n                    <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                        {{ news.content|truncate(150) }}\n                    </p>\n                    <a href=\"#\" class=\"text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 font-medium inline-flex items-center\">\n                        Leggi di più\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </a>\n                </div>\n            </div>\n            {% endfor %}\n        </div>\n        \n        <div class=\"text-center mt-8\">\n            <a href=\"{{ url_for('communications.news') }}\" class=\"inline-flex items-center justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-300\">\n                Vedi tutte le notizie\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fill-rule=\"evenodd\" d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                </svg>\n            </a>\n        </div>\n    </div>\n</section>\n{% endif %}\n\n<!-- Services Preview Section -->\n{% if featured_services %}\n<section class=\"py-16 bg-white dark:bg-gray-800\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center mb-12\">\n            <h2 class=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">I Nostri Servizi</h2>\n            <p class=\"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\">\n                Scopri come possiamo aiutare la tua azienda a crescere e innovare.\n            </p>\n        </div>\n        \n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n            {% for service in featured_services %}\n            <div class=\"bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden shadow-md transition-all duration-300 hover:shadow-lg flex flex-col md:flex-row\">\n                <div class=\"bg-primary-500 text-white p-6 flex items-center justify-center md:w-1/3\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-16 w-16\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                    </svg>\n                </div>\n                <div class=\"p-6 md:w-2/3\">\n                    <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">{{ service.name }}</h3>\n                    <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                        {{ service.description|truncate(120) }}\n                    </p>\n                    <a href=\"{{ url_for('landing.service_detail', service_id=service.id) }}\" class=\"text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 font-medium inline-flex items-center\">\n                        Maggiori informazioni\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </a>\n                </div>\n            </div>\n            {% endfor %}\n        </div>\n        \n        <div class=\"text-center mt-8\">\n            <a href=\"{{ url_for('landing.services') }}\" class=\"inline-flex items-center justify-center py-3 px-6 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-base font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800 transition duration-300\">\n                Esplora tutti i servizi\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fill-rule=\"evenodd\" d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                </svg>\n            </a>\n        </div>\n    </div>\n</section>\n{% endif %}\n\n<!-- CTA Section -->\n<section class=\"py-12 bg-primary-600 text-white\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"lg:flex lg:items-center lg:justify-between\">\n            <div class=\"lg:w-0 lg:flex-1\">\n                <h2 class=\"text-3xl font-extrabold tracking-tight sm:text-4xl\">\n                    Pronto a potenziare la tua azienda?\n                </h2>\n                <p class=\"mt-3 max-w-3xl text-lg\">\n                    Richiedi una demo personalizzata di DatPortal e scopri come può trasformare la tua organizzazione.\n                </p>\n            </div>\n            <div class=\"mt-8 lg:mt-0 lg:ml-8\">\n                <div class=\"flex space-x-4\">\n                    <a href=\"{{ url_for('landing.contact') }}\" class=\"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-primary-600 bg-white hover:bg-primary-50 transition duration-300\">\n                        Richiedi una demo\n                    </a>\n                    <a href=\"{{ dashboard_url }}\" class=\"inline-flex items-center justify-center px-6 py-3 border border-white text-base font-medium rounded-md shadow-sm text-white bg-transparent hover:bg-white/10 transition duration-300\">\n                        Accedi alla piattaforma\n                    </a>\n                </div>\n            </div>\n        </div>\n    </div>\n</section>\n{% endblock %}\n\n{% block scripts %}\n<script>\n    // Aggiungi eventuali script specifici per la home page qui\n    document.addEventListener('DOMContentLoaded', function() {\n        // Esempio: animazione di fade-in per gli elementi principali\n        const heroSection = document.querySelector('.bg-gradient-to-r');\n        if (heroSection) {\n            heroSection.classList.add('animate-fadeIn');\n        }\n    });\n</script>\n{% endblock %}", "modifiedCode": "{% extends 'base.html' %}\n\n{% block title %}DatPortal | DatVinci - Piattaforma Intranet Aziendale{% endblock %}\n\n{% block public_content %}\n<!-- Hero Section -->\n<section class=\"bg-gradient-to-r from-primary-600 to-secondary-500 text-white py-16\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\">\n            <div>\n                <h1 class=\"text-4xl md:text-5xl font-bold mb-6 leading-tight\">La piattaforma intranet completa per la tua azienda</h1>\n                <p class=\"text-xl mb-8\">G<PERSON><PERSON>ci progetti, risorse, clienti, e opportunità di finanziamento in un unico spazio digitale potenziato dall'intelligenza artificiale.</p>\n                <div class=\"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4\">\n                    <a href=\"{{ dashboard_url }}\" class=\"bg-white text-primary-700 hover:bg-gray-100 font-bold py-3 px-6 rounded-lg shadow-md transition duration-300 text-center\">\n                        Accedi alla Piattaforma\n                    </a>\n                    <a href=\"{{ url_for('landing.contact') }}\" class=\"bg-transparent hover:bg-white/10 border-2 border-white font-bold py-3 px-6 rounded-lg transition duration-300 text-center\">\n                        Contattaci\n                    </a>\n                </div>\n            </div>\n            <div class=\"hidden md:block relative\">\n                <img src=\"{{ url_for('static', filename='img/dashboard-preview.svg') }}\" alt=\"DatPortal Dashboard Preview\" class=\"rounded-lg shadow-xl\">\n                <div class=\"absolute inset-0 bg-gradient-to-tr from-primary-900/30 to-transparent rounded-lg\"></div>\n            </div>\n        </div>\n    </div>\n</section>\n\n<!-- Features Section -->\n<section class=\"py-16 bg-white dark:bg-gray-800\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center mb-12\">\n            <h2 class=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">Tutto ciò di cui la tua azienda ha bisogno</h2>\n            <p class=\"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\">\n                DatPortal integra tutti gli strumenti essenziali per gestire la tua azienda in modo efficiente e innovativo.\n            </p>\n        </div>\n        \n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            <!-- Feature 1 -->\n            <div class=\"bg-gray-50 dark:bg-gray-700 p-8 rounded-lg transition-all duration-300 hover:shadow-lg\">\n                <div class=\"text-primary-500 mb-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">Gestione Progetti</h3>\n                <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                    Pianifica, esegui e monitora i tuoi progetti in tempo reale con strumenti avanzati di project management.\n                </p>\n                <ul class=\"text-gray-600 dark:text-gray-300 space-y-2\">\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Dashboard di progetto personalizzabile\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Gestione attività e assegnazioni\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Monitoraggio budget e tempi\n                    </li>\n                </ul>\n            </div>\n            \n            <!-- Feature 2 -->\n            <div class=\"bg-gray-50 dark:bg-gray-700 p-8 rounded-lg transition-all duration-300 hover:shadow-lg\">\n                <div class=\"text-primary-500 mb-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">Gestione Clienti e CRM</h3>\n                <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                    Centralizza tutte le informazioni sui clienti e ottimizza le relazioni commerciali.\n                </p>\n                <ul class=\"text-gray-600 dark:text-gray-300 space-y-2\">\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Gestione contatti e aziende\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Tracking opportunità e proposte\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Reportistica attività commerciali\n                    </li>\n                </ul>\n            </div>\n            \n            <!-- Feature 3 -->\n            <div class=\"bg-gray-50 dark:bg-gray-700 p-8 rounded-lg transition-all duration-300 hover:shadow-lg\">\n                <div class=\"text-primary-500 mb-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">Bandi e Finanziamenti</h3>\n                <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                    Trova e gestisci opportunità di finanziamento per la tua azienda con l'assistenza dell'AI.\n                </p>\n                <ul class=\"text-gray-600 dark:text-gray-300 space-y-2\">\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Monitoraggio bandi disponibili\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Assistenza AI per la candidatura\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Gestione rendicontazione\n                    </li>\n                </ul>\n            </div>\n            \n            <!-- Feature 4 -->\n            <div class=\"bg-gray-50 dark:bg-gray-700 p-8 rounded-lg transition-all duration-300 hover:shadow-lg\">\n                <div class=\"text-primary-500 mb-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">Gestione Documentale</h3>\n                <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                    Archivia e condividi documenti aziendali in modo sicuro e organizzato.\n                </p>\n                <ul class=\"text-gray-600 dark:text-gray-300 space-y-2\">\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Repository documentale centralizzato\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Controllo versioni e permessi\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Ricerca avanzata nei documenti\n                    </li>\n                </ul>\n            </div>\n            \n            <!-- Feature 5 -->\n            <div class=\"bg-gray-50 dark:bg-gray-700 p-8 rounded-lg transition-all duration-300 hover:shadow-lg\">\n                <div class=\"text-primary-500 mb-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z\" />\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">Performance e Analisi</h3>\n                <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                    Monitora KPI, misura performance e analizza i dati aziendali con dashboard personalizzate.\n                </p>\n                <ul class=\"text-gray-600 dark:text-gray-300 space-y-2\">\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Dashboard KPI personalizzabili\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Reportistica avanzata\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Analisi predittiva con AI\n                    </li>\n                </ul>\n            </div>\n            \n            <!-- Feature 6 -->\n            <div class=\"bg-gray-50 dark:bg-gray-700 p-8 rounded-lg transition-all duration-300 hover:shadow-lg\">\n                <div class=\"text-primary-500 mb-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">Innovazione con AI</h3>\n                <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                    Funzionalità di intelligenza artificiale integrate per automatizzare e ottimizzare i processi.\n                </p>\n                <ul class=\"text-gray-600 dark:text-gray-300 space-y-2\">\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Automazione dei processi\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Generazione di contenuti assistita\n                    </li>\n                    <li class=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                        Analisi predittiva del business\n                    </li>\n                </ul>\n            </div>\n        </div>\n    </div>\n</section>\n\n<!-- Latest News Section -->\n{% if recent_news %}\n<section class=\"py-16 bg-gray-50 dark:bg-gray-700\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center mb-12\">\n            <h2 class=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">Ultime Notizie</h2>\n            <p class=\"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\">\n                Rimani aggiornato sulle novità e gli sviluppi della piattaforma DatPortal.\n            </p>\n        </div>\n        \n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {% for news in recent_news %}\n            <div class=\"bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md transition-all duration-300 hover:shadow-lg\">\n                {% if news.image_url %}\n                <img src=\"{{ news.image_url }}\" alt=\"{{ news.title }}\" class=\"w-full h-48 object-cover\">\n                {% else %}\n                <div class=\"w-full h-48 bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-16 w-16 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M19 20a2 2 0 002-2V8a2 2 0 00-2-2h-1M8 7h1m0 0h1m0 0h1m-3 3h1m0 0h1m0 0h1M9 7v3m0-3V4\" />\n                    </svg>\n                </div>\n                {% endif %}\n                <div class=\"p-6\">\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400 mb-2\">\n                        {{ news.created_at.strftime('%d/%m/%Y') }}\n                    </div>\n                    <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">{{ news.title }}</h3>\n                    <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                        {{ news.content|truncate(150) }}\n                    </p>\n                    <a href=\"#\" class=\"text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 font-medium inline-flex items-center\">\n                        Leggi di più\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </a>\n                </div>\n            </div>\n            {% endfor %}\n        </div>\n        \n        <div class=\"text-center mt-8\">\n            <a href=\"{{ url_for('communications.news') }}\" class=\"inline-flex items-center justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-300\">\n                Vedi tutte le notizie\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fill-rule=\"evenodd\" d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                </svg>\n            </a>\n        </div>\n    </div>\n</section>\n{% endif %}\n\n<!-- Services Preview Section -->\n{% if featured_services %}\n<section class=\"py-16 bg-white dark:bg-gray-800\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center mb-12\">\n            <h2 class=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">I Nostri Servizi</h2>\n            <p class=\"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\">\n                Scopri come possiamo aiutare la tua azienda a crescere e innovare.\n            </p>\n        </div>\n        \n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n            {% for service in featured_services %}\n            <div class=\"bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden shadow-md transition-all duration-300 hover:shadow-lg flex flex-col md:flex-row\">\n                <div class=\"bg-primary-500 text-white p-6 flex items-center justify-center md:w-1/3\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-16 w-16\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                    </svg>\n                </div>\n                <div class=\"p-6 md:w-2/3\">\n                    <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">{{ service.name }}</h3>\n                    <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                        {{ service.description|truncate(120) }}\n                    </p>\n                    <a href=\"{{ url_for('landing.service_detail', service_id=service.id) }}\" class=\"text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 font-medium inline-flex items-center\">\n                        Maggiori informazioni\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </a>\n                </div>\n            </div>\n            {% endfor %}\n        </div>\n        \n        <div class=\"text-center mt-8\">\n            <a href=\"{{ url_for('landing.services') }}\" class=\"inline-flex items-center justify-center py-3 px-6 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-base font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800 transition duration-300\">\n                Esplora tutti i servizi\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fill-rule=\"evenodd\" d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                </svg>\n            </a>\n        </div>\n    </div>\n</section>\n{% endif %}\n\n<!-- CTA Section -->\n<section class=\"py-12 bg-primary-600 text-white\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"lg:flex lg:items-center lg:justify-between\">\n            <div class=\"lg:w-0 lg:flex-1\">\n                <h2 class=\"text-3xl font-extrabold tracking-tight sm:text-4xl\">\n                    Pronto a potenziare la tua azienda?\n                </h2>\n                <p class=\"mt-3 max-w-3xl text-lg\">\n                    Richiedi una demo personalizzata di DatPortal e scopri come può trasformare la tua organizzazione.\n                </p>\n            </div>\n            <div class=\"mt-8 lg:mt-0 lg:ml-8\">\n                <div class=\"flex space-x-4\">\n                    <a href=\"{{ url_for('landing.contact') }}\" class=\"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-primary-600 bg-white hover:bg-primary-50 transition duration-300\">\n                        Richiedi una demo\n                    </a>\n                    <a href=\"{{ dashboard_url }}\" class=\"inline-flex items-center justify-center px-6 py-3 border border-white text-base font-medium rounded-md shadow-sm text-white bg-transparent hover:bg-white/10 transition duration-300\">\n                        Accedi alla piattaforma\n                    </a>\n                </div>\n            </div>\n        </div>\n    </div>\n</section>\n{% endblock %}\n\n{% block scripts %}\n<script>\n    // Aggiungi eventuali script specifici per la home page qui\n    document.addEventListener('DOMContentLoaded', function() {\n        // Esempio: animazione di fade-in per gli elementi principali\n        const heroSection = document.querySelector('.bg-gradient-to-r');\n        if (heroSection) {\n            heroSection.classList.add('animate-fadeIn');\n        }\n    });\n</script>\n{% endblock %}"}