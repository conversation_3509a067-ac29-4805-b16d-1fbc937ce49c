{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/landing.py"}, "originalCode": "from flask import Blueprint, render_template, redirect, url_for, request, flash, current_app\nfrom flask_login import current_user\nfrom sqlalchemy import desc\n\nfrom app import db\nfrom models import Service, News\nfrom ai_services import analyze_text_with_openai\n\nlanding_bp = Blueprint('landing', __name__)\n\n@landing_bp.route('/')\n@landing_bp.route('/services')\n@landing_bp.route('/services/<path:path>')\n@landing_bp.route('/about')\n@landing_bp.route('/contact')\n@landing_bp.route('/privacy')\ndef spa_routes():\n    \"\"\"Serve Vue.js SPA for all public routes\"\"\"\n    return render_template('spa.html')\n\n@landing_bp.route('/services')\ndef services():\n    # Get all active services\n    services_query = Service.query.filter_by(status='active')\n\n    # Apply filtering by category if provided\n    category = request.args.get('category', None)\n    if category:\n        services_query = services_query.filter_by(category=category)\n\n    # Get all services\n    services = services_query.order_by(Service.name).all()\n\n    # Get unique categories for filtering\n    categories = db.session.query(Service.category).distinct().all()\n    categories = [c[0] for c in categories if c[0]]\n\n    # Group services by category for display\n    services_by_category = {}\n    for service in services:\n        if service.category not in services_by_category:\n            services_by_category[service.category] = []\n        services_by_category[service.category].append(service)\n\n    return render_template(\n        'landing/services.html',\n        services=services,\n        services_by_category=services_by_category,\n        categories=categories,\n        current_category=category\n    )\n\n@landing_bp.route('/services/<int:service_id>')\ndef service_detail(service_id):\n    service = Service.query.get_or_404(service_id)\n\n    # Get other services in the same category\n    related_services = Service.query.filter(\n        Service.category == service.category,\n        Service.id != service.id,\n        Service.status == 'active'\n    ).limit(3).all()\n\n    # AI-generated case study (if service description is substantial)\n    case_study = None\n    if service.description and len(service.description) > 50:\n        try:\n            case_study = analyze_text_with_openai(\n                service.description,\n                \"Genera un breve caso di studio su come questo servizio ha aiutato un'azienda cliente:\"\n            )\n        except Exception as e:\n            current_app.logger.error(f\"Service case study generation error: {str(e)}\")\n\n    return render_template(\n        'landing/service_detail.html',\n        service=service,\n        related_services=related_services,\n        case_study=case_study\n    )\n\n@landing_bp.route('/contact', methods=['GET', 'POST'])\ndef contact():\n    if request.method == 'POST':\n        name = request.form.get('name')\n        email = request.form.get('email')\n        message = request.form.get('message')\n\n        if not name or not email or not message:\n            flash('Tutti i campi sono obbligatori', 'error')\n            return redirect(url_for('landing.contact'))\n\n        # In a real application, we would send an email or store the contact request\n        # For now, we'll just show a success message\n\n        flash('Messaggio inviato con successo! Ti contatteremo presto.', 'success')\n        return redirect(url_for('landing.contact'))\n\n    # Company contact information\n    company_info = {\n        'name': 'DatVinci',\n        'address': 'Via dell\\'Innovazione 123, Milano',\n        'email': '<EMAIL>',\n        'phone': '+39 02 1234567',\n        'hours': 'Lun-Ven: 9:00 - 18:00'\n    }\n\n    return render_template('landing/contact.html', company_info=company_info)\n\n@landing_bp.route('/privacy')\ndef privacy():\n    privacy_policy = {\n        'last_updated': '2023-11-01',\n        'company_name': 'DatVinci',\n        'company_address': 'Via dell\\'Innovazione 123, Milano',\n        'company_email': '<EMAIL>'\n    }\n\n    return render_template('landing/privacy.html', privacy_policy=privacy_policy)\n\n@landing_bp.route('/about')\ndef about():\n    # Company information\n    company_info = {\n        'name': 'DatVinci',\n        'founded': '2018',\n        'mission': 'Supportare le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all\\'avanguardia.',\n        'vision': 'Diventare il partner di riferimento per l\\'innovazione tecnologica in Italia, aiutando le aziende a cogliere le opportunità del futuro digitale.',\n        'team_size': '25+ professionisti',\n        'expertise': [\n            'Sviluppo Software',\n            'Intelligenza Artificiale',\n            'Consulenza IT',\n            'Gestione Progetti Innovativi',\n            'Supporto su Bandi e Finanziamenti'\n        ]\n    }\n\n    # Team members (would normally come from a database)\n    team = [\n        {\n            'name': 'Marco Rossi',\n            'role': 'CEO & Founder',\n            'bio': 'Esperto di innovazione tecnologica con 15+ anni di esperienza nel settore IT.'\n        },\n        {\n            'name': 'Laura Bianchi',\n            'role': 'CTO',\n            'bio': 'Specialista in architetture software e intelligenza artificiale.'\n        },\n        {\n            'name': 'Alessandro Verdi',\n            'role': 'Business Development Manager',\n            'bio': 'Esperto in sviluppo commerciale e gestione clienti nel settore tech.'\n        }\n    ]\n\n    return render_template('landing/about.html', company_info=company_info, team=team)\n", "modifiedCode": "from flask import Blueprint, render_template, redirect, url_for, request, flash, current_app\nfrom flask_login import current_user\nfrom sqlalchemy import desc\n\nfrom app import db\nfrom models import Service, News\nfrom ai_services import analyze_text_with_openai\n\nlanding_bp = Blueprint('landing', __name__)\n\n@landing_bp.route('/')\ndef home():\n    \"\"\"Home page - serve Vue.js SPA\"\"\"\n    return render_template('spa.html')\n\n@landing_bp.route('/services')\n@landing_bp.route('/services/<path:path>')\ndef services():\n    \"\"\"Services pages - serve Vue.js SPA\"\"\"\n    return render_template('spa.html')\n\n@landing_bp.route('/about')\ndef about():\n    \"\"\"About page - serve Vue.js SPA\"\"\"\n    return render_template('spa.html')\n\n@landing_bp.route('/contact')\ndef contact():\n    \"\"\"Contact page - serve Vue.js SPA\"\"\"\n    return render_template('spa.html')\n\n@landing_bp.route('/privacy')\ndef privacy():\n    \"\"\"Privacy page - serve Vue.js SPA\"\"\"\n    return render_template('spa.html')\n\n@landing_bp.route('/services')\ndef services():\n    # Get all active services\n    services_query = Service.query.filter_by(status='active')\n\n    # Apply filtering by category if provided\n    category = request.args.get('category', None)\n    if category:\n        services_query = services_query.filter_by(category=category)\n\n    # Get all services\n    services = services_query.order_by(Service.name).all()\n\n    # Get unique categories for filtering\n    categories = db.session.query(Service.category).distinct().all()\n    categories = [c[0] for c in categories if c[0]]\n\n    # Group services by category for display\n    services_by_category = {}\n    for service in services:\n        if service.category not in services_by_category:\n            services_by_category[service.category] = []\n        services_by_category[service.category].append(service)\n\n    return render_template(\n        'landing/services.html',\n        services=services,\n        services_by_category=services_by_category,\n        categories=categories,\n        current_category=category\n    )\n\n@landing_bp.route('/services/<int:service_id>')\ndef service_detail(service_id):\n    service = Service.query.get_or_404(service_id)\n\n    # Get other services in the same category\n    related_services = Service.query.filter(\n        Service.category == service.category,\n        Service.id != service.id,\n        Service.status == 'active'\n    ).limit(3).all()\n\n    # AI-generated case study (if service description is substantial)\n    case_study = None\n    if service.description and len(service.description) > 50:\n        try:\n            case_study = analyze_text_with_openai(\n                service.description,\n                \"Genera un breve caso di studio su come questo servizio ha aiutato un'azienda cliente:\"\n            )\n        except Exception as e:\n            current_app.logger.error(f\"Service case study generation error: {str(e)}\")\n\n    return render_template(\n        'landing/service_detail.html',\n        service=service,\n        related_services=related_services,\n        case_study=case_study\n    )\n\n@landing_bp.route('/contact', methods=['GET', 'POST'])\ndef contact():\n    if request.method == 'POST':\n        name = request.form.get('name')\n        email = request.form.get('email')\n        message = request.form.get('message')\n\n        if not name or not email or not message:\n            flash('Tutti i campi sono obbligatori', 'error')\n            return redirect(url_for('landing.contact'))\n\n        # In a real application, we would send an email or store the contact request\n        # For now, we'll just show a success message\n\n        flash('Messaggio inviato con successo! Ti contatteremo presto.', 'success')\n        return redirect(url_for('landing.contact'))\n\n    # Company contact information\n    company_info = {\n        'name': 'DatVinci',\n        'address': 'Via dell\\'Innovazione 123, Milano',\n        'email': '<EMAIL>',\n        'phone': '+39 02 1234567',\n        'hours': 'Lun-Ven: 9:00 - 18:00'\n    }\n\n    return render_template('landing/contact.html', company_info=company_info)\n\n@landing_bp.route('/privacy')\ndef privacy():\n    privacy_policy = {\n        'last_updated': '2023-11-01',\n        'company_name': 'DatVinci',\n        'company_address': 'Via dell\\'Innovazione 123, Milano',\n        'company_email': '<EMAIL>'\n    }\n\n    return render_template('landing/privacy.html', privacy_policy=privacy_policy)\n\n@landing_bp.route('/about')\ndef about():\n    # Company information\n    company_info = {\n        'name': 'DatVinci',\n        'founded': '2018',\n        'mission': 'Supportare le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all\\'avanguardia.',\n        'vision': 'Diventare il partner di riferimento per l\\'innovazione tecnologica in Italia, aiutando le aziende a cogliere le opportunità del futuro digitale.',\n        'team_size': '25+ professionisti',\n        'expertise': [\n            'Sviluppo Software',\n            'Intelligenza Artificiale',\n            'Consulenza IT',\n            'Gestione Progetti Innovativi',\n            'Supporto su Bandi e Finanziamenti'\n        ]\n    }\n\n    # Team members (would normally come from a database)\n    team = [\n        {\n            'name': 'Marco Rossi',\n            'role': 'CEO & Founder',\n            'bio': 'Esperto di innovazione tecnologica con 15+ anni di esperienza nel settore IT.'\n        },\n        {\n            'name': 'Laura Bianchi',\n            'role': 'CTO',\n            'bio': 'Specialista in architetture software e intelligenza artificiale.'\n        },\n        {\n            'name': 'Alessandro Verdi',\n            'role': 'Business Development Manager',\n            'bio': 'Esperto in sviluppo commerciale e gestione clienti nel settore tech.'\n        }\n    ]\n\n    return render_template('landing/about.html', company_info=company_info, team=team)\n"}