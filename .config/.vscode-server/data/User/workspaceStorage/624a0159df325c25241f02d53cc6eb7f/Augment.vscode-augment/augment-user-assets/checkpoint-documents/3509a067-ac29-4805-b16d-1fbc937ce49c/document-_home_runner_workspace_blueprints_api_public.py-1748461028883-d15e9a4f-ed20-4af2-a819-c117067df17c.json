{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/api/public.py"}, "modifiedCode": "from flask import Blueprint, jsonify\nfrom sqlalchemy import desc\nfrom models import Service, News\n\npublic_api_bp = Blueprint('public_api', __name__, url_prefix='/api/public')\n\n@public_api_bp.route('/services/featured')\ndef featured_services():\n    \"\"\"Get featured services for homepage\"\"\"\n    try:\n        services = Service.query.filter_by(status='active').limit(4).all()\n        \n        services_data = []\n        for service in services:\n            services_data.append({\n                'id': service.id,\n                'name': service.name,\n                'description': service.description[:150] + '...' if len(service.description) > 150 else service.description,\n                'category': service.category,\n                'icon': get_service_icon(service.category)\n            })\n        \n        return jsonify({\n            'success': True,\n            'data': services_data\n        })\n    except Exception as e:\n        return jsonify({\n            'success': False,\n            'error': str(e)\n        }), 500\n\n@public_api_bp.route('/news/recent')\ndef recent_news():\n    \"\"\"Get recent news for homepage\"\"\"\n    try:\n        news = News.query.filter_by(is_published=True).order_by(desc(News.created_at)).limit(3).all()\n        \n        news_data = []\n        for item in news:\n            news_data.append({\n                'id': item.id,\n                'title': item.title,\n                'excerpt': item.content[:200] + '...' if len(item.content) > 200 else item.content,\n                'created_at': item.created_at.isoformat()\n            })\n        \n        return jsonify({\n            'success': True,\n            'data': news_data\n        })\n    except Exception as e:\n        return jsonify({\n            'success': False,\n            'error': str(e)\n        }), 500\n\n@public_api_bp.route('/services')\ndef all_services():\n    \"\"\"Get all services\"\"\"\n    try:\n        services = Service.query.filter_by(status='active').order_by(Service.name).all()\n        \n        services_data = []\n        for service in services:\n            services_data.append({\n                'id': service.id,\n                'name': service.name,\n                'description': service.description,\n                'category': service.category,\n                'icon': get_service_icon(service.category)\n            })\n        \n        return jsonify({\n            'success': True,\n            'data': services_data\n        })\n    except Exception as e:\n        return jsonify({\n            'success': False,\n            'error': str(e)\n        }), 500\n\n@public_api_bp.route('/services/<int:service_id>')\ndef service_detail(service_id):\n    \"\"\"Get service detail\"\"\"\n    try:\n        service = Service.query.get_or_404(service_id)\n        \n        # Get related services\n        related_services = Service.query.filter(\n            Service.category == service.category,\n            Service.id != service.id,\n            Service.status == 'active'\n        ).limit(3).all()\n        \n        related_data = []\n        for related in related_services:\n            related_data.append({\n                'id': related.id,\n                'name': related.name,\n                'description': related.description[:100] + '...' if len(related.description) > 100 else related.description,\n                'category': related.category\n            })\n        \n        service_data = {\n            'id': service.id,\n            'name': service.name,\n            'description': service.description,\n            'category': service.category,\n            'icon': get_service_icon(service.category),\n            'related_services': related_data\n        }\n        \n        return jsonify({\n            'success': True,\n            'data': service_data\n        })\n    except Exception as e:\n        return jsonify({\n            'success': False,\n            'error': str(e)\n        }), 500\n\ndef get_service_icon(category):\n    \"\"\"Get icon for service category\"\"\"\n    icons = {\n        'Sviluppo Software': 'fas fa-code',\n        'Intelligenza Artificiale': 'fas fa-brain',\n        'Consulenza IT': 'fas fa-laptop',\n        'Gestione Progetti': 'fas fa-project-diagram',\n        'Finanziamenti': 'fas fa-coins',\n        'default': 'fas fa-cog'\n    }\n    return icons.get(category, icons['default'])\n"}