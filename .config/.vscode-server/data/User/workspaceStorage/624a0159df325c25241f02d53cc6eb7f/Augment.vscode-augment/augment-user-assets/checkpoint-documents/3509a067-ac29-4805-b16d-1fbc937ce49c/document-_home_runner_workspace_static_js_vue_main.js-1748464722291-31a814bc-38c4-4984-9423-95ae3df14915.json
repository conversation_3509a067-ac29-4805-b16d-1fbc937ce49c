{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/main.js"}, "originalCode": "/**\n * Vue.js 3 Main Application Entry Point - VERSIONE SEMPLIFICATA\n * DatPortal SPA Basic Setup\n */\n\nimport { createApp } from 'vue'\n\n// Semplice app Vue.js per testare che i file locali funzionano\nconst vueApp = createApp({\n    data() {\n        return {\n            message: 'Vue.js SPA Funziona! 🎉 (File Locali)',\n            isAuthenticated: window.APP_CONFIG?.isAuthenticated || false,\n            user: window.APP_CONFIG?.user || null\n        }\n    },\n    template: `\n        <div class=\"min-h-screen flex items-center justify-center bg-gray-50\">\n            <div class=\"text-center\">\n                <h1 class=\"text-4xl font-bold text-green-600 mb-4\">{{ message }}</h1>\n                <p class=\"text-gray-600 mb-4\">DatPortal Vue.js SPA è attivo con file locali</p>\n                <div v-if=\"isAuthenticated\" class=\"mb-4\">\n                    <p class=\"text-blue-600\">Benvenuto, {{ user?.first_name || 'Utente' }}!</p>\n                </div>\n                <div class=\"mt-8 space-x-4\">\n                    <a href=\"/auth/login\" class=\"bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600\">\n                        Vai al Login\n                    </a>\n                    <a href=\"/dashboard\" class=\"bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600\">\n                        Dashboard\n                    </a>\n                </div>\n            </div>\n        </div>\n    `\n})\n\n// Mount the app\nvueApp.mount('#app')\n\n// Make it available globally for debugging\nwindow.vueApp = vueApp\n\nconsole.log('🎉 Vue.js SPA caricato con successo dai file locali!')\n\n// === ROUTER SETUP ===\nconst router = createRouter({\n  history: createWebHistory(),\n  routes,\n  scrollBehavior(to, from, savedPosition) {\n    if (savedPosition) {\n      return savedPosition\n    } else {\n      return { top: 0 }\n    }\n  }\n})\n\n// Router guards\nrouter.beforeEach(async (to, from, next) => {\n  const authStore = useAuthStore()\n  const appStore = useAppStore()\n\n  // Show loading\n  appStore.setLoading(true)\n\n  // Check authentication for protected routes\n  if (to.meta.requiresAuth && !authStore.isAuthenticated) {\n    // Redirect to login\n    window.location.href = '/auth/login'\n    return\n  }\n\n  // Check permissions for authenticated routes\n  if (to.meta.permissions && authStore.isAuthenticated && !authStore.hasPermission(to.meta.permissions)) {\n    // Redirect to dashboard if no permissions\n    next({ name: 'dashboard' })\n    return\n  }\n\n  next()\n})\n\nrouter.afterEach(() => {\n  const appStore = useAppStore()\n  // Hide loading after a short delay to prevent flashing\n  setTimeout(() => {\n    appStore.setLoading(false)\n  }, 100)\n})\n\n// === PINIA SETUP ===\nconst pinia = createPinia()\n\n// === VUE APP SETUP ===\nconst app = createApp(App)\n\n// Use plugins\napp.use(pinia)\napp.use(router)\n\n// Global properties\napp.config.globalProperties.$brand = useBrandStore()\napp.config.globalProperties.$auth = useAuthStore()\napp.config.globalProperties.$app = useAppStore()\n\n// Global error handler\napp.config.errorHandler = (error, instance, info) => {\n  console.error('Vue Error:', error)\n  console.error('Component:', instance)\n  console.error('Info:', info)\n\n  // Send to error reporting service if available\n  if (window.errorReporting) {\n    window.errorReporting.captureException(error, {\n      extra: { info, component: instance?.$options.name }\n    })\n  }\n}\n\n// Global warning handler (development only)\nif (window.APP_CONFIG.debug) {\n  app.config.warnHandler = (msg, instance, trace) => {\n    console.warn('Vue Warning:', msg)\n    console.warn('Trace:', trace)\n  }\n}\n\n// === GLOBAL DIRECTIVES ===\n\n// v-focus directive for auto-focusing elements\napp.directive('focus', {\n  mounted(el) {\n    el.focus()\n  }\n})\n\n// v-click-outside directive\napp.directive('click-outside', {\n  beforeMount(el, binding) {\n    el.clickOutsideEvent = function(event) {\n      if (!(el === event.target || el.contains(event.target))) {\n        binding.value(event)\n      }\n    }\n    document.addEventListener('click', el.clickOutsideEvent)\n  },\n  unmounted(el) {\n    document.removeEventListener('click', el.clickOutsideEvent)\n  }\n})\n\n// v-tooltip directive (simple tooltip)\napp.directive('tooltip', {\n  beforeMount(el, binding) {\n    el.setAttribute('title', binding.value)\n    el.style.position = 'relative'\n  },\n  updated(el, binding) {\n    el.setAttribute('title', binding.value)\n  }\n})\n\n// === GLOBAL MIXINS ===\n\n// Global mixin for common functionality\napp.mixin({\n  computed: {\n    $isMobile() {\n      return window.innerWidth < 768\n    },\n    $isTablet() {\n      return window.innerWidth >= 768 && window.innerWidth < 1024\n    },\n    $isDesktop() {\n      return window.innerWidth >= 1024\n    }\n  },\n  methods: {\n    // Format date utility\n    $formatDate(date, options = {}) {\n      if (!date) return ''\n      const defaultOptions = {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        ...options\n      }\n      return new Date(date).toLocaleDateString('it-IT', defaultOptions)\n    },\n\n    // Format currency utility\n    $formatCurrency(amount, currency = 'EUR') {\n      if (amount === null || amount === undefined) return ''\n      return new Intl.NumberFormat('it-IT', {\n        style: 'currency',\n        currency: currency\n      }).format(amount)\n    },\n\n    // Debounce utility\n    $debounce(func, wait) {\n      let timeout\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // Copy to clipboard utility\n    async $copyToClipboard(text) {\n      try {\n        await navigator.clipboard.writeText(text)\n        this.$app.showNotification('Copiato negli appunti', 'success')\n        return true\n      } catch (error) {\n        console.error('Failed to copy to clipboard:', error)\n        this.$app.showNotification('Errore durante la copia', 'error')\n        return false\n      }\n    },\n\n    // Download file utility\n    $downloadFile(url, filename) {\n      const link = document.createElement('a')\n      link.href = url\n      link.download = filename\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n    }\n  }\n})\n\n// === APP INITIALIZATION ===\n\nasync function initializeApp() {\n  try {\n    // Initialize stores\n    const brandStore = useBrandStore()\n    const authStore = useAuthStore()\n    const appStore = useAppStore()\n    const tenantStore = useTenantStore()\n\n    // Load tenant configuration first (needed for titles and content)\n    await tenantStore.loadConfig()\n\n    // Load brand configuration\n    await brandStore.loadBrandConfig()\n\n    // Load user data if authenticated\n    if (window.APP_CONFIG.isAuthenticated) {\n      await authStore.loadCurrentUser()\n    }\n\n    // Set initial app state\n    appStore.setInitialized(true)\n\n    console.log('🎨 DatPortal Vue.js SPA initialized successfully')\n    console.log('🏢 Tenant configuration loaded')\n    console.log('📱 Brand system loaded')\n    console.log('🔐 Authentication state:', authStore.isAuthenticated)\n\n  } catch (error) {\n    console.error('Failed to initialize app:', error)\n\n    // Show error to user\n    const appStore = useAppStore()\n    appStore.showNotification(\n      'Errore durante l\\'inizializzazione dell\\'applicazione',\n      'error'\n    )\n  }\n}\n\n// === MOUNT APP ===\n\n// Wait for DOM to be ready\nif (document.readyState === 'loading') {\n  document.addEventListener('DOMContentLoaded', async () => {\n    app.mount('#app')\n    await initializeApp()\n  })\n} else {\n  app.mount('#app')\n  initializeApp()\n}\n\n// === DEVELOPMENT HELPERS ===\n\nif (window.APP_CONFIG.debug) {\n  // Make stores available globally for debugging\n  window.stores = {\n    brand: useBrandStore(),\n    auth: useAuthStore(),\n    app: useAppStore()\n  }\n\n  // Make Vue app available globally\n  window.vueApp = app\n\n  console.log('🔧 Development mode: stores and app available on window object')\n}\n\n// === HOT MODULE REPLACEMENT (for development) ===\n\nif (import.meta.hot) {\n  import.meta.hot.accept()\n\n  // Handle HMR for stores\n  import.meta.hot.accept('./stores/brand.js', (newModule) => {\n    console.log('🔄 Hot reloading brand store')\n  })\n\n  import.meta.hot.accept('./stores/auth.js', (newModule) => {\n    console.log('🔄 Hot reloading auth store')\n  })\n\n  import.meta.hot.accept('./stores/app.js', (newModule) => {\n    console.log('🔄 Hot reloading app store')\n  })\n}\n\nexport default app\n", "modifiedCode": "/**\n * Vue.js 3 Main Application Entry Point - VERSIONE SEMPLIFICATA\n * DatPortal SPA Basic Setup\n */\n\nimport { createApp } from 'vue'\n\n// Semplice app Vue.js per testare che i file locali funzionano\nconst vueApp = createApp({\n    data() {\n        return {\n            message: 'Vue.js SPA Funziona! 🎉 (File Locali)',\n            isAuthenticated: window.APP_CONFIG?.isAuthenticated || false,\n            user: window.APP_CONFIG?.user || null\n        }\n    },\n    template: `\n        <div class=\"min-h-screen flex items-center justify-center bg-gray-50\">\n            <div class=\"text-center\">\n                <h1 class=\"text-4xl font-bold text-green-600 mb-4\">{{ message }}</h1>\n                <p class=\"text-gray-600 mb-4\">DatPortal Vue.js SPA è attivo con file locali</p>\n                <div v-if=\"isAuthenticated\" class=\"mb-4\">\n                    <p class=\"text-blue-600\">Benvenuto, {{ user?.first_name || 'Utente' }}!</p>\n                </div>\n                <div class=\"mt-8 space-x-4\">\n                    <a href=\"/auth/login\" class=\"bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600\">\n                        Vai al Login\n                    </a>\n                    <a href=\"/dashboard\" class=\"bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600\">\n                        Dashboard\n                    </a>\n                </div>\n            </div>\n        </div>\n    `\n})\n\n// Mount the app\nvueApp.mount('#app')\n\n// Make it available globally for debugging\nwindow.vueApp = vueApp\n\nconsole.log('🎉 Vue.js SPA caricato con successo dai file locali!')\n\nexport default vueApp\n\n// Router guards\nrouter.beforeEach(async (to, from, next) => {\n  const authStore = useAuthStore()\n  const appStore = useAppStore()\n\n  // Show loading\n  appStore.setLoading(true)\n\n  // Check authentication for protected routes\n  if (to.meta.requiresAuth && !authStore.isAuthenticated) {\n    // Redirect to login\n    window.location.href = '/auth/login'\n    return\n  }\n\n  // Check permissions for authenticated routes\n  if (to.meta.permissions && authStore.isAuthenticated && !authStore.hasPermission(to.meta.permissions)) {\n    // Redirect to dashboard if no permissions\n    next({ name: 'dashboard' })\n    return\n  }\n\n  next()\n})\n\nrouter.afterEach(() => {\n  const appStore = useAppStore()\n  // Hide loading after a short delay to prevent flashing\n  setTimeout(() => {\n    appStore.setLoading(false)\n  }, 100)\n})\n\n// === PINIA SETUP ===\nconst pinia = createPinia()\n\n// === VUE APP SETUP ===\nconst app = createApp(App)\n\n// Use plugins\napp.use(pinia)\napp.use(router)\n\n// Global properties\napp.config.globalProperties.$brand = useBrandStore()\napp.config.globalProperties.$auth = useAuthStore()\napp.config.globalProperties.$app = useAppStore()\n\n// Global error handler\napp.config.errorHandler = (error, instance, info) => {\n  console.error('Vue Error:', error)\n  console.error('Component:', instance)\n  console.error('Info:', info)\n\n  // Send to error reporting service if available\n  if (window.errorReporting) {\n    window.errorReporting.captureException(error, {\n      extra: { info, component: instance?.$options.name }\n    })\n  }\n}\n\n// Global warning handler (development only)\nif (window.APP_CONFIG.debug) {\n  app.config.warnHandler = (msg, instance, trace) => {\n    console.warn('Vue Warning:', msg)\n    console.warn('Trace:', trace)\n  }\n}\n\n// === GLOBAL DIRECTIVES ===\n\n// v-focus directive for auto-focusing elements\napp.directive('focus', {\n  mounted(el) {\n    el.focus()\n  }\n})\n\n// v-click-outside directive\napp.directive('click-outside', {\n  beforeMount(el, binding) {\n    el.clickOutsideEvent = function(event) {\n      if (!(el === event.target || el.contains(event.target))) {\n        binding.value(event)\n      }\n    }\n    document.addEventListener('click', el.clickOutsideEvent)\n  },\n  unmounted(el) {\n    document.removeEventListener('click', el.clickOutsideEvent)\n  }\n})\n\n// v-tooltip directive (simple tooltip)\napp.directive('tooltip', {\n  beforeMount(el, binding) {\n    el.setAttribute('title', binding.value)\n    el.style.position = 'relative'\n  },\n  updated(el, binding) {\n    el.setAttribute('title', binding.value)\n  }\n})\n\n// === GLOBAL MIXINS ===\n\n// Global mixin for common functionality\napp.mixin({\n  computed: {\n    $isMobile() {\n      return window.innerWidth < 768\n    },\n    $isTablet() {\n      return window.innerWidth >= 768 && window.innerWidth < 1024\n    },\n    $isDesktop() {\n      return window.innerWidth >= 1024\n    }\n  },\n  methods: {\n    // Format date utility\n    $formatDate(date, options = {}) {\n      if (!date) return ''\n      const defaultOptions = {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        ...options\n      }\n      return new Date(date).toLocaleDateString('it-IT', defaultOptions)\n    },\n\n    // Format currency utility\n    $formatCurrency(amount, currency = 'EUR') {\n      if (amount === null || amount === undefined) return ''\n      return new Intl.NumberFormat('it-IT', {\n        style: 'currency',\n        currency: currency\n      }).format(amount)\n    },\n\n    // Debounce utility\n    $debounce(func, wait) {\n      let timeout\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // Copy to clipboard utility\n    async $copyToClipboard(text) {\n      try {\n        await navigator.clipboard.writeText(text)\n        this.$app.showNotification('Copiato negli appunti', 'success')\n        return true\n      } catch (error) {\n        console.error('Failed to copy to clipboard:', error)\n        this.$app.showNotification('Errore durante la copia', 'error')\n        return false\n      }\n    },\n\n    // Download file utility\n    $downloadFile(url, filename) {\n      const link = document.createElement('a')\n      link.href = url\n      link.download = filename\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n    }\n  }\n})\n\n// === APP INITIALIZATION ===\n\nasync function initializeApp() {\n  try {\n    // Initialize stores\n    const brandStore = useBrandStore()\n    const authStore = useAuthStore()\n    const appStore = useAppStore()\n    const tenantStore = useTenantStore()\n\n    // Load tenant configuration first (needed for titles and content)\n    await tenantStore.loadConfig()\n\n    // Load brand configuration\n    await brandStore.loadBrandConfig()\n\n    // Load user data if authenticated\n    if (window.APP_CONFIG.isAuthenticated) {\n      await authStore.loadCurrentUser()\n    }\n\n    // Set initial app state\n    appStore.setInitialized(true)\n\n    console.log('🎨 DatPortal Vue.js SPA initialized successfully')\n    console.log('🏢 Tenant configuration loaded')\n    console.log('📱 Brand system loaded')\n    console.log('🔐 Authentication state:', authStore.isAuthenticated)\n\n  } catch (error) {\n    console.error('Failed to initialize app:', error)\n\n    // Show error to user\n    const appStore = useAppStore()\n    appStore.showNotification(\n      'Errore durante l\\'inizializzazione dell\\'applicazione',\n      'error'\n    )\n  }\n}\n\n// === MOUNT APP ===\n\n// Wait for DOM to be ready\nif (document.readyState === 'loading') {\n  document.addEventListener('DOMContentLoaded', async () => {\n    app.mount('#app')\n    await initializeApp()\n  })\n} else {\n  app.mount('#app')\n  initializeApp()\n}\n\n// === DEVELOPMENT HELPERS ===\n\nif (window.APP_CONFIG.debug) {\n  // Make stores available globally for debugging\n  window.stores = {\n    brand: useBrandStore(),\n    auth: useAuthStore(),\n    app: useAppStore()\n  }\n\n  // Make Vue app available globally\n  window.vueApp = app\n\n  console.log('🔧 Development mode: stores and app available on window object')\n}\n\n// === HOT MODULE REPLACEMENT (for development) ===\n\nif (import.meta.hot) {\n  import.meta.hot.accept()\n\n  // Handle HMR for stores\n  import.meta.hot.accept('./stores/brand.js', (newModule) => {\n    console.log('🔄 Hot reloading brand store')\n  })\n\n  import.meta.hot.accept('./stores/auth.js', (newModule) => {\n    console.log('🔄 Hot reloading auth store')\n  })\n\n  import.meta.hot.accept('./stores/app.js', (newModule) => {\n    console.log('🔄 Hot reloading app store')\n  })\n}\n\nexport default app\n"}