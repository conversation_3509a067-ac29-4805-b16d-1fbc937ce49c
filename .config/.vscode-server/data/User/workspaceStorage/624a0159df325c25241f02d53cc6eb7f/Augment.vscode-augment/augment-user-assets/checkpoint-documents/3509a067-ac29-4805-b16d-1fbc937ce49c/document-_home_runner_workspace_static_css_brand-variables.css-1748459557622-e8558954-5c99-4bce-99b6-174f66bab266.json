{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/css/brand-variables.css"}, "modifiedCode": "/**\n * Brand Variables CSS\n * Sistema di variabili CSS per gestione branding configurabile\n * Utilizzato da Tailwind CSS per generare classi personalizzate\n */\n\n:root {\n  /* === BRAND COLORS === */\n  /* Primary Color (Brand principale) */\n  --brand-primary-50: #e6f4fb;\n  --brand-primary-100: #b3e0f3;\n  --brand-primary-200: #80cceb;\n  --brand-primary-300: #4db7e3;\n  --brand-primary-400: #1aa3dc;\n  --brand-primary-500: #0080c0;\n  --brand-primary-600: #006699;\n  --brand-primary-700: #004d73;\n  --brand-primary-800: #00334d;\n  --brand-primary-900: #001a26;\n\n  /* Secondary Color (Brand secondario) */\n  --brand-secondary-50: #e6fff9;\n  --brand-secondary-100: #b3ffec;\n  --brand-secondary-200: #80ffdf;\n  --brand-secondary-300: #4dffd3;\n  --brand-secondary-400: #1affc6;\n  --brand-secondary-500: #00cc99;\n  --brand-secondary-600: #00a677;\n  --brand-secondary-700: #007f59;\n  --brand-secondary-800: #00533c;\n  --brand-secondary-900: #00291e;\n\n  /* Accent Color (Colore di accento) */\n  --brand-accent-50: #fef7e6;\n  --brand-accent-100: #fde8b3;\n  --brand-accent-200: #fcd980;\n  --brand-accent-300: #fbca4d;\n  --brand-accent-400: #fabb1a;\n  --brand-accent-500: #f59e0b;\n  --brand-accent-600: #d97706;\n  --brand-accent-700: #b45309;\n  --brand-accent-800: #92400e;\n  --brand-accent-900: #78350f;\n\n  /* Success, Warning, Error Colors */\n  --brand-success-50: #ecfdf5;\n  --brand-success-500: #10b981;\n  --brand-success-600: #059669;\n  --brand-success-700: #047857;\n\n  --brand-warning-50: #fffbeb;\n  --brand-warning-500: #f59e0b;\n  --brand-warning-600: #d97706;\n  --brand-warning-700: #b45309;\n\n  --brand-error-50: #fef2f2;\n  --brand-error-500: #ef4444;\n  --brand-error-600: #dc2626;\n  --brand-error-700: #b91c1c;\n\n  /* === TYPOGRAPHY === */\n  /* Font Families */\n  --brand-font-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n  --brand-font-body: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n  --brand-font-mono: 'JetBrains Mono', 'Fira Code', Consolas, 'Courier New', monospace;\n\n  /* Font Weights */\n  --brand-font-weight-light: 300;\n  --brand-font-weight-normal: 400;\n  --brand-font-weight-medium: 500;\n  --brand-font-weight-semibold: 600;\n  --brand-font-weight-bold: 700;\n  --brand-font-weight-extrabold: 800;\n\n  /* Font Sizes */\n  --brand-text-xs: 0.75rem;\n  --brand-text-sm: 0.875rem;\n  --brand-text-base: 1rem;\n  --brand-text-lg: 1.125rem;\n  --brand-text-xl: 1.25rem;\n  --brand-text-2xl: 1.5rem;\n  --brand-text-3xl: 1.875rem;\n  --brand-text-4xl: 2.25rem;\n\n  /* === SPACING === */\n  --brand-spacing-xs: 0.25rem;\n  --brand-spacing-sm: 0.5rem;\n  --brand-spacing-md: 1rem;\n  --brand-spacing-lg: 1.5rem;\n  --brand-spacing-xl: 2rem;\n  --brand-spacing-2xl: 3rem;\n  --brand-spacing-3xl: 4rem;\n\n  /* === BORDER RADIUS === */\n  --brand-radius-none: 0;\n  --brand-radius-sm: 0.125rem;\n  --brand-radius-md: 0.375rem;\n  --brand-radius-lg: 0.5rem;\n  --brand-radius-xl: 0.75rem;\n  --brand-radius-2xl: 1rem;\n  --brand-radius-full: 9999px;\n\n  /* === SHADOWS === */\n  --brand-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n  --brand-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  --brand-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n  --brand-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n\n  /* === TRANSITIONS === */\n  --brand-transition-fast: 150ms ease-in-out;\n  --brand-transition-normal: 250ms ease-in-out;\n  --brand-transition-slow: 350ms ease-in-out;\n\n  /* === LAYOUT === */\n  --brand-sidebar-width: 16rem;\n  --brand-sidebar-collapsed-width: 5rem;\n  --brand-header-height: 4rem;\n  --brand-container-max-width: 1280px;\n\n  /* === LOGO PATHS === */\n  --brand-logo-main: url('/static/img/logo.png');\n  --brand-logo-compact: url('/static/img/logo_transparent.png');\n  --brand-logo-white: url('/static/img/logo_white.png');\n  --brand-logo-dark: url('/static/img/logo_dark.png');\n}\n\n/* Dark Mode Variables */\n[data-theme=\"dark\"], .dark {\n  /* Override colors for dark mode */\n  --brand-bg-primary: #111827;\n  --brand-bg-secondary: #1f2937;\n  --brand-bg-tertiary: #374151;\n  --brand-text-primary: #f9fafb;\n  --brand-text-secondary: #d1d5db;\n  --brand-text-tertiary: #9ca3af;\n  --brand-border-primary: #374151;\n  --brand-border-secondary: #4b5563;\n}\n\n/* Light Mode Variables */\n[data-theme=\"light\"], :root {\n  --brand-bg-primary: #ffffff;\n  --brand-bg-secondary: #f9fafb;\n  --brand-bg-tertiary: #f3f4f6;\n  --brand-text-primary: #111827;\n  --brand-text-secondary: #374151;\n  --brand-text-tertiary: #6b7280;\n  --brand-border-primary: #e5e7eb;\n  --brand-border-secondary: #d1d5db;\n}\n\n/* === UTILITY CLASSES === */\n.brand-primary {\n  color: var(--brand-primary-500);\n}\n\n.brand-secondary {\n  color: var(--brand-secondary-500);\n}\n\n.brand-accent {\n  color: var(--brand-accent-500);\n}\n\n.bg-brand-primary {\n  background-color: var(--brand-primary-500);\n}\n\n.bg-brand-secondary {\n  background-color: var(--brand-secondary-500);\n}\n\n.bg-brand-accent {\n  background-color: var(--brand-accent-500);\n}\n\n.border-brand-primary {\n  border-color: var(--brand-primary-500);\n}\n\n.font-brand-heading {\n  font-family: var(--brand-font-heading);\n}\n\n.font-brand-body {\n  font-family: var(--brand-font-body);\n}\n\n.font-brand-mono {\n  font-family: var(--brand-font-mono);\n}\n\n.transition-brand {\n  transition: all var(--brand-transition-normal);\n}\n\n.shadow-brand {\n  box-shadow: var(--brand-shadow-md);\n}\n\n.rounded-brand {\n  border-radius: var(--brand-radius-md);\n}\n\n/* === COMPONENT SPECIFIC === */\n.brand-button {\n  background-color: var(--brand-primary-500);\n  color: white;\n  padding: var(--brand-spacing-sm) var(--brand-spacing-md);\n  border-radius: var(--brand-radius-md);\n  font-family: var(--brand-font-body);\n  font-weight: var(--brand-font-weight-medium);\n  transition: all var(--brand-transition-fast);\n  box-shadow: var(--brand-shadow-sm);\n}\n\n.brand-button:hover {\n  background-color: var(--brand-primary-600);\n  box-shadow: var(--brand-shadow-md);\n  transform: translateY(-1px);\n}\n\n.brand-card {\n  background-color: var(--brand-bg-primary);\n  border: 1px solid var(--brand-border-primary);\n  border-radius: var(--brand-radius-lg);\n  box-shadow: var(--brand-shadow-sm);\n  transition: all var(--brand-transition-normal);\n}\n\n.brand-card:hover {\n  box-shadow: var(--brand-shadow-md);\n  transform: translateY(-2px);\n}\n\n.brand-input {\n  background-color: var(--brand-bg-primary);\n  border: 1px solid var(--brand-border-secondary);\n  border-radius: var(--brand-radius-md);\n  padding: var(--brand-spacing-sm) var(--brand-spacing-md);\n  font-family: var(--brand-font-body);\n  color: var(--brand-text-primary);\n  transition: all var(--brand-transition-fast);\n}\n\n.brand-input:focus {\n  outline: none;\n  border-color: var(--brand-primary-500);\n  box-shadow: 0 0 0 3px rgba(var(--brand-primary-500), 0.1);\n}\n\n/* === RESPONSIVE BREAKPOINTS === */\n@media (max-width: 640px) {\n  :root {\n    --brand-sidebar-width: 100%;\n    --brand-spacing-md: 0.75rem;\n    --brand-spacing-lg: 1rem;\n  }\n}\n\n@media (max-width: 768px) {\n  :root {\n    --brand-text-base: 0.875rem;\n    --brand-text-lg: 1rem;\n    --brand-text-xl: 1.125rem;\n  }\n}\n"}