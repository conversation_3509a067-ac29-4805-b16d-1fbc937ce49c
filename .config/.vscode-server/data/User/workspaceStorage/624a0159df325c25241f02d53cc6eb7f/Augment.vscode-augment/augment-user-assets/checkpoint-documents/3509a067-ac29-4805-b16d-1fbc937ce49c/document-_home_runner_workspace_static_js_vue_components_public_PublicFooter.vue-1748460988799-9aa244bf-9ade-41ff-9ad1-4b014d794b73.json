{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/public/PublicFooter.vue"}, "modifiedCode": "<template>\n  <footer class=\"bg-gray-900 text-white\">\n    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n        <!-- Company Info -->\n        <div class=\"col-span-1 md:col-span-2\">\n          <div class=\"flex items-center mb-4\">\n            <img \n              :src=\"brandStore.brandConfig.logos.white || brandStore.currentLogo\" \n              alt=\"DatVinci\" \n              class=\"h-8 w-auto\"\n            >\n            <span class=\"ml-2 text-xl font-bold\">\n              {{ brandStore.brandConfig.name }}\n            </span>\n          </div>\n          <p class=\"text-gray-300 mb-4\">\n            Supportiamo le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all'avanguardia.\n          </p>\n          <div class=\"flex space-x-4\">\n            <a href=\"#\" class=\"text-gray-400 hover:text-white transition-colors\">\n              <i class=\"fab fa-linkedin text-xl\"></i>\n            </a>\n            <a href=\"#\" class=\"text-gray-400 hover:text-white transition-colors\">\n              <i class=\"fab fa-twitter text-xl\"></i>\n            </a>\n            <a href=\"#\" class=\"text-gray-400 hover:text-white transition-colors\">\n              <i class=\"fab fa-facebook text-xl\"></i>\n            </a>\n          </div>\n        </div>\n\n        <!-- Quick Links -->\n        <div>\n          <h3 class=\"text-lg font-semibold mb-4\">Link Rapidi</h3>\n          <ul class=\"space-y-2\">\n            <li>\n              <router-link to=\"/\" class=\"text-gray-300 hover:text-white transition-colors\">\n                Home\n              </router-link>\n            </li>\n            <li>\n              <router-link to=\"/services\" class=\"text-gray-300 hover:text-white transition-colors\">\n                Servizi\n              </router-link>\n            </li>\n            <li>\n              <router-link to=\"/about\" class=\"text-gray-300 hover:text-white transition-colors\">\n                Chi Siamo\n              </router-link>\n            </li>\n            <li>\n              <router-link to=\"/contact\" class=\"text-gray-300 hover:text-white transition-colors\">\n                Contatti\n              </router-link>\n            </li>\n          </ul>\n        </div>\n\n        <!-- Contact Info -->\n        <div>\n          <h3 class=\"text-lg font-semibold mb-4\">Contatti</h3>\n          <ul class=\"space-y-2 text-gray-300\">\n            <li class=\"flex items-center\">\n              <i class=\"fas fa-map-marker-alt mr-2\"></i>\n              Via dell'Innovazione 123, Milano\n            </li>\n            <li class=\"flex items-center\">\n              <i class=\"fas fa-phone mr-2\"></i>\n              +39 02 1234567\n            </li>\n            <li class=\"flex items-center\">\n              <i class=\"fas fa-envelope mr-2\"></i>\n              <EMAIL>\n            </li>\n          </ul>\n        </div>\n      </div>\n\n      <!-- Bottom Bar -->\n      <div class=\"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n        <p class=\"text-gray-400 text-sm\">\n          © {{ currentYear }} {{ brandStore.brandConfig.name }}. Tutti i diritti riservati.\n        </p>\n        <div class=\"flex space-x-4 mt-4 md:mt-0\">\n          <router-link to=\"/privacy\" class=\"text-gray-400 hover:text-white text-sm transition-colors\">\n            Privacy Policy\n          </router-link>\n          <span class=\"text-gray-600\">|</span>\n          <a href=\"/terms\" class=\"text-gray-400 hover:text-white text-sm transition-colors\">\n            Termini di Servizio\n          </a>\n        </div>\n      </div>\n    </div>\n  </footer>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\nimport { useBrandStore } from '../../stores/brand.js'\n\n// Store\nconst brandStore = useBrandStore()\n\n// Computed\nconst currentYear = computed(() => new Date().getFullYear())\n</script>\n\n<style scoped>\n/* Link hover effects */\na:hover {\n  text-decoration: none;\n}\n\n/* Social icons hover */\n.fab:hover {\n  transform: scale(1.1);\n  transition: transform 0.2s ease;\n}\n\n/* Focus styles */\na:focus {\n  outline: 2px solid var(--brand-primary-500);\n  outline-offset: 2px;\n  border-radius: 2px;\n}\n</style>\n"}