{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/landing/service_detail.html"}, "originalCode": "{% extends 'base.html' %}\n\n{% block title %}{{ service.name }} - DatPortal | DatVinci{% endblock %}\n\n{% block public_content %}\n<section class=\"bg-gradient-to-r from-primary-600 to-secondary-500 text-white py-12\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"max-w-4xl mx-auto\">\n            <nav class=\"flex mb-4\" aria-label=\"Breadcrumb\">\n                <ol class=\"inline-flex items-center space-x-1 md:space-x-3\">\n                    <li class=\"inline-flex items-center\">\n                        <a href=\"{{ url_for('landing.home') }}\" class=\"inline-flex items-center text-white hover:text-gray-200\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n                            </svg>\n                            Home\n                        </a>\n                    </li>\n                    <li>\n                        <div class=\"flex items-center\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\" />\n                            </svg>\n                            <a href=\"{{ url_for('landing.services') }}\" class=\"ml-1 text-white hover:text-gray-200 md:ml-2\">Servizi</a>\n                        </div>\n                    </li>\n                    <li aria-current=\"page\">\n                        <div class=\"flex items-center\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\" />\n                            </svg>\n                            <span class=\"ml-1 text-white md:ml-2\">{{ service.name }}</span>\n                        </div>\n                    </li>\n                </ol>\n            </nav>\n            \n            <h1 class=\"text-3xl font-bold mb-4\">{{ service.name }}</h1>\n            <div class=\"flex items-center mb-6\">\n                {% if service.category %}\n                <span class=\"bg-white/20 text-white px-3 py-1 rounded-full text-sm\">\n                    {{ service.category }}\n                </span>\n                {% endif %}\n            </div>\n        </div>\n    </div>\n</section>\n\n<section class=\"py-12 bg-white dark:bg-gray-800\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            <div class=\"lg:col-span-2\">\n                <div class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-8 shadow-md\">\n                    <h2 class=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">Descrizione del servizio</h2>\n                    <div class=\"prose prose-lg max-w-none text-gray-600 dark:text-gray-300\">\n                        {% if service.description %}\n                            {{ service.description|safe }}\n                        {% else %}\n                            <p>Nessuna descrizione disponibile per questo servizio.</p>\n                        {% endif %}\n                    </div>\n                    \n                    <div class=\"mt-8\">\n                        <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-4\">Dettagli</h3>\n                        <ul class=\"space-y-2\">\n                            <li class=\"flex items-start\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                                </svg>\n                                <span class=\"text-gray-600 dark:text-gray-300\">Consulenza personalizzata</span>\n                            </li>\n                            <li class=\"flex items-start\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                                </svg>\n                                <span class=\"text-gray-600 dark:text-gray-300\">Supporto continuo</span>\n                            </li>\n                            <li class=\"flex items-start\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                                </svg>\n                                <span class=\"text-gray-600 dark:text-gray-300\">Soluzioni innovative</span>\n                            </li>\n                        </ul>\n                    </div>\n                    \n                    {% if case_study %}\n                    <div class=\"mt-8 p-6 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800\">\n                        <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-4\">\n                            <span class=\"text-primary-600 dark:text-primary-400\">Case Study</span>\n                        </h3>\n                        <div class=\"prose prose-lg max-w-none text-gray-600 dark:text-gray-300\">\n                            {{ case_study|safe }}\n                        </div>\n                    </div>\n                    {% endif %}\n                    \n                    <div class=\"mt-8\">\n                        <a href=\"{{ url_for('landing.contact') }}\" class=\"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 transition duration-300\">\n                            Richiedi informazioni\n                        </a>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"lg:col-span-1\">\n                <div class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-8 shadow-md mb-8\">\n                    <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-4\">Dettagli rapidi</h3>\n                    <ul class=\"space-y-4\">\n                        {% if service.category %}\n                        <li class=\"flex items-start\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\" />\n                            </svg>\n                            <div>\n                                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Categoria</span>\n                                <p class=\"text-gray-900 dark:text-white\">{{ service.category }}</p>\n                            </div>\n                        </li>\n                        {% endif %}\n                        \n                        {% if service.hourly_rate %}\n                        <li class=\"flex items-start\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                            </svg>\n                            <div>\n                                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Tariffa oraria</span>\n                                <p class=\"text-gray-900 dark:text-white\">€{{ service.hourly_rate }}</p>\n                            </div>\n                        </li>\n                        {% endif %}\n                        \n                        <li class=\"flex items-start\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                            </svg>\n                            <div>\n                                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Durata tipica</span>\n                                <p class=\"text-gray-900 dark:text-white\">2-4 settimane</p>\n                            </div>\n                        </li>\n                        \n                        <li class=\"flex items-start\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                            </svg>\n                            <div>\n                                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Stato</span>\n                                <p class=\"text-gray-900 dark:text-white\">\n                                    {% if service.status == 'active' %}\n                                        <span class=\"text-green-600 dark:text-green-400\">Attivo</span>\n                                    {% else %}\n                                        <span class=\"text-red-600 dark:text-red-400\">Non disponibile</span>\n                                    {% endif %}\n                                </p>\n                            </div>\n                        </li>\n                    </ul>\n                </div>\n                \n                {% if related_services %}\n                <div class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-8 shadow-md\">\n                    <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-4\">Servizi correlati</h3>\n                    <div class=\"space-y-4\">\n                        {% for related in related_services %}\n                        <div class=\"border-b border-gray-200 dark:border-gray-600 last:border-b-0 pb-4 last:pb-0\">\n                            <h4 class=\"font-semibold text-gray-900 dark:text-white\">{{ related.name }}</h4>\n                            <p class=\"text-sm text-gray-600 dark:text-gray-300 mt-1\">\n                                {{ related.description|truncate(80) if related.description else 'Nessuna descrizione disponibile.' }}\n                            </p>\n                            <a href=\"{{ url_for('landing.service_detail', service_id=related.id) }}\" class=\"text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 text-sm font-medium inline-flex items-center mt-2\">\n                                Scopri di più\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 ml-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                                    <path fill-rule=\"evenodd\" d=\"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                                </svg>\n                            </a>\n                        </div>\n                        {% endfor %}\n                    </div>\n                </div>\n                {% endif %}\n            </div>\n        </div>\n    </div>\n</section>\n\n<!-- CTA Section -->\n<section class=\"py-12 bg-primary-600 text-white\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"lg:flex lg:items-center lg:justify-between\">\n            <div class=\"lg:w-0 lg:flex-1\">\n                <h2 class=\"text-2xl font-extrabold tracking-tight sm:text-3xl\">\n                    Interessato a questo servizio?\n                </h2>\n                <p class=\"mt-3 max-w-3xl text-lg\">\n                    Contattaci per una consulenza personalizzata e scopri come possiamo aiutarti a raggiungere i tuoi obiettivi.\n                </p>\n            </div>\n            <div class=\"mt-8 lg:mt-0 lg:ml-8\">\n                <div class=\"flex space-x-4\">\n                    <a href=\"{{ url_for('landing.contact') }}\" class=\"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-primary-600 bg-white hover:bg-primary-50 transition duration-300\">\n                        Contattaci ora\n                    </a>\n                </div>\n            </div>\n        </div>\n    </div>\n</section>\n{% endblock %}", "modifiedCode": "{% extends 'base.html' %}\n\n{% block title %}{{ service.name }} - DatPortal | DatVinci{% endblock %}\n\n{% block public_content %}\n<section class=\"bg-gradient-to-r from-primary-600 to-secondary-500 text-white py-12\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"max-w-4xl mx-auto\">\n            <nav class=\"flex mb-4\" aria-label=\"Breadcrumb\">\n                <ol class=\"inline-flex items-center space-x-1 md:space-x-3\">\n                    <li class=\"inline-flex items-center\">\n                        <a href=\"{{ url_for('landing.home') }}\" class=\"inline-flex items-center text-white hover:text-gray-200\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n                            </svg>\n                            Home\n                        </a>\n                    </li>\n                    <li>\n                        <div class=\"flex items-center\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\" />\n                            </svg>\n                            <a href=\"{{ url_for('landing.services') }}\" class=\"ml-1 text-white hover:text-gray-200 md:ml-2\">Servizi</a>\n                        </div>\n                    </li>\n                    <li aria-current=\"page\">\n                        <div class=\"flex items-center\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\" />\n                            </svg>\n                            <span class=\"ml-1 text-white md:ml-2\">{{ service.name }}</span>\n                        </div>\n                    </li>\n                </ol>\n            </nav>\n            \n            <h1 class=\"text-3xl font-bold mb-4\">{{ service.name }}</h1>\n            <div class=\"flex items-center mb-6\">\n                {% if service.category %}\n                <span class=\"bg-white/20 text-white px-3 py-1 rounded-full text-sm\">\n                    {{ service.category }}\n                </span>\n                {% endif %}\n            </div>\n        </div>\n    </div>\n</section>\n\n<section class=\"py-12 bg-white dark:bg-gray-800\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            <div class=\"lg:col-span-2\">\n                <div class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-8 shadow-md\">\n                    <h2 class=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">Descrizione del servizio</h2>\n                    <div class=\"prose prose-lg max-w-none text-gray-600 dark:text-gray-300\">\n                        {% if service.description %}\n                            {{ service.description|safe }}\n                        {% else %}\n                            <p>Nessuna descrizione disponibile per questo servizio.</p>\n                        {% endif %}\n                    </div>\n                    \n                    <div class=\"mt-8\">\n                        <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-4\">Dettagli</h3>\n                        <ul class=\"space-y-2\">\n                            <li class=\"flex items-start\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                                </svg>\n                                <span class=\"text-gray-600 dark:text-gray-300\">Consulenza personalizzata</span>\n                            </li>\n                            <li class=\"flex items-start\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                                </svg>\n                                <span class=\"text-gray-600 dark:text-gray-300\">Supporto continuo</span>\n                            </li>\n                            <li class=\"flex items-start\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                                </svg>\n                                <span class=\"text-gray-600 dark:text-gray-300\">Soluzioni innovative</span>\n                            </li>\n                        </ul>\n                    </div>\n                    \n                    {% if case_study %}\n                    <div class=\"mt-8 p-6 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800\">\n                        <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-4\">\n                            <span class=\"text-primary-600 dark:text-primary-400\">Case Study</span>\n                        </h3>\n                        <div class=\"prose prose-lg max-w-none text-gray-600 dark:text-gray-300\">\n                            {{ case_study|safe }}\n                        </div>\n                    </div>\n                    {% endif %}\n                    \n                    <div class=\"mt-8\">\n                        <a href=\"{{ url_for('landing.contact') }}\" class=\"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 transition duration-300\">\n                            Richiedi informazioni\n                        </a>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"lg:col-span-1\">\n                <div class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-8 shadow-md mb-8\">\n                    <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-4\">Dettagli rapidi</h3>\n                    <ul class=\"space-y-4\">\n                        {% if service.category %}\n                        <li class=\"flex items-start\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\" />\n                            </svg>\n                            <div>\n                                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Categoria</span>\n                                <p class=\"text-gray-900 dark:text-white\">{{ service.category }}</p>\n                            </div>\n                        </li>\n                        {% endif %}\n                        \n                        {% if service.hourly_rate %}\n                        <li class=\"flex items-start\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                            </svg>\n                            <div>\n                                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Tariffa oraria</span>\n                                <p class=\"text-gray-900 dark:text-white\">€{{ service.hourly_rate }}</p>\n                            </div>\n                        </li>\n                        {% endif %}\n                        \n                        <li class=\"flex items-start\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                            </svg>\n                            <div>\n                                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Durata tipica</span>\n                                <p class=\"text-gray-900 dark:text-white\">2-4 settimane</p>\n                            </div>\n                        </li>\n                        \n                        <li class=\"flex items-start\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-primary-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n                            </svg>\n                            <div>\n                                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Stato</span>\n                                <p class=\"text-gray-900 dark:text-white\">\n                                    {% if service.status == 'active' %}\n                                        <span class=\"text-green-600 dark:text-green-400\">Attivo</span>\n                                    {% else %}\n                                        <span class=\"text-red-600 dark:text-red-400\">Non disponibile</span>\n                                    {% endif %}\n                                </p>\n                            </div>\n                        </li>\n                    </ul>\n                </div>\n                \n                {% if related_services %}\n                <div class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-8 shadow-md\">\n                    <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-4\">Servizi correlati</h3>\n                    <div class=\"space-y-4\">\n                        {% for related in related_services %}\n                        <div class=\"border-b border-gray-200 dark:border-gray-600 last:border-b-0 pb-4 last:pb-0\">\n                            <h4 class=\"font-semibold text-gray-900 dark:text-white\">{{ related.name }}</h4>\n                            <p class=\"text-sm text-gray-600 dark:text-gray-300 mt-1\">\n                                {{ related.description|truncate(80) if related.description else 'Nessuna descrizione disponibile.' }}\n                            </p>\n                            <a href=\"{{ url_for('landing.service_detail', service_id=related.id) }}\" class=\"text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 text-sm font-medium inline-flex items-center mt-2\">\n                                Scopri di più\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 ml-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                                    <path fill-rule=\"evenodd\" d=\"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                                </svg>\n                            </a>\n                        </div>\n                        {% endfor %}\n                    </div>\n                </div>\n                {% endif %}\n            </div>\n        </div>\n    </div>\n</section>\n\n<!-- CTA Section -->\n<section class=\"py-12 bg-primary-600 text-white\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"lg:flex lg:items-center lg:justify-between\">\n            <div class=\"lg:w-0 lg:flex-1\">\n                <h2 class=\"text-2xl font-extrabold tracking-tight sm:text-3xl\">\n                    Interessato a questo servizio?\n                </h2>\n                <p class=\"mt-3 max-w-3xl text-lg\">\n                    Contattaci per una consulenza personalizzata e scopri come possiamo aiutarti a raggiungere i tuoi obiettivi.\n                </p>\n            </div>\n            <div class=\"mt-8 lg:mt-0 lg:ml-8\">\n                <div class=\"flex space-x-4\">\n                    <a href=\"{{ url_for('landing.contact') }}\" class=\"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-primary-600 bg-white hover:bg-primary-50 transition duration-300\">\n                        Contattaci ora\n                    </a>\n                </div>\n            </div>\n        </div>\n    </div>\n</section>\n{% endblock %}"}