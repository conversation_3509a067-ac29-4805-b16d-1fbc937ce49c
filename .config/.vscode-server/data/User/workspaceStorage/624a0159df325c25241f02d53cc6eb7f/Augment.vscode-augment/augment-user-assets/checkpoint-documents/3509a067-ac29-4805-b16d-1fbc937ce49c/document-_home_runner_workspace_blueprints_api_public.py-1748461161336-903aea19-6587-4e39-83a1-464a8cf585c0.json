{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/api/public.py"}, "originalCode": "from flask import Blueprint, jsonify\nfrom sqlalchemy import desc\nfrom models import Service, News\n\npublic_api_bp = Blueprint('public_api', __name__, url_prefix='/api/public')\n\n@public_api_bp.route('/services/featured')\ndef featured_services():\n    \"\"\"Get featured services for homepage\"\"\"\n    try:\n        services = Service.query.filter_by(status='active').limit(4).all()\n\n        services_data = []\n        for service in services:\n            services_data.append({\n                'id': service.id,\n                'name': service.name,\n                'description': service.description[:150] + '...' if len(service.description) > 150 else service.description,\n                'category': service.category,\n                'icon': get_service_icon(service.category)\n            })\n\n        return jsonify({\n            'success': True,\n            'data': services_data\n        })\n    except Exception as e:\n        return jsonify({\n            'success': False,\n            'error': str(e)\n        }), 500\n\n@public_api_bp.route('/news/recent')\ndef recent_news():\n    \"\"\"Get recent news for homepage\"\"\"\n    try:\n        news = News.query.filter_by(is_published=True).order_by(desc(News.created_at)).limit(3).all()\n\n        news_data = []\n        for item in news:\n            news_data.append({\n                'id': item.id,\n                'title': item.title,\n                'excerpt': item.content[:200] + '...' if len(item.content) > 200 else item.content,\n                'created_at': item.created_at.isoformat()\n            })\n\n        return jsonify({\n            'success': True,\n            'data': news_data\n        })\n    except Exception as e:\n        return jsonify({\n            'success': False,\n            'error': str(e)\n        }), 500\n\n@public_api_bp.route('/services')\ndef all_services():\n    \"\"\"Get all services\"\"\"\n    try:\n        services = Service.query.filter_by(status='active').order_by(Service.name).all()\n\n        services_data = []\n        for service in services:\n            services_data.append({\n                'id': service.id,\n                'name': service.name,\n                'description': service.description,\n                'category': service.category,\n                'icon': get_service_icon(service.category)\n            })\n\n        return jsonify({\n            'success': True,\n            'data': services_data\n        })\n    except Exception as e:\n        return jsonify({\n            'success': False,\n            'error': str(e)\n        }), 500\n\n@public_api_bp.route('/services/<int:service_id>')\ndef service_detail(service_id):\n    \"\"\"Get service detail\"\"\"\n    try:\n        service = Service.query.get_or_404(service_id)\n\n        # Get related services\n        related_services = Service.query.filter(\n            Service.category == service.category,\n            Service.id != service.id,\n            Service.status == 'active'\n        ).limit(3).all()\n\n        related_data = []\n        for related in related_services:\n            related_data.append({\n                'id': related.id,\n                'name': related.name,\n                'description': related.description[:100] + '...' if len(related.description) > 100 else related.description,\n                'category': related.category\n            })\n\n        service_data = {\n            'id': service.id,\n            'name': service.name,\n            'description': service.description,\n            'category': service.category,\n            'icon': get_service_icon(service.category),\n            'related_services': related_data\n        }\n\n        return jsonify({\n            'success': True,\n            'data': service_data\n        })\n    except Exception as e:\n        return jsonify({\n            'success': False,\n            'error': str(e)\n        }), 500\n\ndef get_service_icon(category):\n    \"\"\"Get icon for service category\"\"\"\n    icons = {\n        'Sviluppo Software': 'fas fa-code',\n        'Intelligenza Artificiale': 'fas fa-brain',\n        'Consulenza IT': 'fas fa-laptop',\n        'Gestione Progetti': 'fas fa-project-diagram',\n        'Finanziamenti': 'fas fa-coins',\n        'default': 'fas fa-cog'\n    }\n    return icons.get(category, icons['default'])\n", "modifiedCode": "from flask import Blueprint, jsonify, current_app\nfrom sqlalchemy import desc\nfrom models import Service, News\nimport os\nimport json\n\npublic_api_bp = Blueprint('public_api', __name__, url_prefix='/api/public')\n\n@public_api_bp.route('/config')\ndef get_public_config():\n    \"\"\"Get public configuration for the tenant\"\"\"\n    try:\n        # Load tenant configuration from file or database\n        config_path = os.path.join(current_app.root_path, 'config', 'tenant_config.json')\n\n        # Default configuration\n        default_config = {\n            'company': {\n                'name': 'DatVinci',\n                'tagline': 'Innovazione per il futuro',\n                'description': 'Supportiamo le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all\\'avanguardia.',\n                'mission': 'Supportare le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all\\'avanguardia.',\n                'vision': 'Diventare il partner di riferimento per l\\'innovazione tecnologica in Italia.',\n                'founded': '2018',\n                'team_size': '25+ professionisti',\n                'expertise': [\n                    'Sviluppo Software',\n                    'Intelligenza Artificiale',\n                    'Consulenza IT',\n                    'Gestione Progetti Innovativi',\n                    'Supporto su Bandi e Finanziamenti'\n                ]\n            },\n            'contact': {\n                'address': 'Via dell\\'Innovazione 123, Milano',\n                'email': '<EMAIL>',\n                'phone': '+39 02 1234567',\n                'hours': 'Lun-Ven: 9:00 - 18:00',\n                'social': {\n                    'linkedin': '#',\n                    'twitter': '#',\n                    'facebook': '#'\n                }\n            },\n            'pages': {\n                'home': {\n                    'hero_title': 'Innovazione per il futuro',\n                    'hero_subtitle': 'Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all\\'avanguardia',\n                    'cta_primary': 'Scopri i nostri servizi',\n                    'cta_secondary': 'Contattaci',\n                    'services_title': 'I nostri servizi',\n                    'services_subtitle': 'Soluzioni innovative per ogni esigenza aziendale',\n                    'news_title': 'Ultime notizie',\n                    'final_cta_title': 'Pronto a innovare la tua azienda?',\n                    'final_cta_subtitle': 'Contattaci per una consulenza gratuita',\n                    'final_cta_button': 'Inizia ora'\n                },\n                'about': {\n                    'title': 'Chi Siamo',\n                    'subtitle': 'La nostra storia e i nostri valori',\n                    'content': 'Siamo un team di professionisti appassionati di tecnologia e innovazione.',\n                    'team_title': 'Il nostro team',\n                    'values_title': 'I nostri valori'\n                },\n                'services': {\n                    'title': 'I nostri servizi',\n                    'subtitle': 'Soluzioni complete per la tua azienda',\n                    'content': 'Offriamo una gamma completa di servizi per supportare la crescita della tua azienda.'\n                },\n                'contact': {\n                    'title': 'Contattaci',\n                    'subtitle': 'Siamo qui per aiutarti',\n                    'content': 'Non esitare a contattarci per qualsiasi informazione o per richiedere una consulenza gratuita.',\n                    'form_title': 'Invia un messaggio',\n                    'success_message': 'Messaggio inviato con successo! Ti contatteremo presto.',\n                    'error_message': 'Tutti i campi sono obbligatori'\n                }\n            }\n        }\n\n        # Try to load custom configuration\n        if os.path.exists(config_path):\n            with open(config_path, 'r', encoding='utf-8') as f:\n                custom_config = json.load(f)\n                # Merge with default config\n                default_config.update(custom_config)\n\n        return jsonify({\n            'success': True,\n            'data': default_config\n        })\n    except Exception as e:\n        return jsonify({\n            'success': False,\n            'error': str(e)\n        }), 500\n\n@public_api_bp.route('/services/featured')\ndef featured_services():\n    \"\"\"Get featured services for homepage\"\"\"\n    try:\n        services = Service.query.filter_by(status='active').limit(4).all()\n\n        services_data = []\n        for service in services:\n            services_data.append({\n                'id': service.id,\n                'name': service.name,\n                'description': service.description[:150] + '...' if len(service.description) > 150 else service.description,\n                'category': service.category,\n                'icon': get_service_icon(service.category)\n            })\n\n        return jsonify({\n            'success': True,\n            'data': services_data\n        })\n    except Exception as e:\n        return jsonify({\n            'success': False,\n            'error': str(e)\n        }), 500\n\n@public_api_bp.route('/news/recent')\ndef recent_news():\n    \"\"\"Get recent news for homepage\"\"\"\n    try:\n        news = News.query.filter_by(is_published=True).order_by(desc(News.created_at)).limit(3).all()\n\n        news_data = []\n        for item in news:\n            news_data.append({\n                'id': item.id,\n                'title': item.title,\n                'excerpt': item.content[:200] + '...' if len(item.content) > 200 else item.content,\n                'created_at': item.created_at.isoformat()\n            })\n\n        return jsonify({\n            'success': True,\n            'data': news_data\n        })\n    except Exception as e:\n        return jsonify({\n            'success': False,\n            'error': str(e)\n        }), 500\n\n@public_api_bp.route('/services')\ndef all_services():\n    \"\"\"Get all services\"\"\"\n    try:\n        services = Service.query.filter_by(status='active').order_by(Service.name).all()\n\n        services_data = []\n        for service in services:\n            services_data.append({\n                'id': service.id,\n                'name': service.name,\n                'description': service.description,\n                'category': service.category,\n                'icon': get_service_icon(service.category)\n            })\n\n        return jsonify({\n            'success': True,\n            'data': services_data\n        })\n    except Exception as e:\n        return jsonify({\n            'success': False,\n            'error': str(e)\n        }), 500\n\n@public_api_bp.route('/services/<int:service_id>')\ndef service_detail(service_id):\n    \"\"\"Get service detail\"\"\"\n    try:\n        service = Service.query.get_or_404(service_id)\n\n        # Get related services\n        related_services = Service.query.filter(\n            Service.category == service.category,\n            Service.id != service.id,\n            Service.status == 'active'\n        ).limit(3).all()\n\n        related_data = []\n        for related in related_services:\n            related_data.append({\n                'id': related.id,\n                'name': related.name,\n                'description': related.description[:100] + '...' if len(related.description) > 100 else related.description,\n                'category': related.category\n            })\n\n        service_data = {\n            'id': service.id,\n            'name': service.name,\n            'description': service.description,\n            'category': service.category,\n            'icon': get_service_icon(service.category),\n            'related_services': related_data\n        }\n\n        return jsonify({\n            'success': True,\n            'data': service_data\n        })\n    except Exception as e:\n        return jsonify({\n            'success': False,\n            'error': str(e)\n        }), 500\n\ndef get_service_icon(category):\n    \"\"\"Get icon for service category\"\"\"\n    icons = {\n        'Sviluppo Software': 'fas fa-code',\n        'Intelligenza Artificiale': 'fas fa-brain',\n        'Consulenza IT': 'fas fa-laptop',\n        'Gestione Progetti': 'fas fa-project-diagram',\n        'Finanziamenti': 'fas fa-coins',\n        'default': 'fas fa-cog'\n    }\n    return icons.get(category, icons['default'])\n"}