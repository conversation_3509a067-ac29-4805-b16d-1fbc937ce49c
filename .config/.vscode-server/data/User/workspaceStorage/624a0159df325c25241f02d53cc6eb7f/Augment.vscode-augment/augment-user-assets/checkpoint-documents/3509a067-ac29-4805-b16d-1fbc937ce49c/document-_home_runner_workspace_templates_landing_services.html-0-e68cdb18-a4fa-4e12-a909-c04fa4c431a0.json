{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/landing/services.html"}, "originalCode": "{% extends 'base.html' %}\n\n{% block title %}Servizi - DatPortal | DatVinci{% endblock %}\n\n{% block public_content %}\n<section class=\"bg-gradient-to-r from-primary-600 to-secondary-500 text-white py-12\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center\">\n            <h1 class=\"text-3xl font-bold mb-4\">I <PERSON><PERSON>ri <PERSON></h1>\n            <p class=\"text-xl max-w-3xl mx-auto\">\n                Esplora la gamma completa di servizi che DatVinci offre per supportare la crescita e l'innovazione della tua azienda.\n            </p>\n        </div>\n    </div>\n</section>\n\n<section class=\"py-12 bg-white dark:bg-gray-800\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <!-- Filtri per categoria -->\n        {% if categories %}\n        <div class=\"mb-8\">\n            <div class=\"flex flex-wrap items-center justify-center space-x-2 space-y-2 md:space-y-0 md:justify-start\">\n                <a href=\"{{ url_for('landing.services') }}\" class=\"inline-block px-4 py-2 rounded-full {% if not current_category %}bg-primary-500 text-white{% else %}bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600{% endif %} transition duration-300 mt-2\">\n                    Tutti\n                </a>\n                {% for category in categories %}\n                <a href=\"{{ url_for('landing.services', category=category) }}\" class=\"inline-block px-4 py-2 rounded-full {% if current_category == category %}bg-primary-500 text-white{% else %}bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600{% endif %} transition duration-300 mt-2\">\n                    {{ category }}\n                </a>\n                {% endfor %}\n            </div>\n        </div>\n        {% endif %}\n\n        <!-- Servizi per categoria -->\n        {% if services_by_category %}\n            {% for category, category_services in services_by_category.items() %}\n            <div class=\"mb-12\">\n                <h2 class=\"text-2xl font-bold text-gray-900 dark:text-white mb-6 pb-2 border-b border-gray-200 dark:border-gray-700\">\n                    {{ category if category else 'Altri Servizi' }}\n                </h2>\n                \n                <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n                    {% for service in category_services %}\n                    <div class=\"bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden shadow-md transition-all duration-300 hover:shadow-lg\">\n                        <div class=\"p-6\">\n                            <div class=\"text-primary-500 mb-4\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                                </svg>\n                            </div>\n                            <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">{{ service.name }}</h3>\n                            <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                                {{ service.description|truncate(120) if service.description else 'Nessuna descrizione disponibile.' }}\n                            </p>\n                            <div class=\"mt-4\">\n                                <a href=\"{{ url_for('landing.service_detail', service_id=service.id) }}\" class=\"inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-500 hover:bg-primary-600 transition duration-300\">\n                                    Dettagli\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 ml-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\" />\n                                    </svg>\n                                </a>\n                            </div>\n                        </div>\n                    </div>\n                    {% endfor %}\n                </div>\n            </div>\n            {% endfor %}\n        {% else %}\n        <div class=\"text-center py-12\">\n            <div class=\"text-gray-400 dark:text-gray-500 mb-4\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-16 w-16 mx-auto\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n            </div>\n            <h3 class=\"text-xl font-semibold text-gray-700 dark:text-gray-300\">Nessun servizio disponibile</h3>\n            <p class=\"text-gray-500 dark:text-gray-400 mt-2\">\n                Al momento non ci sono servizi disponibili nella categoria selezionata.\n            </p>\n        </div>\n        {% endif %}\n    </div>\n</section>\n\n<!-- CTA Section -->\n<section class=\"py-12 bg-gray-50 dark:bg-gray-700\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"lg:flex lg:items-center lg:justify-between\">\n            <div class=\"lg:w-0 lg:flex-1\">\n                <h2 class=\"text-2xl font-extrabold tracking-tight text-gray-900 dark:text-white sm:text-3xl\">\n                    Hai bisogno di un servizio personalizzato?\n                </h2>\n                <p class=\"mt-3 max-w-3xl text-lg text-gray-600 dark:text-gray-300\">\n                    Contattaci per discutere le tue esigenze specifiche e scoprire come possiamo aiutarti a raggiungere i tuoi obiettivi aziendali.\n                </p>\n            </div>\n            <div class=\"mt-8 lg:mt-0 lg:ml-8\">\n                <div class=\"flex space-x-4\">\n                    <a href=\"{{ url_for('landing.contact') }}\" class=\"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 transition duration-300\">\n                        Contattaci\n                    </a>\n                </div>\n            </div>\n        </div>\n    </div>\n</section>\n{% endblock %}", "modifiedCode": "{% extends 'base.html' %}\n\n{% block title %}Servizi - DatPortal | DatVinci{% endblock %}\n\n{% block public_content %}\n<section class=\"bg-gradient-to-r from-primary-600 to-secondary-500 text-white py-12\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"text-center\">\n            <h1 class=\"text-3xl font-bold mb-4\">I <PERSON><PERSON>ri <PERSON></h1>\n            <p class=\"text-xl max-w-3xl mx-auto\">\n                Esplora la gamma completa di servizi che DatVinci offre per supportare la crescita e l'innovazione della tua azienda.\n            </p>\n        </div>\n    </div>\n</section>\n\n<section class=\"py-12 bg-white dark:bg-gray-800\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <!-- Filtri per categoria -->\n        {% if categories %}\n        <div class=\"mb-8\">\n            <div class=\"flex flex-wrap items-center justify-center space-x-2 space-y-2 md:space-y-0 md:justify-start\">\n                <a href=\"{{ url_for('landing.services') }}\" class=\"inline-block px-4 py-2 rounded-full {% if not current_category %}bg-primary-500 text-white{% else %}bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600{% endif %} transition duration-300 mt-2\">\n                    Tutti\n                </a>\n                {% for category in categories %}\n                <a href=\"{{ url_for('landing.services', category=category) }}\" class=\"inline-block px-4 py-2 rounded-full {% if current_category == category %}bg-primary-500 text-white{% else %}bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600{% endif %} transition duration-300 mt-2\">\n                    {{ category }}\n                </a>\n                {% endfor %}\n            </div>\n        </div>\n        {% endif %}\n\n        <!-- Servizi per categoria -->\n        {% if services_by_category %}\n            {% for category, category_services in services_by_category.items() %}\n            <div class=\"mb-12\">\n                <h2 class=\"text-2xl font-bold text-gray-900 dark:text-white mb-6 pb-2 border-b border-gray-200 dark:border-gray-700\">\n                    {{ category if category else 'Altri Servizi' }}\n                </h2>\n                \n                <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n                    {% for service in category_services %}\n                    <div class=\"bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden shadow-md transition-all duration-300 hover:shadow-lg\">\n                        <div class=\"p-6\">\n                            <div class=\"text-primary-500 mb-4\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                                </svg>\n                            </div>\n                            <h3 class=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">{{ service.name }}</h3>\n                            <p class=\"text-gray-600 dark:text-gray-300 mb-4\">\n                                {{ service.description|truncate(120) if service.description else 'Nessuna descrizione disponibile.' }}\n                            </p>\n                            <div class=\"mt-4\">\n                                <a href=\"{{ url_for('landing.service_detail', service_id=service.id) }}\" class=\"inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-500 hover:bg-primary-600 transition duration-300\">\n                                    Dettagli\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 ml-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\" />\n                                    </svg>\n                                </a>\n                            </div>\n                        </div>\n                    </div>\n                    {% endfor %}\n                </div>\n            </div>\n            {% endfor %}\n        {% else %}\n        <div class=\"text-center py-12\">\n            <div class=\"text-gray-400 dark:text-gray-500 mb-4\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-16 w-16 mx-auto\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n            </div>\n            <h3 class=\"text-xl font-semibold text-gray-700 dark:text-gray-300\">Nessun servizio disponibile</h3>\n            <p class=\"text-gray-500 dark:text-gray-400 mt-2\">\n                Al momento non ci sono servizi disponibili nella categoria selezionata.\n            </p>\n        </div>\n        {% endif %}\n    </div>\n</section>\n\n<!-- CTA Section -->\n<section class=\"py-12 bg-gray-50 dark:bg-gray-700\">\n    <div class=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"lg:flex lg:items-center lg:justify-between\">\n            <div class=\"lg:w-0 lg:flex-1\">\n                <h2 class=\"text-2xl font-extrabold tracking-tight text-gray-900 dark:text-white sm:text-3xl\">\n                    Hai bisogno di un servizio personalizzato?\n                </h2>\n                <p class=\"mt-3 max-w-3xl text-lg text-gray-600 dark:text-gray-300\">\n                    Contattaci per discutere le tue esigenze specifiche e scoprire come possiamo aiutarti a raggiungere i tuoi obiettivi aziendali.\n                </p>\n            </div>\n            <div class=\"mt-8 lg:mt-0 lg:ml-8\">\n                <div class=\"flex space-x-4\">\n                    <a href=\"{{ url_for('landing.contact') }}\" class=\"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 transition duration-300\">\n                        Contattaci\n                    </a>\n                </div>\n            </div>\n        </div>\n    </div>\n</section>\n{% endblock %}"}