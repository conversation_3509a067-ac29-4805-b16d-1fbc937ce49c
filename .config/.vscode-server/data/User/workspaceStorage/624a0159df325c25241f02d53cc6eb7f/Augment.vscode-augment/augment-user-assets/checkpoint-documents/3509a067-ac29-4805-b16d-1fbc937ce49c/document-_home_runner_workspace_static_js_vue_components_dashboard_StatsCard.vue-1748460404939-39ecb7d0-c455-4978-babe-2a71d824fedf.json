{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/dashboard/StatsCard.vue"}, "modifiedCode": "<template>\n  <div class=\"card-brand p-6 hover:shadow-brand-lg transition-all duration-300 group\">\n    <div class=\"flex items-center\">\n      <!-- Icon -->\n      <div \n        class=\"flex-shrink-0 rounded-md p-3 transition-transform duration-300 group-hover:scale-110\"\n        :class=\"iconBgClass\"\n      >\n        <i :class=\"[icon, 'h-6 w-6 text-white']\"></i>\n      </div>\n      \n      <!-- Content -->\n      <div class=\"ml-5 w-0 flex-1\">\n        <dl>\n          <dt class=\"text-sm font-medium text-brand-text-secondary truncate\">\n            {{ title }}\n          </dt>\n          <dd>\n            <div class=\"text-lg font-medium text-brand-text-primary\">\n              {{ formattedValue }}\n            </div>\n          </dd>\n        </dl>\n      </div>\n    </div>\n    \n    <!-- Footer with trend and link -->\n    <div class=\"mt-4 flex items-center justify-between\">\n      <!-- Trend -->\n      <div v-if=\"trend\" class=\"flex items-center text-sm\">\n        <i \n          :class=\"[\n            trend.direction === 'up' ? 'fas fa-arrow-up text-brand-success-500' : \n            trend.direction === 'down' ? 'fas fa-arrow-down text-brand-error-500' : \n            'fas fa-minus text-brand-text-tertiary',\n            'h-3 w-3 mr-1'\n          ]\"\n        ></i>\n        <span \n          :class=\"[\n            trend.direction === 'up' ? 'text-brand-success-600' : \n            trend.direction === 'down' ? 'text-brand-error-600' : \n            'text-brand-text-tertiary'\n          ]\"\n        >\n          {{ trend.value }}{{ trend.unit || '%' }}\n        </span>\n        <span class=\"text-brand-text-tertiary ml-1\">\n          {{ trend.period || 'vs periodo precedente' }}\n        </span>\n      </div>\n      \n      <!-- Link -->\n      <div v-if=\"link\">\n        <router-link\n          :to=\"link\"\n          class=\"text-sm font-medium text-brand-primary-600 dark:text-brand-primary-400 hover:text-brand-primary-500 dark:hover:text-brand-primary-300 transition-colors\"\n        >\n          Vedi tutti\n          <i class=\"fas fa-arrow-right ml-1 h-3 w-3\"></i>\n        </router-link>\n      </div>\n    </div>\n    \n    <!-- Loading overlay -->\n    <div \n      v-if=\"loading\"\n      class=\"absolute inset-0 bg-white dark:bg-gray-800 bg-opacity-75 flex items-center justify-center rounded-lg\"\n    >\n      <div class=\"animate-spin rounded-full h-6 w-6 border-b-2 border-brand-primary-500\"></div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\n\n// Props\nconst props = defineProps({\n  title: {\n    type: String,\n    required: true\n  },\n  value: {\n    type: [String, Number],\n    required: true\n  },\n  icon: {\n    type: String,\n    required: true\n  },\n  color: {\n    type: String,\n    default: 'primary',\n    validator: (value) => ['primary', 'secondary', 'success', 'warning', 'error'].includes(value)\n  },\n  trend: {\n    type: Object,\n    default: null\n    // Expected format: { direction: 'up'|'down'|'neutral', value: number, unit: '%', period: 'vs last month' }\n  },\n  link: {\n    type: String,\n    default: null\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  },\n  format: {\n    type: String,\n    default: 'number',\n    validator: (value) => ['number', 'currency', 'percentage'].includes(value)\n  }\n})\n\n// Computed\nconst iconBgClass = computed(() => {\n  const classes = {\n    primary: 'bg-brand-primary-500',\n    secondary: 'bg-brand-secondary-500',\n    success: 'bg-brand-success-500',\n    warning: 'bg-brand-warning-500',\n    error: 'bg-brand-error-500'\n  }\n  return classes[props.color] || classes.primary\n})\n\nconst formattedValue = computed(() => {\n  if (typeof props.value === 'string') {\n    return props.value\n  }\n  \n  switch (props.format) {\n    case 'currency':\n      return new Intl.NumberFormat('it-IT', {\n        style: 'currency',\n        currency: 'EUR'\n      }).format(props.value)\n    \n    case 'percentage':\n      return `${props.value}%`\n    \n    case 'number':\n    default:\n      return new Intl.NumberFormat('it-IT').format(props.value)\n  }\n})\n</script>\n\n<style scoped>\n/* Card hover effects */\n.group:hover .card-brand {\n  transform: translateY(-2px);\n}\n\n/* Icon animation */\n.group:hover .flex-shrink-0 {\n  transform: scale(1.1);\n}\n\n/* Loading state */\n.relative {\n  position: relative;\n}\n\n/* Trend animations */\n.fas.fa-arrow-up,\n.fas.fa-arrow-down {\n  animation: bounce 2s infinite;\n}\n\n@keyframes bounce {\n  0%, 20%, 50%, 80%, 100% {\n    transform: translateY(0);\n  }\n  40% {\n    transform: translateY(-3px);\n  }\n  60% {\n    transform: translateY(-1px);\n  }\n}\n\n/* Responsive adjustments */\n@media (max-width: 640px) {\n  .text-lg {\n    font-size: 1rem;\n  }\n  \n  .p-6 {\n    padding: 1rem;\n  }\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .transition-all,\n  .transition-transform,\n  .transition-colors,\n  .group:hover .card-brand,\n  .group:hover .flex-shrink-0 {\n    transition: none;\n    transform: none;\n  }\n  \n  .fas.fa-arrow-up,\n  .fas.fa-arrow-down {\n    animation: none;\n  }\n}\n\n/* Focus styles */\n.router-link:focus {\n  outline: 2px solid var(--brand-primary-500);\n  outline-offset: 2px;\n  border-radius: var(--brand-radius-sm);\n}\n\n/* High contrast mode */\n@media (prefers-contrast: high) {\n  .card-brand {\n    border: 2px solid var(--brand-border-primary);\n  }\n}\n</style>\n"}