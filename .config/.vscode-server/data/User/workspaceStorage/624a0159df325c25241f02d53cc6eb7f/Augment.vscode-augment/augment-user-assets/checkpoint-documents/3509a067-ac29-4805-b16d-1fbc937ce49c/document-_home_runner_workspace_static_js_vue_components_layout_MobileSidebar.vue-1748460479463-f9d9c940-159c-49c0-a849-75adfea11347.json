{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/layout/MobileSidebar.vue"}, "modifiedCode": "<template>\n  <!-- Mobile Sidebar Overlay -->\n  <div \n    v-if=\"appStore.isMobile\"\n    class=\"md:hidden\"\n  >\n    <!-- Backdrop -->\n    <div \n      v-show=\"appStore.sidebarOpen\"\n      class=\"fixed inset-0 bg-gray-600 bg-opacity-75 z-40 transition-opacity duration-300\"\n      @click=\"appStore.setSidebarOpen(false)\"\n    ></div>\n    \n    <!-- Sidebar -->\n    <div \n      class=\"fixed inset-y-0 left-0 flex flex-col w-64 bg-brand-primary-700 dark:bg-gray-800 z-50 transform transition-transform duration-300\"\n      :class=\"[\n        appStore.sidebarOpen ? 'translate-x-0' : '-translate-x-full'\n      ]\"\n    >\n      <!-- Header -->\n      <div class=\"flex items-center justify-between p-4 border-b border-brand-primary-600 dark:border-gray-700\">\n        <div class=\"flex items-center\">\n          <img \n            :src=\"brandStore.currentLogo\" \n            alt=\"Logo\" \n            class=\"h-8 w-auto rounded-lg\"\n          >\n          <span class=\"ml-2 text-white font-bold text-lg\">\n            {{ brandStore.brandConfig.name }}\n          </span>\n        </div>\n        <button\n          @click=\"appStore.setSidebarOpen(false)\"\n          class=\"text-white hover:text-brand-primary-200 transition-colors\"\n        >\n          <i class=\"fas fa-times h-6 w-6\"></i>\n        </button>\n      </div>\n      \n      <!-- Navigation -->\n      <nav class=\"flex-1 px-2 py-4 space-y-1 overflow-y-auto\">\n        <!-- Dashboard -->\n        <MobileSidebarItem\n          to=\"/\"\n          icon=\"fas fa-home\"\n          label=\"Dashboard\"\n          @click=\"closeSidebar\"\n        />\n\n        <!-- Personnel -->\n        <MobileSidebarItem\n          to=\"/personnel\"\n          icon=\"fas fa-users\"\n          label=\"Personale\"\n          @click=\"closeSidebar\"\n        />\n\n        <!-- Projects -->\n        <MobileSidebarItem\n          to=\"/projects\"\n          icon=\"fas fa-project-diagram\"\n          label=\"Progetti\"\n          @click=\"closeSidebar\"\n        />\n\n        <!-- CRM -->\n        <MobileSidebarItem\n          to=\"/crm\"\n          icon=\"fas fa-handshake\"\n          label=\"CRM\"\n          @click=\"closeSidebar\"\n        />\n\n        <!-- Performance -->\n        <MobileSidebarItem\n          to=\"/performance\"\n          icon=\"fas fa-chart-line\"\n          label=\"Performance\"\n          @click=\"closeSidebar\"\n        />\n\n        <!-- Admin (only for admins) -->\n        <MobileSidebarItem\n          v-if=\"authStore.isAdmin\"\n          to=\"/admin\"\n          icon=\"fas fa-cog\"\n          label=\"Admin\"\n          @click=\"closeSidebar\"\n        />\n      </nav>\n      \n      <!-- User Section -->\n      <div class=\"border-t border-brand-primary-600 dark:border-gray-700 p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"h-10 w-10 rounded-full bg-brand-primary-500 flex items-center justify-center text-white font-medium\">\n            {{ authStore.userInitials }}\n          </div>\n          <div class=\"ml-3\">\n            <p class=\"text-sm font-medium text-white\">\n              {{ authStore.userFullName }}\n            </p>\n            <button\n              @click=\"logout\"\n              class=\"text-xs text-brand-primary-200 hover:text-white transition-colors\"\n            >\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { useAppStore } from '../../stores/app.js'\nimport { useAuthStore } from '../../stores/auth.js'\nimport { useBrandStore } from '../../stores/brand.js'\nimport MobileSidebarItem from './MobileSidebarItem.vue'\n\n// Stores\nconst appStore = useAppStore()\nconst authStore = useAuthStore()\nconst brandStore = useBrandStore()\n\n// Methods\nfunction closeSidebar() {\n  appStore.setSidebarOpen(false)\n}\n\nfunction logout() {\n  authStore.logout()\n}\n</script>\n\n<style scoped>\n/* Sidebar animations */\n.transform.transition-transform {\n  transition: transform 0.3s ease-in-out;\n}\n\n/* Backdrop animation */\n.transition-opacity {\n  transition: opacity 0.3s ease-in-out;\n}\n\n/* Custom scrollbar */\n.overflow-y-auto::-webkit-scrollbar {\n  width: 4px;\n}\n\n.overflow-y-auto::-webkit-scrollbar-track {\n  background: rgba(255, 255, 255, 0.1);\n}\n\n.overflow-y-auto::-webkit-scrollbar-thumb {\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 2px;\n}\n</style>\n"}