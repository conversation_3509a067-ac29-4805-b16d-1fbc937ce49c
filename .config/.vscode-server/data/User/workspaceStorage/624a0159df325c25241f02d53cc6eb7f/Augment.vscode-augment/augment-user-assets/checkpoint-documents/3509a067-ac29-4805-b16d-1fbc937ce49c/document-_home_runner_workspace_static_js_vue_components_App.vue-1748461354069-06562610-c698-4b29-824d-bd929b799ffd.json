{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/App.vue"}, "originalCode": "<template>\n  <div\n    id=\"app\"\n    class=\"min-h-screen bg-brand-bg-secondary dark:bg-gray-900 text-brand-text-primary transition-brand\"\n    :class=\"{ 'dark': appStore.darkMode }\"\n  >\n    <!-- Loading Overlay -->\n    <LoadingOverlay v-if=\"appStore.isLoading\" :message=\"appStore.loadingMessage\" />\n\n    <!-- Private Layout (Authenticated) -->\n    <div v-if=\"isPrivateRoute\" class=\"flex h-screen overflow-hidden\">\n      <!-- Sidebar -->\n      <Sidebar />\n\n      <!-- Main Content Area -->\n      <div class=\"flex flex-col flex-1 overflow-y-auto\">\n        <!-- Top Navigation -->\n        <TopNavigation />\n\n        <!-- Page Content -->\n        <main class=\"flex-grow p-4 md:p-6 lg:p-8\" id=\"main-content\">\n          <!-- Breadcrumbs -->\n          <Breadcrumbs v-if=\"appStore.breadcrumbs.length > 0\" :items=\"appStore.breadcrumbs\" />\n\n          <!-- Router View with Transitions -->\n          <router-view v-slot=\"{ Component, route }\">\n            <transition\n              :name=\"getTransitionName(route)\"\n              mode=\"out-in\"\n              @enter=\"onPageEnter\"\n              @leave=\"onPageLeave\"\n            >\n              <component :is=\"Component\" :key=\"route.path\" />\n            </transition>\n          </router-view>\n        </main>\n\n        <!-- Footer -->\n        <AppFooter />\n      </div>\n    </div>\n\n    <!-- Public Layout (Not Authenticated) -->\n    <div v-else class=\"min-h-screen bg-brand-bg-primary\">\n      <!-- Router View for Public Pages -->\n      <router-view v-slot=\"{ Component, route }\">\n        <transition\n          :name=\"getTransitionName(route)\"\n          mode=\"out-in\"\n          @enter=\"onPageEnter\"\n          @leave=\"onPageLeave\"\n        >\n          <component :is=\"Component\" :key=\"route.path\" />\n        </transition>\n      </router-view>\n    </div>\n\n    <!-- Global Notifications -->\n    <NotificationContainer />\n\n    <!-- Global Modals -->\n    <ModalContainer />\n\n    <!-- Offline Indicator -->\n    <OfflineIndicator v-if=\"!appStore.isOnline\" />\n  </div>\n</template>\n\n<script setup>\nimport { onMounted, watch, nextTick } from 'vue'\nimport { useRouter, useRoute } from 'vue-router'\nimport { useAuthStore } from '../stores/auth.js'\nimport { useAppStore } from '../stores/app.js'\nimport { useBrandStore } from '../stores/brand.js'\n\n// Components\nimport Sidebar from './layout/Sidebar.vue'\nimport TopNavigation from './layout/TopNavigation.vue'\nimport AppFooter from './layout/AppFooter.vue'\nimport Breadcrumbs from './layout/Breadcrumbs.vue'\nimport LoadingOverlay from './ui/LoadingOverlay.vue'\nimport NotificationContainer from './ui/NotificationContainer.vue'\nimport ModalContainer from './ui/ModalContainer.vue'\nimport OfflineIndicator from './ui/OfflineIndicator.vue'\n\n// Stores\nconst authStore = useAuthStore()\nconst appStore = useAppStore()\nconst brandStore = useBrandStore()\nconst router = useRouter()\nconst route = useRoute()\n\n// Computed\nconst isPrivateRoute = computed(() => {\n  return route.meta?.layout === 'private' && authStore.isAuthenticated\n})\n\n// Page transition logic\nfunction getTransitionName(route) {\n  // Different transitions based on route depth or type\n  if (route.meta?.transition) {\n    return route.meta.transition\n  }\n\n  // Public pages use fade, private pages use slide\n  if (route.meta?.layout === 'public') {\n    return 'page-fade'\n  }\n\n  // Default transitions\n  const depth = route.path.split('/').length\n  if (depth <= 2) {\n    return 'page-slide'\n  } else {\n    return 'page-fade'\n  }\n}\n\nfunction onPageEnter() {\n  // Page enter animation complete\n  nextTick(() => {\n    // Scroll to top\n    const mainContent = document.getElementById('main-content')\n    if (mainContent) {\n      mainContent.scrollTop = 0\n    }\n  })\n}\n\nfunction onPageLeave() {\n  // Page leave animation starting\n}\n\n// Watch route changes to update app state\nwatch(route, (newRoute) => {\n  // Update page info\n  appStore.setCurrentPage(\n    newRoute.name,\n    newRoute.meta?.title || 'DatPortal',\n    newRoute.meta?.breadcrumbs || []\n  )\n\n  // Update last activity for authenticated users\n  if (authStore.isAuthenticated) {\n    authStore.updateLastActivity()\n  }\n}, { immediate: true })\n\n// Session check interval\nlet sessionCheckInterval = null\n\nfunction startSessionCheck() {\n  // Check session every 5 minutes\n  sessionCheckInterval = setInterval(async () => {\n    if (authStore.isAuthenticated) {\n      const isValid = await authStore.checkSession()\n      if (!isValid) {\n        appStore.showNotification(\n          'Sessione scaduta. Effettua nuovamente il login.',\n          'warning',\n          0\n        )\n        setTimeout(() => {\n          authStore.logout()\n        }, 3000)\n      }\n    }\n  }, 5 * 60 * 1000) // 5 minutes\n}\n\nfunction stopSessionCheck() {\n  if (sessionCheckInterval) {\n    clearInterval(sessionCheckInterval)\n    sessionCheckInterval = null\n  }\n}\n\n// Lifecycle\nonMounted(async () => {\n  try {\n    // Initialize brand system\n    await brandStore.loadBrandConfig()\n\n    // Load user data if authenticated\n    if (window.APP_CONFIG.isAuthenticated) {\n      await authStore.loadCurrentUser()\n      startSessionCheck()\n    } else {\n      // Redirect to login if not authenticated\n      window.location.href = '/auth/login'\n      return\n    }\n\n    // Mark app as initialized\n    appStore.setInitialized(true)\n\n    // Show welcome notification\n    if (authStore.userFullName) {\n      appStore.showNotification(\n        `Benvenuto, ${authStore.userFullName}!`,\n        'success',\n        3000\n      )\n    }\n\n  } catch (error) {\n    console.error('App initialization failed:', error)\n    appStore.showNotification(\n      'Errore durante l\\'inizializzazione dell\\'applicazione',\n      'error',\n      0\n    )\n  }\n})\n\n// Cleanup on unmount\nonUnmounted(() => {\n  stopSessionCheck()\n})\n\n// Handle browser back/forward\nwindow.addEventListener('popstate', () => {\n  authStore.updateLastActivity()\n})\n\n// Handle visibility change (tab focus/blur)\ndocument.addEventListener('visibilitychange', () => {\n  if (!document.hidden && authStore.isAuthenticated) {\n    // Tab became visible, check session\n    authStore.checkSession()\n    authStore.updateLastActivity()\n  }\n})\n</script>\n\n<style scoped>\n/* Page Transitions */\n.page-slide-enter-active,\n.page-slide-leave-active {\n  transition: all var(--brand-transition-normal);\n}\n\n.page-slide-enter-from {\n  opacity: 0;\n  transform: translateX(20px);\n}\n\n.page-slide-leave-to {\n  opacity: 0;\n  transform: translateX(-20px);\n}\n\n.page-fade-enter-active,\n.page-fade-leave-active {\n  transition: opacity var(--brand-transition-fast);\n}\n\n.page-fade-enter-from,\n.page-fade-leave-to {\n  opacity: 0;\n}\n\n/* Loading state */\n.page-loading {\n  pointer-events: none;\n}\n\n.page-loading * {\n  cursor: wait !important;\n}\n\n/* Smooth scrolling */\n#main-content {\n  scroll-behavior: smooth;\n}\n\n/* Custom scrollbar */\n#main-content::-webkit-scrollbar {\n  width: 6px;\n}\n\n#main-content::-webkit-scrollbar-track {\n  background: var(--brand-bg-tertiary);\n}\n\n#main-content::-webkit-scrollbar-thumb {\n  background: var(--brand-border-secondary);\n  border-radius: var(--brand-radius-full);\n}\n\n#main-content::-webkit-scrollbar-thumb:hover {\n  background: var(--brand-primary-400);\n}\n\n/* Focus management */\n.router-link-active {\n  @apply text-brand-primary-600 dark:text-brand-primary-400;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .page-slide-enter-active,\n  .page-slide-leave-active,\n  .page-fade-enter-active,\n  .page-fade-leave-active {\n    transition: none;\n  }\n\n  .animate-spin {\n    animation: none;\n  }\n}\n\n/* Print styles */\n@media print {\n  .sidebar,\n  .top-navigation,\n  .app-footer,\n  .notification-container,\n  .modal-container {\n    display: none !important;\n  }\n\n  #main-content {\n    margin: 0 !important;\n    padding: 0 !important;\n  }\n}\n</style>\n", "modifiedCode": "<template>\n  <div\n    id=\"app\"\n    class=\"min-h-screen bg-brand-bg-secondary dark:bg-gray-900 text-brand-text-primary transition-brand\"\n    :class=\"{ 'dark': appStore.darkMode }\"\n  >\n    <!-- Loading Overlay -->\n    <LoadingOverlay v-if=\"appStore.isLoading\" :message=\"appStore.loadingMessage\" />\n\n    <!-- Private Layout (Authenticated) -->\n    <div v-if=\"isPrivateRoute\" class=\"flex h-screen overflow-hidden\">\n      <!-- Sidebar -->\n      <Sidebar />\n\n      <!-- Main Content Area -->\n      <div class=\"flex flex-col flex-1 overflow-y-auto\">\n        <!-- Top Navigation -->\n        <TopNavigation />\n\n        <!-- Page Content -->\n        <main class=\"flex-grow p-4 md:p-6 lg:p-8\" id=\"main-content\">\n          <!-- Breadcrumbs -->\n          <Breadcrumbs v-if=\"appStore.breadcrumbs.length > 0\" :items=\"appStore.breadcrumbs\" />\n\n          <!-- Router View with Transitions -->\n          <router-view v-slot=\"{ Component, route }\">\n            <transition\n              :name=\"getTransitionName(route)\"\n              mode=\"out-in\"\n              @enter=\"onPageEnter\"\n              @leave=\"onPageLeave\"\n            >\n              <component :is=\"Component\" :key=\"route.path\" />\n            </transition>\n          </router-view>\n        </main>\n\n        <!-- Footer -->\n        <AppFooter />\n      </div>\n    </div>\n\n    <!-- Public Layout (Not Authenticated) -->\n    <div v-else class=\"min-h-screen bg-brand-bg-primary\">\n      <!-- Router View for Public Pages -->\n      <router-view v-slot=\"{ Component, route }\">\n        <transition\n          :name=\"getTransitionName(route)\"\n          mode=\"out-in\"\n          @enter=\"onPageEnter\"\n          @leave=\"onPageLeave\"\n        >\n          <component :is=\"Component\" :key=\"route.path\" />\n        </transition>\n      </router-view>\n    </div>\n\n    <!-- Global Notifications -->\n    <NotificationContainer />\n\n    <!-- Global Modals -->\n    <ModalContainer />\n\n    <!-- Offline Indicator -->\n    <OfflineIndicator v-if=\"!appStore.isOnline\" />\n  </div>\n</template>\n\n<script setup>\nimport { onMounted, watch, nextTick } from 'vue'\nimport { useRouter, useRoute } from 'vue-router'\nimport { useAuthStore } from '../stores/auth.js'\nimport { useAppStore } from '../stores/app.js'\nimport { useBrandStore } from '../stores/brand.js'\nimport { useTenantStore } from '../stores/tenant.js'\n\n// Components\nimport Sidebar from './layout/Sidebar.vue'\nimport TopNavigation from './layout/TopNavigation.vue'\nimport AppFooter from './layout/AppFooter.vue'\nimport Breadcrumbs from './layout/Breadcrumbs.vue'\nimport LoadingOverlay from './ui/LoadingOverlay.vue'\nimport NotificationContainer from './ui/NotificationContainer.vue'\nimport ModalContainer from './ui/ModalContainer.vue'\nimport OfflineIndicator from './ui/OfflineIndicator.vue'\n\n// Stores\nconst authStore = useAuthStore()\nconst appStore = useAppStore()\nconst brandStore = useBrandStore()\nconst router = useRouter()\nconst route = useRoute()\n\n// Computed\nconst isPrivateRoute = computed(() => {\n  return route.meta?.layout === 'private' && authStore.isAuthenticated\n})\n\n// Page transition logic\nfunction getTransitionName(route) {\n  // Different transitions based on route depth or type\n  if (route.meta?.transition) {\n    return route.meta.transition\n  }\n\n  // Public pages use fade, private pages use slide\n  if (route.meta?.layout === 'public') {\n    return 'page-fade'\n  }\n\n  // Default transitions\n  const depth = route.path.split('/').length\n  if (depth <= 2) {\n    return 'page-slide'\n  } else {\n    return 'page-fade'\n  }\n}\n\nfunction onPageEnter() {\n  // Page enter animation complete\n  nextTick(() => {\n    // Scroll to top\n    const mainContent = document.getElementById('main-content')\n    if (mainContent) {\n      mainContent.scrollTop = 0\n    }\n  })\n}\n\nfunction onPageLeave() {\n  // Page leave animation starting\n}\n\n// Watch route changes to update app state\nwatch(route, (newRoute) => {\n  // Update page info\n  appStore.setCurrentPage(\n    newRoute.name,\n    newRoute.meta?.title || 'DatPortal',\n    newRoute.meta?.breadcrumbs || []\n  )\n\n  // Update last activity for authenticated users\n  if (authStore.isAuthenticated) {\n    authStore.updateLastActivity()\n  }\n}, { immediate: true })\n\n// Session check interval\nlet sessionCheckInterval = null\n\nfunction startSessionCheck() {\n  // Check session every 5 minutes\n  sessionCheckInterval = setInterval(async () => {\n    if (authStore.isAuthenticated) {\n      const isValid = await authStore.checkSession()\n      if (!isValid) {\n        appStore.showNotification(\n          'Sessione scaduta. Effettua nuovamente il login.',\n          'warning',\n          0\n        )\n        setTimeout(() => {\n          authStore.logout()\n        }, 3000)\n      }\n    }\n  }, 5 * 60 * 1000) // 5 minutes\n}\n\nfunction stopSessionCheck() {\n  if (sessionCheckInterval) {\n    clearInterval(sessionCheckInterval)\n    sessionCheckInterval = null\n  }\n}\n\n// Lifecycle\nonMounted(async () => {\n  try {\n    // Initialize brand system\n    await brandStore.loadBrandConfig()\n\n    // Load user data if authenticated\n    if (window.APP_CONFIG.isAuthenticated) {\n      await authStore.loadCurrentUser()\n      startSessionCheck()\n    } else {\n      // Redirect to login if not authenticated\n      window.location.href = '/auth/login'\n      return\n    }\n\n    // Mark app as initialized\n    appStore.setInitialized(true)\n\n    // Show welcome notification\n    if (authStore.userFullName) {\n      appStore.showNotification(\n        `Benvenuto, ${authStore.userFullName}!`,\n        'success',\n        3000\n      )\n    }\n\n  } catch (error) {\n    console.error('App initialization failed:', error)\n    appStore.showNotification(\n      'Errore durante l\\'inizializzazione dell\\'applicazione',\n      'error',\n      0\n    )\n  }\n})\n\n// Cleanup on unmount\nonUnmounted(() => {\n  stopSessionCheck()\n})\n\n// Handle browser back/forward\nwindow.addEventListener('popstate', () => {\n  authStore.updateLastActivity()\n})\n\n// Handle visibility change (tab focus/blur)\ndocument.addEventListener('visibilitychange', () => {\n  if (!document.hidden && authStore.isAuthenticated) {\n    // Tab became visible, check session\n    authStore.checkSession()\n    authStore.updateLastActivity()\n  }\n})\n</script>\n\n<style scoped>\n/* Page Transitions */\n.page-slide-enter-active,\n.page-slide-leave-active {\n  transition: all var(--brand-transition-normal);\n}\n\n.page-slide-enter-from {\n  opacity: 0;\n  transform: translateX(20px);\n}\n\n.page-slide-leave-to {\n  opacity: 0;\n  transform: translateX(-20px);\n}\n\n.page-fade-enter-active,\n.page-fade-leave-active {\n  transition: opacity var(--brand-transition-fast);\n}\n\n.page-fade-enter-from,\n.page-fade-leave-to {\n  opacity: 0;\n}\n\n/* Loading state */\n.page-loading {\n  pointer-events: none;\n}\n\n.page-loading * {\n  cursor: wait !important;\n}\n\n/* Smooth scrolling */\n#main-content {\n  scroll-behavior: smooth;\n}\n\n/* Custom scrollbar */\n#main-content::-webkit-scrollbar {\n  width: 6px;\n}\n\n#main-content::-webkit-scrollbar-track {\n  background: var(--brand-bg-tertiary);\n}\n\n#main-content::-webkit-scrollbar-thumb {\n  background: var(--brand-border-secondary);\n  border-radius: var(--brand-radius-full);\n}\n\n#main-content::-webkit-scrollbar-thumb:hover {\n  background: var(--brand-primary-400);\n}\n\n/* Focus management */\n.router-link-active {\n  @apply text-brand-primary-600 dark:text-brand-primary-400;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .page-slide-enter-active,\n  .page-slide-leave-active,\n  .page-fade-enter-active,\n  .page-fade-leave-active {\n    transition: none;\n  }\n\n  .animate-spin {\n    animation: none;\n  }\n}\n\n/* Print styles */\n@media print {\n  .sidebar,\n  .top-navigation,\n  .app-footer,\n  .notification-container,\n  .modal-container {\n    display: none !important;\n  }\n\n  #main-content {\n    margin: 0 !important;\n    padding: 0 !important;\n  }\n}\n</style>\n"}