{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/base.html"}, "originalCode": "<!DOCTYPE html>\n<html lang=\"it\" x-data=\"{ darkMode: localStorage.getItem('darkMode') === 'true' }\" :class=\"{ 'dark': darkMode }\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <meta name=\"csrf-token\" content=\"{{ csrf_token() }}\">\n    <title>{% block title %}DatPortal | DatVinci{% endblock %}</title>\n\n    <!-- Tailwind CSS -->\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n        tailwind.config = {\n            darkMode: 'class',\n            theme: {\n                extend: {\n                    colors: {\n                        primary: {\n                            50: '#e6f4fb',\n                            100: '#b3e0f3',\n                            200: '#80cceb',\n                            300: '#4db7e3',\n                            400: '#1aa3dc', // DatVinci blue primary\n                            500: '#0080c0',\n                            600: '#006699',\n                            700: '#004d73',\n                            800: '#00334d',\n                            900: '#001a26'\n                        },\n                        secondary: {\n                            50: '#e6fff9',\n                            100: '#b3ffec',\n                            200: '#80ffdf',\n                            300: '#4dffd3',\n                            400: '#1affc6',\n                            500: '#00cc99', // DatVinci teal secondary\n                            600: '#00a677',\n                            700: '#007f59',\n                            800: '#00533c',\n                            900: '#00291e'\n                        }\n                    }\n                }\n            }\n        }\n    </script>\n    <link rel=\"stylesheet\" href=\"{{ url_for('static', filename='css/tailwind.css') }}\">\n    <link rel=\"stylesheet\" href=\"{{ url_for('static', filename='css/components.css') }}\">\n\n    <!-- Feather Icons -->\n    <script src=\"https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js\"></script>\n\n    <!-- Removed HTMX to avoid conflicts with Alpine.js -->\n\n    <!-- Alpine.js -->\n    <script defer src=\"https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js\"></script>\n\n    <!-- Chart.js -->\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n\n    {% block head_extras %}{% endblock %}\n</head>\n\n<body class=\"bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 min-h-screen flex flex-col transition-colors duration-200\">\n\n    {% if current_user.is_authenticated %}\n    <div class=\"flex h-screen overflow-hidden\"\n         x-data=\"{\n             sidebarOpen: localStorage.getItem('sidebarOpen') === 'true',\n             isLoading: false,\n             currentUrl: window.location.pathname\n         }\"\n         x-init=\"$watch('sidebarOpen', value => localStorage.setItem('sidebarOpen', value))\"\n         :class=\"{ 'page-loading': isLoading }\">\n\n        {% include 'components/sidebar.html' %}\n\n        <div class=\"flex flex-col flex-1 overflow-y-auto\">\n            {% include 'components/navbar.html' %}\n\n            <!-- Main Content -->\n            <main class=\"flex-grow p-4 md:p-6 lg:p-8\" id=\"main-content\">\n                <!-- Loading Indicator -->\n                <div x-show=\"isLoading\" x-cloak class=\"fixed top-0 left-0 w-full h-1 bg-primary-200 dark:bg-primary-800 z-50\">\n                    <div class=\"h-full bg-primary-600 dark:bg-primary-400 animate-pulse\"></div>\n                </div>\n\n                {% include 'components/notification.html' %}\n                <!-- Toast, Modal e Loader rimossi per evitare flash - includere solo quando necessari -->\n\n                <div class=\"container mx-auto content-wrapper\" id=\"content-area\">\n                    {% block content %}{% endblock %}\n                </div>\n            </main>\n\n            <!-- Footer -->\n            <footer class=\"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4 px-8 text-center text-sm text-gray-500 dark:text-gray-400\">\n                <p>&copy; {{ current_year }} DatVinci - DatPortal. Tutti i diritti riservati.</p>\n            </footer>\n        </div>\n    </div>\n    {% else %}\n    <div class=\"min-h-screen flex flex-col\">\n        <!-- Public Navigation -->\n        <nav class=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\">\n            <div class=\"container mx-auto px-4 py-3\">\n                <div class=\"flex justify-between items-center\">\n                    <a href=\"{{ url_for('landing.home') }}\" class=\"flex items-center\">\n                        <span class=\"text-primary-500 font-bold text-xl flex items-center\">\n                            <img src=\"{{ url_for('static', filename='img/logo.png') }}\" id=\"logo\" alt=\"DatVinci\" class=\"h-12 w-auto rounded-lg shadow-xl\">\n                        </span>\n                    </a>\n                    <div class=\"flex items-center space-x-8 ml-8\">\n                        <a href=\"{{ url_for('landing.home') }}\" class=\"text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400\">Home</a>\n                        <a href=\"{{ url_for('landing.services') }}\" class=\"text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400\">Servizi</a>\n                        <a href=\"{{ url_for('landing.contact') }}\" class=\"text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400\">Contatti</a>\n                        <a href=\"{{ url_for('auth.login') }}\" class=\"bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md\">Accedi</a>\n                        <button @click=\"darkMode = !darkMode\" class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700\">\n                            <svg x-show=\"!darkMode\" xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\" />\n                            </svg>\n                            <svg x-show=\"darkMode\" xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\" />\n                            </svg>\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </nav>\n\n        <!-- Main Content -->\n        <main class=\"flex-grow\">\n            {% include 'components/notification.html' %}\n\n            {% block public_content %}{% endblock %}\n        </main>\n\n        <!-- Footer -->\n        <footer class=\"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-6 px-8\">\n            <div class=\"container mx-auto\">\n                <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n                    <div>\n                        <h3 class=\"text-primary-500 font-bold text-lg mb-3\">DatVinci</h3>\n                        <p class=\"text-gray-600 dark:text-gray-400 mb-3\">\n                            Soluzioni innovative per le aziende che guardano al futuro.\n                        </p>\n                        <div class=\"flex space-x-4\">\n                            <a href=\"#\" class=\"text-gray-500 hover:text-primary-500\">\n                                <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                                    <path fill-rule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clip-rule=\"evenodd\"></path>\n                                </svg>\n                            </a>\n                            <a href=\"#\" class=\"text-gray-500 hover:text-primary-500\">\n                                <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                                    <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"></path>\n                                </svg>\n                            </a>\n                            <a href=\"#\" class=\"text-gray-500 hover:text-primary-500\">\n                                <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                                    <path fill-rule=\"evenodd\" d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\" clip-rule=\"evenodd\"></path>\n                                </svg>\n                            </a>\n                            <a href=\"#\" class=\"text-gray-500 hover:text-primary-500\">\n                                <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                                    <path fill-rule=\"evenodd\" d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10c5.51 0 10-4.48 10-10S17.51 2 12 2zm6.605 4.61a8.502 8.502 0 011.93 5.314c-.281-.054-3.101-.629-5.943-.271-.065-.141-.12-.293-.184-.445a25.416 25.416 0 00-.564-1.236c3.145-1.28 4.577-3.124 4.761-3.362zM12 3.475c2.17 0 4.154.813 5.662 2.148-.152.216-1.443 1.941-4.48 3.08-1.399-2.57-2.95-4.675-3.189-5A8.687 8.687 0 0112 3.475zm-3.633.803a53.896 53.896 0 013.167 4.935c-3.992 1.063-7.517 1.04-7.896 1.04a8.581 8.581 0 014.729-5.975zM3.453 12.01v-.21c.37.01 4.512.065 8.775-1.215.25.477.477.965.694 1.453-.109.033-.228.065-.336.098-4.404 1.42-6.747 5.303-6.942 5.629a8.522 8.522 0 01-2.19-5.705zM12 20.547a8.482 8.482 0 01-5.239-1.8c.152-.315 1.888-3.656 6.703-5.337.022-.01.033-.01.054-.022a35.318 35.318 0 011.823 6.475 8.4 8.4 0 01-3.341.684zm4.761-1.465c-.086-.52-.542-3.015-1.659-6.084 2.679-.423 5.022.271 5.314.369a8.468 8.468 0 01-3.655 5.715z\" clip-rule=\"evenodd\"></path>\n                                </svg>\n                            </a>\n                        </div>\n                    </div>\n                    <div>\n                        <h3 class=\"text-primary-500 font-bold text-lg mb-3\">Link Utili</h3>\n                        <ul class=\"space-y-2\">\n                            <li><a href=\"{{ url_for('landing.home') }}\" class=\"text-gray-600 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400\">Home</a></li>\n                            <li><a href=\"{{ url_for('landing.services') }}\" class=\"text-gray-600 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400\">Servizi</a></li>\n                            <li><a href=\"{{ url_for('landing.about') }}\" class=\"text-gray-600 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400\">Chi Siamo</a></li>\n                            <li><a href=\"{{ url_for('landing.contact') }}\" class=\"text-gray-600 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400\">Contatti</a></li>\n                            <li><a href=\"{{ url_for('landing.privacy') }}\" class=\"text-gray-600 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400\">Privacy Policy</a></li>\n                        </ul>\n                    </div>\n                    <div>\n                        <h3 class=\"text-primary-500 font-bold text-lg mb-3\">Contatti</h3>\n                        <ul class=\"space-y-2\">\n                            <li class=\"flex items-start space-x-2\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 mt-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                </svg>\n                                <span class=\"text-gray-600 dark:text-gray-400\">Via dell'Innovazione, 123<br>Milano, Italia</span>\n                            </li>\n                            <li class=\"flex items-center space-x-2\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                                </svg>\n                                <span class=\"text-gray-600 dark:text-gray-400\"><EMAIL></span>\n                            </li>\n                            <li class=\"flex items-center space-x-2\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                                </svg>\n                                <span class=\"text-gray-600 dark:text-gray-400\">+39 02 1234567</span>\n                            </li>\n                        </ul>\n                    </div>\n                </div>\n                <div class=\"mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 text-center text-gray-500 dark:text-gray-400\">\n                    <p>&copy; 2025 DatVinci - DatPortal. Tutti i diritti riservati.</p>\n                </div>\n            </div>\n        </footer>\n    </div>\n    {% endif %}\n\n    <!-- App Scripts -->\n    <script src=\"{{ url_for('static', filename='js/theme.js') }}\"></script>\n    \n    <!-- Utility functions -->\n    <script>\n        function formatDate(dateString) {\n            const date = new Date(dateString);\n            return date.toLocaleDateString('it-IT', { \n                year: 'numeric', \n                month: 'short', \n                day: 'numeric',\n                hour: '2-digit',\n                minute: '2-digit'\n            });\n        }\n    </script>\n\n    <!-- Initialize Feather Icons -->\n    <script>\n        document.addEventListener(\"DOMContentLoaded\", function() {\n            feather.replace();\n        });\n    </script>\n\n    {% block scripts %}{% endblock %}\n</body>\n</html>\n"}