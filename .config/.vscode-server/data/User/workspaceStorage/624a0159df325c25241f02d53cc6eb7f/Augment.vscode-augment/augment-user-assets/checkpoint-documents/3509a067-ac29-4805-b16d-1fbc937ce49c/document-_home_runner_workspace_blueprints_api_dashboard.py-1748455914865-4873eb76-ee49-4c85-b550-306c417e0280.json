{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/api/dashboard.py"}, "modifiedCode": "\"\"\"\nAPI endpoints for dashboard data.\nProvides REST API for dashboard statistics, recent activities, and quick actions.\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user\nfrom sqlalchemy import func, desc, and_, or_\nfrom datetime import datetime, timedelta\n\nfrom extensions import db\nfrom models import (\n    Project, Task, Timesheet, News, Event, KPI, Client, Document, \n    FundingOpportunity, User, Notification, Department, UserSkill, Skill\n)\nfrom utils.api_utils import (\n    api_response, api_login_required, handle_api_error\n)\nfrom utils.permissions import (\n    PERMISSION_VIEW_DASHBOARD, PERMISSION_VIEW_ALL_PROJECTS, \n    PERMISSION_VIEW_REPORTS, PERMISSION_VIEW_CRM, user_has_permission\n)\n\n# Create blueprint\napi_dashboard = Blueprint('api_dashboard', __name__, url_prefix='/dashboard')\n\n@api_dashboard.route('/stats', methods=['GET'])\n@api_login_required\ndef get_dashboard_stats():\n    \"\"\"\n    Get dashboard statistics and KPIs.\n    \n    Returns:\n        JSON with dashboard statistics including:\n        - Active projects count\n        - Tasks statistics\n        - Recent activities count\n        - KPIs data\n    \"\"\"\n    try:\n        now = datetime.utcnow()\n        week_ahead = now + timedelta(days=7)\n        \n        # Active projects count - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            active_projects_count = Project.query.filter_by(status='active').count()\n            total_projects_count = Project.query.count()\n        else:\n            # Only projects where user is a team member\n            active_projects_count = Project.query.filter_by(\n                status='active'\n            ).filter(\n                Project.team_members.any(id=current_user.id)\n            ).count()\n            total_projects_count = Project.query.filter(\n                Project.team_members.any(id=current_user.id)\n            ).count()\n\n        # Tasks statistics - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            total_tasks = Task.query.count()\n            pending_tasks = Task.query.filter(Task.status.in_(['todo', 'in-progress'])).count()\n            completed_tasks = Task.query.filter_by(status='done').count()\n            overdue_tasks = Task.query.filter(\n                and_(Task.due_date < now.date(), Task.status != 'done')\n            ).count()\n        else:\n            total_tasks = Task.query.filter_by(assignee_id=current_user.id).count()\n            pending_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.status.in_(['todo', 'in-progress'])\n                )\n            ).count()\n            completed_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.status == 'done'\n                )\n            ).count()\n            overdue_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.due_date < now.date(),\n                    Task.status != 'done'\n                )\n            ).count()\n\n        # Client count - visible only to those with CRM permission\n        if user_has_permission(current_user.role, PERMISSION_VIEW_CRM):\n            client_count = Client.query.count()\n        else:\n            client_count = 0\n\n        # Team statistics\n        total_users = User.query.filter_by(is_active=True).count()\n        total_departments = Department.query.filter_by(is_active=True).count()\n\n        # Recent activities count\n        recent_timesheets_count = Timesheet.query.filter(\n            and_(\n                Timesheet.user_id == current_user.id,\n                Timesheet.date >= (now - timedelta(days=7)).date()\n            )\n        ).count()\n\n        # Unread notifications count\n        unread_notifications = Notification.query.filter_by(\n            user_id=current_user.id,\n            is_read=False\n        ).count()\n\n        stats_data = {\n            'projects': {\n                'active': active_projects_count,\n                'total': total_projects_count\n            },\n            'tasks': {\n                'total': total_tasks,\n                'pending': pending_tasks,\n                'completed': completed_tasks,\n                'overdue': overdue_tasks\n            },\n            'team': {\n                'users': total_users,\n                'departments': total_departments,\n                'clients': client_count\n            },\n            'activities': {\n                'recent_timesheets': recent_timesheets_count,\n                'unread_notifications': unread_notifications\n            }\n        }\n\n        return api_response(\n            data=stats_data,\n            message=\"Dashboard statistics retrieved successfully\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/recent-activities', methods=['GET'])\n@api_login_required\ndef get_recent_activities():\n    \"\"\"\n    Get recent activities for the dashboard.\n    \n    Query Parameters:\n        limit (int): Number of activities to return (default: 10, max: 50)\n        \n    Returns:\n        JSON with recent activities including:\n        - Recent tasks\n        - Recent timesheet entries\n        - Recent projects\n        - Recent events\n    \"\"\"\n    try:\n        # Get limit parameter\n        limit = min(int(request.args.get('limit', 10)), 50)\n        \n        now = datetime.utcnow()\n        week_ago = now - timedelta(days=7)\n\n        activities = []\n\n        # Recent tasks (assigned to user or all if has permission)\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            recent_tasks = Task.query.filter(\n                Task.updated_at >= week_ago\n            ).order_by(desc(Task.updated_at)).limit(limit).all()\n        else:\n            recent_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.updated_at >= week_ago\n                )\n            ).order_by(desc(Task.updated_at)).limit(limit).all()\n\n        for task in recent_tasks:\n            activities.append({\n                'type': 'task',\n                'id': task.id,\n                'title': task.name,\n                'description': f\"Task {task.status} in project {task.project.name}\",\n                'timestamp': task.updated_at.isoformat(),\n                'link': f\"/projects/{task.project_id}#task-{task.id}\",\n                'status': task.status,\n                'priority': task.priority\n            })\n\n        # Recent timesheet entries (user's own)\n        recent_timesheets = Timesheet.query.filter(\n            and_(\n                Timesheet.user_id == current_user.id,\n                Timesheet.created_at >= week_ago\n            )\n        ).order_by(desc(Timesheet.created_at)).limit(limit).all()\n\n        for timesheet in recent_timesheets:\n            activities.append({\n                'type': 'timesheet',\n                'id': timesheet.id,\n                'title': f\"Logged {timesheet.hours}h\",\n                'description': f\"Time logged for {timesheet.project.name}\",\n                'timestamp': timesheet.created_at.isoformat(),\n                'link': f\"/projects/{timesheet.project_id}\",\n                'hours': timesheet.hours,\n                'date': timesheet.date.isoformat()\n            })\n\n        # Recent events (user's or all if has permission)\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            recent_events = Event.query.filter(\n                Event.created_at >= week_ago\n            ).order_by(desc(Event.created_at)).limit(limit).all()\n        else:\n            recent_events = Event.query.filter(\n                and_(\n                    Event.created_by == current_user.id,\n                    Event.created_at >= week_ago\n                )\n            ).order_by(desc(Event.created_at)).limit(limit).all()\n\n        for event in recent_events:\n            activities.append({\n                'type': 'event',\n                'id': event.id,\n                'title': event.title,\n                'description': f\"Event scheduled for {event.start_time.strftime('%Y-%m-%d %H:%M')}\",\n                'timestamp': event.created_at.isoformat(),\n                'link': f\"/calendar#event-{event.id}\",\n                'start_time': event.start_time.isoformat(),\n                'event_type': event.event_type\n            })\n\n        # Sort all activities by timestamp (most recent first)\n        activities.sort(key=lambda x: x['timestamp'], reverse=True)\n        \n        # Limit to requested number\n        activities = activities[:limit]\n\n        return api_response(\n            data={'activities': activities},\n            message=f\"Retrieved {len(activities)} recent activities\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/upcoming-tasks', methods=['GET'])\n@api_login_required\ndef get_upcoming_tasks():\n    \"\"\"\n    Get upcoming tasks for the dashboard.\n    \n    Query Parameters:\n        days (int): Number of days ahead to look (default: 7)\n        limit (int): Number of tasks to return (default: 10)\n        \n    Returns:\n        JSON with upcoming tasks\n    \"\"\"\n    try:\n        # Get parameters\n        days = int(request.args.get('days', 7))\n        limit = min(int(request.args.get('limit', 10)), 50)\n        \n        now = datetime.utcnow()\n        future_date = now + timedelta(days=days)\n\n        # Get upcoming tasks - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            upcoming_tasks = Task.query.filter(\n                and_(\n                    Task.due_date.between(now.date(), future_date.date()),\n                    Task.status != 'done'\n                )\n            ).order_by(Task.due_date, Task.priority.desc()).limit(limit).all()\n        else:\n            upcoming_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.due_date.between(now.date(), future_date.date()),\n                    Task.status != 'done'\n                )\n            ).order_by(Task.due_date, Task.priority.desc()).limit(limit).all()\n\n        tasks_data = []\n        for task in upcoming_tasks:\n            # Calculate days until due\n            days_until_due = (task.due_date - now.date()).days\n            \n            tasks_data.append({\n                'id': task.id,\n                'name': task.name,\n                'description': task.description,\n                'project_id': task.project_id,\n                'project_name': task.project.name,\n                'assignee_id': task.assignee_id,\n                'assignee_name': task.assignee.full_name if task.assignee else None,\n                'status': task.status,\n                'priority': task.priority,\n                'due_date': task.due_date.isoformat(),\n                'days_until_due': days_until_due,\n                'estimated_hours': task.estimated_hours,\n                'is_overdue': days_until_due < 0,\n                'link': f\"/projects/{task.project_id}#task-{task.id}\"\n            })\n\n        return api_response(\n            data={'tasks': tasks_data},\n            message=f\"Retrieved {len(tasks_data)} upcoming tasks\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n"}