{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/layout/TopNavigation.vue"}, "modifiedCode": "<template>\n  <header class=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 h-header\">\n    <div class=\"flex items-center justify-between px-4 sm:px-6 lg:px-8 h-full\">\n      <!-- Left Section -->\n      <div class=\"flex items-center\">\n        <!-- Mobile Menu Button -->\n        <button\n          @click=\"appStore.toggleSidebar()\"\n          class=\"md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-brand-primary-500\"\n        >\n          <i class=\"fas fa-bars h-6 w-6\"></i>\n        </button>\n\n        <!-- Page Title -->\n        <div class=\"ml-4 md:ml-0\">\n          <h1 class=\"text-xl font-semibold text-gray-900 dark:text-white\">\n            {{ appStore.pageTitle }}\n          </h1>\n          <p v-if=\"pageSubtitle\" class=\"text-sm text-gray-500 dark:text-gray-400\">\n            {{ pageSubtitle }}\n          </p>\n        </div>\n      </div>\n\n      <!-- Right Section -->\n      <div class=\"flex items-center space-x-4\">\n        <!-- Search -->\n        <div class=\"hidden lg:block\">\n          <div class=\"relative\">\n            <input\n              v-model=\"searchQuery\"\n              type=\"text\"\n              placeholder=\"Cerca...\"\n              class=\"input-brand w-64 pl-10 pr-4 py-2 text-sm\"\n              @keyup.enter=\"performSearch\"\n            >\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <i class=\"fas fa-search h-4 w-4 text-gray-400\"></i>\n            </div>\n            <button\n              v-if=\"searchQuery\"\n              @click=\"clearSearch\"\n              class=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n            >\n              <i class=\"fas fa-times h-4 w-4 text-gray-400 hover:text-gray-600\"></i>\n            </button>\n          </div>\n        </div>\n\n        <!-- Quick Actions -->\n        <div class=\"flex items-center space-x-2\">\n          <!-- Notifications -->\n          <NotificationDropdown />\n\n          <!-- Dark Mode Toggle -->\n          <button\n            @click=\"appStore.toggleDarkMode()\"\n            class=\"p-2 rounded-full text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-brand-primary-500 transition-colors\"\n            :title=\"appStore.darkMode ? 'Modalità chiara' : 'Modalità scura'\"\n          >\n            <i \n              :class=\"appStore.darkMode ? 'fas fa-sun' : 'fas fa-moon'\"\n              class=\"h-5 w-5\"\n            ></i>\n          </button>\n\n          <!-- Quick Add Button -->\n          <QuickAddDropdown />\n\n          <!-- User Menu -->\n          <UserDropdown />\n        </div>\n      </div>\n    </div>\n\n    <!-- Mobile Search (shown when search is active) -->\n    <div \n      v-if=\"showMobileSearch\"\n      class=\"lg:hidden border-t border-gray-200 dark:border-gray-700 px-4 py-3\"\n    >\n      <div class=\"relative\">\n        <input\n          v-model=\"searchQuery\"\n          type=\"text\"\n          placeholder=\"Cerca...\"\n          class=\"input-brand w-full pl-10 pr-4 py-2 text-sm\"\n          @keyup.enter=\"performSearch\"\n          ref=\"mobileSearchInput\"\n        >\n        <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n          <i class=\"fas fa-search h-4 w-4 text-gray-400\"></i>\n        </div>\n        <button\n          @click=\"closeMobileSearch\"\n          class=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n        >\n          <i class=\"fas fa-times h-4 w-4 text-gray-400 hover:text-gray-600\"></i>\n        </button>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script setup>\nimport { ref, computed, nextTick } from 'vue'\nimport { useRoute } from 'vue-router'\nimport { useAppStore } from '../../stores/app.js'\nimport { useAuthStore } from '../../stores/auth.js'\n\n// Components\nimport NotificationDropdown from './NotificationDropdown.vue'\nimport QuickAddDropdown from './QuickAddDropdown.vue'\nimport UserDropdown from './UserDropdown.vue'\n\n// Stores\nconst appStore = useAppStore()\nconst authStore = useAuthStore()\nconst route = useRoute()\n\n// State\nconst searchQuery = ref('')\nconst showMobileSearch = ref(false)\nconst mobileSearchInput = ref(null)\n\n// Computed\nconst pageSubtitle = computed(() => {\n  // Generate subtitle based on current route\n  const routeMeta = route.meta\n  if (routeMeta?.subtitle) {\n    return routeMeta.subtitle\n  }\n  \n  // Auto-generate subtitle based on user info\n  if (route.name === 'dashboard') {\n    return `Benvenuto, ${authStore.userFullName}`\n  }\n  \n  return null\n})\n\n// Methods\nfunction performSearch() {\n  if (!searchQuery.value.trim()) return\n  \n  // Implement global search\n  console.log('Searching for:', searchQuery.value)\n  \n  // You can emit an event or call a search service here\n  appStore.showNotification(`Ricerca per: \"${searchQuery.value}\"`, 'info', 3000)\n  \n  // Close mobile search\n  if (showMobileSearch.value) {\n    closeMobileSearch()\n  }\n}\n\nfunction clearSearch() {\n  searchQuery.value = ''\n}\n\nfunction openMobileSearch() {\n  showMobileSearch.value = true\n  nextTick(() => {\n    mobileSearchInput.value?.focus()\n  })\n}\n\nfunction closeMobileSearch() {\n  showMobileSearch.value = false\n  searchQuery.value = ''\n}\n\n// Keyboard shortcuts\nfunction handleKeydown(event) {\n  // Ctrl/Cmd + K to open search\n  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {\n    event.preventDefault()\n    if (appStore.isMobile) {\n      openMobileSearch()\n    } else {\n      // Focus desktop search\n      const searchInput = document.querySelector('input[placeholder=\"Cerca...\"]')\n      searchInput?.focus()\n    }\n  }\n  \n  // Escape to close mobile search\n  if (event.key === 'Escape' && showMobileSearch.value) {\n    closeMobileSearch()\n  }\n}\n\n// Add keyboard event listener\nif (typeof window !== 'undefined') {\n  window.addEventListener('keydown', handleKeydown)\n}\n</script>\n\n<style scoped>\n/* Header animations */\n.header-enter-active,\n.header-leave-active {\n  transition: all var(--brand-transition-normal);\n}\n\n.header-enter-from,\n.header-leave-to {\n  opacity: 0;\n  transform: translateY(-10px);\n}\n\n/* Search input focus effects */\n.input-brand:focus {\n  box-shadow: 0 0 0 3px rgba(var(--brand-primary-500), 0.1);\n}\n\n/* Mobile search animation */\n.mobile-search-enter-active,\n.mobile-search-leave-active {\n  transition: all 0.3s ease;\n}\n\n.mobile-search-enter-from,\n.mobile-search-leave-to {\n  opacity: 0;\n  transform: translateY(-10px);\n}\n\n/* Button hover effects */\nbutton:hover i {\n  transform: scale(1.1);\n  transition: transform 0.2s ease;\n}\n\n/* Search suggestions (if implemented) */\n.search-suggestions {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  background: white;\n  border: 1px solid var(--brand-border-primary);\n  border-top: none;\n  border-radius: 0 0 var(--brand-radius-md) var(--brand-radius-md);\n  box-shadow: var(--brand-shadow-lg);\n  z-index: 50;\n}\n\n/* Responsive adjustments */\n@media (max-width: 640px) {\n  .text-xl {\n    font-size: 1.125rem;\n  }\n  \n  .space-x-4 > * + * {\n    margin-left: 0.5rem;\n  }\n}\n\n/* Focus management */\nbutton:focus,\ninput:focus {\n  outline: 2px solid var(--brand-primary-500);\n  outline-offset: 2px;\n}\n\n/* Dark mode specific styles */\n.dark .input-brand {\n  background-color: var(--brand-bg-secondary);\n  border-color: var(--brand-border-secondary);\n  color: var(--brand-text-primary);\n}\n\n.dark .input-brand::placeholder {\n  color: var(--brand-text-tertiary);\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .transition-colors,\n  .header-enter-active,\n  .header-leave-active,\n  .mobile-search-enter-active,\n  .mobile-search-leave-active {\n    transition: none;\n  }\n  \n  button:hover i {\n    transform: none;\n  }\n}\n\n/* Print styles */\n@media print {\n  header {\n    display: none !important;\n  }\n}\n</style>\n"}