/**
 * Vue Router Configuration
 * Definisce tutte le route dell'applicazione SPA
 */

// Lazy load components for better performance
// PUBLIC PAGES
const Home = () => import('../views/public/Home.vue')
const Services = () => import('../views/public/Services.vue')
const ServiceDetail = () => import('../views/public/ServiceDetail.vue')
const About = () => import('../views/public/About.vue')
const Contact = () => import('../views/public/Contact.vue')
const Privacy = () => import('../views/public/Privacy.vue')

// PRIVATE PAGES
const Dashboard = () => import('../views/Dashboard.vue')
const Personnel = () => import('../views/Personnel.vue')
const Projects = () => import('../views/Projects.vue')
const ProjectDetail = () => import('../views/ProjectDetail.vue')
const Performance = () => import('../views/Performance.vue')
const NotFound = () => import('../views/NotFound.vue')

export const routes = [
  // === PUBLIC ROUTES ===
  {
    path: '/',
    name: 'home',
    component: Home,
    meta: {
      title: 'DatVinci - Innovazione per il futuro',
      requiresAuth: false,
      layout: 'public'
    }
  },
  {
    path: '/services',
    name: 'services',
    component: Services,
    meta: {
      title: 'Servizi - DatVinci',
      requiresAuth: false,
      layout: 'public'
    }
  },
  {
    path: '/services/:id',
    name: 'service-detail',
    component: ServiceDetail,
    meta: {
      title: 'Dettaglio Servizio - DatVinci',
      requiresAuth: false,
      layout: 'public'
    }
  },
  {
    path: '/about',
    name: 'about',
    component: About,
    meta: {
      title: 'Chi Siamo - DatVinci',
      requiresAuth: false,
      layout: 'public'
    }
  },
  {
    path: '/contact',
    name: 'contact',
    component: Contact,
    meta: {
      title: 'Contatti - DatVinci',
      requiresAuth: false,
      layout: 'public'
    }
  },
  {
    path: '/privacy',
    name: 'privacy',
    component: Privacy,
    meta: {
      title: 'Privacy Policy - DatVinci',
      requiresAuth: false,
      layout: 'public'
    }
  },

  // === PRIVATE ROUTES ===
  {
    path: '/dashboard',
    name: 'dashboard',
    component: Dashboard,
    meta: {
      title: 'Dashboard',
      requiresAuth: true,
      layout: 'private',
      permissions: ['view_dashboard'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/dashboard' }
      ]
    }
  },

  // === PERSONNEL ===
  {
    path: '/personnel',
    name: 'personnel',
    component: Personnel,
    meta: {
      title: 'Personale',
      requiresAuth: true,
      layout: 'private',
      permissions: ['view_personnel'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/dashboard' },
        { name: 'Personale', path: '/personnel' }
      ]
    }
  },
  {
    path: '/personnel/directory',
    name: 'personnel-directory',
    component: () => import('../views/PersonnelDirectory.vue'),
    meta: {
      title: 'Directory Personale',
      requiresAuth: true,
      permissions: ['view_personnel'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Personale', path: '/personnel' },
        { name: 'Directory', path: '/personnel/directory' }
      ]
    }
  },
  {
    path: '/personnel/orgchart',
    name: 'personnel-orgchart',
    component: () => import('../views/PersonnelOrgChart.vue'),
    meta: {
      title: 'Organigramma',
      requiresAuth: true,
      permissions: ['view_personnel'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Personale', path: '/personnel' },
        { name: 'Organigramma', path: '/personnel/orgchart' }
      ]
    }
  },
  {
    path: '/personnel/skills',
    name: 'personnel-skills',
    component: () => import('../views/PersonnelSkills.vue'),
    meta: {
      title: 'Competenze',
      requiresAuth: true,
      permissions: ['view_personnel'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Personale', path: '/personnel' },
        { name: 'Competenze', path: '/personnel/skills' }
      ]
    }
  },
  {
    path: '/personnel/departments',
    name: 'personnel-departments',
    component: () => import('../views/PersonnelDepartments.vue'),
    meta: {
      title: 'Dipartimenti',
      requiresAuth: true,
      permissions: ['manage_departments'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Personale', path: '/personnel' },
        { name: 'Dipartimenti', path: '/personnel/departments' }
      ]
    }
  },

  // === PROJECTS ===
  {
    path: '/projects',
    name: 'projects',
    component: Projects,
    meta: {
      title: 'Progetti',
      requiresAuth: true,
      permissions: ['view_projects'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Progetti', path: '/projects' }
      ]
    }
  },
  {
    path: '/projects/:id',
    name: 'project-detail',
    component: ProjectDetail,
    meta: {
      title: 'Dettaglio Progetto',
      requiresAuth: true,
      permissions: ['view_projects'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Progetti', path: '/projects' },
        { name: 'Dettaglio', path: '' } // Will be updated dynamically
      ]
    }
  },

  // === CRM ===
  {
    path: '/crm',
    name: 'crm',
    component: () => import('../views/CRM.vue'),
    meta: {
      title: 'CRM',
      requiresAuth: true,
      permissions: ['view_crm'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'CRM', path: '/crm' }
      ]
    }
  },
  {
    path: '/crm/clients',
    name: 'crm-clients',
    component: () => import('../views/CRMClients.vue'),
    meta: {
      title: 'Clienti',
      requiresAuth: true,
      permissions: ['view_crm'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'CRM', path: '/crm' },
        { name: 'Clienti', path: '/crm/clients' }
      ]
    }
  },

  // === PRODUCTS ===
  {
    path: '/products',
    name: 'products',
    component: () => import('../views/Products.vue'),
    meta: {
      title: 'Prodotti e Servizi',
      requiresAuth: true,
      permissions: ['view_products'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Prodotti', path: '/products' }
      ]
    }
  },

  // === PERFORMANCE ===
  {
    path: '/performance',
    name: 'performance',
    component: Performance,
    meta: {
      title: 'Performance',
      requiresAuth: true,
      permissions: ['view_performance'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Performance', path: '/performance' }
      ]
    }
  },
  {
    path: '/performance/kpi',
    name: 'performance-kpi',
    component: () => import('../views/PerformanceKPI.vue'),
    meta: {
      title: 'KPI',
      requiresAuth: true,
      permissions: ['view_performance'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Performance', path: '/performance' },
        { name: 'KPI', path: '/performance/kpi' }
      ]
    }
  },

  // === COMMUNICATIONS ===
  {
    path: '/communications',
    name: 'communications',
    component: () => import('../views/Communications.vue'),
    meta: {
      title: 'Comunicazione',
      requiresAuth: true,
      permissions: ['view_communications'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Comunicazione', path: '/communications' }
      ]
    }
  },
  {
    path: '/communications/news',
    name: 'communications-news',
    component: () => import('../views/CommunicationsNews.vue'),
    meta: {
      title: 'News',
      requiresAuth: true,
      permissions: ['view_communications'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Comunicazione', path: '/communications' },
        { name: 'News', path: '/communications/news' }
      ]
    }
  },

  // === FUNDING ===
  {
    path: '/funding',
    name: 'funding',
    component: () => import('../views/Funding.vue'),
    meta: {
      title: 'Finanziamenti',
      requiresAuth: true,
      permissions: ['view_funding'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Finanziamenti', path: '/funding' }
      ]
    }
  },
  {
    path: '/funding/opportunities',
    name: 'funding-opportunities',
    component: () => import('../views/FundingOpportunities.vue'),
    meta: {
      title: 'Opportunità',
      requiresAuth: true,
      permissions: ['view_funding'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Finanziamenti', path: '/funding' },
        { name: 'Opportunità', path: '/funding/opportunities' }
      ]
    }
  },

  // === REPORTING ===
  {
    path: '/reporting',
    name: 'reporting',
    component: () => import('../views/Reporting.vue'),
    meta: {
      title: 'Rendicontazione',
      requiresAuth: true,
      permissions: ['view_reporting'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Rendicontazione', path: '/reporting' }
      ]
    }
  },

  // === STARTUP ===
  {
    path: '/startup',
    name: 'startup',
    component: () => import('../views/Startup.vue'),
    meta: {
      title: 'Startup',
      requiresAuth: true,
      permissions: ['view_startup'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Startup', path: '/startup' }
      ]
    }
  },

  // === ADMIN ===
  {
    path: '/admin',
    name: 'admin',
    component: () => import('../views/Admin.vue'),
    meta: {
      title: 'Amministrazione',
      requiresAuth: true,
      permissions: ['admin'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Admin', path: '/admin' }
      ]
    }
  },
  {
    path: '/admin/kpi-templates',
    name: 'admin-kpi-templates',
    component: () => import('../views/AdminKPITemplates.vue'),
    meta: {
      title: 'Template KPI',
      requiresAuth: true,
      permissions: ['admin'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Admin', path: '/admin' },
        { name: 'Template KPI', path: '/admin/kpi-templates' }
      ]
    }
  },

  // === PROFILE ===
  {
    path: '/profile',
    name: 'profile',
    component: () => import('../views/Profile.vue'),
    meta: {
      title: 'Profilo',
      requiresAuth: true,
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Profilo', path: '/profile' }
      ]
    }
  },

  // === SETTINGS ===
  {
    path: '/settings',
    name: 'settings',
    component: () => import('../views/Settings.vue'),
    meta: {
      title: 'Impostazioni',
      requiresAuth: true,
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Impostazioni', path: '/settings' }
      ]
    }
  },

  // === BRAND SETTINGS (Admin only) ===
  {
    path: '/settings/brand',
    name: 'settings-brand',
    component: () => import('../views/SettingsBrand.vue'),
    meta: {
      title: 'Impostazioni Brand',
      requiresAuth: true,
      permissions: ['admin'],
      breadcrumbs: [
        { name: 'Dashboard', path: '/' },
        { name: 'Impostazioni', path: '/settings' },
        { name: 'Brand', path: '/settings/brand' }
      ]
    }
  },

  // === ERROR PAGES ===
  {
    path: '/404',
    name: 'not-found',
    component: NotFound,
    meta: {
      title: 'Pagina non trovata',
      requiresAuth: false
    }
  },

  // === CATCH ALL ===
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

export default routes
