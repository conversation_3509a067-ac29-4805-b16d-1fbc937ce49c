"""
Test suite for Personnel API endpoints.
Tests for users, departments, and skills management.
"""

import pytest
import json
from datetime import datetime, date
from flask import url_for

from models import User, Department, Skill, UserSkill, UserProfile
from extensions import db


class TestPersonnelUsersAPI:
    """Test cases for /api/personnel/users endpoints."""

    def test_get_users_success(self, client, auth, sample_users):
        """Test successful retrieval of users list."""
        # Login as admin to have proper permissions
        auth.login(username='admin', password='password')

        response = client.get('/api/personnel/users')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'users' in data['data']
        assert 'pagination' in data['data']
        assert len(data['data']['users']) > 0

        # Check user structure
        user = data['data']['users'][0]
        required_fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'full_name', 'role', 'is_active'
        ]
        for field in required_fields:
            assert field in user

    def test_get_users_with_pagination(self, client, auth, sample_users):
        """Test users list with pagination parameters."""
        auth.login(username='admin', password='password')

        response = client.get('/api/personnel/users?page=1&per_page=2')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert len(data['data']['users']) <= 2
        assert data['data']['pagination']['per_page'] == 2
        assert data['data']['pagination']['page'] == 1

    def test_get_users_with_search(self, client, auth, sample_users):
        """Test users list with search parameter."""
        auth.login()

        # Search for a specific user
        response = client.get('/api/personnel/users?search=admin')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # Check that search results contain the search term
        for user in data['data']['users']:
            user_text = f"{user['username']} {user['first_name']} {user['last_name']} {user['email']}".lower()
            assert 'admin' in user_text

    def test_get_users_with_department_filter(self, client, auth, sample_users, sample_departments):
        """Test users list filtered by department."""
        auth.login()

        # Get first department ID
        dept = sample_departments[0]

        response = client.get(f'/api/personnel/users?department_id={dept.id}')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # All users should belong to the specified department
        for user in data['data']['users']:
            if user['department_id'] is not None:
                assert user['department_id'] == dept.id

    def test_get_users_with_role_filter(self, client, auth, sample_users):
        """Test users list filtered by role."""
        auth.login()

        response = client.get('/api/personnel/users?role=admin')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # All users should have admin role
        for user in data['data']['users']:
            assert user['role'] == 'admin'

    def test_get_users_with_active_filter(self, client, auth, sample_users):
        """Test users list filtered by active status."""
        auth.login()

        response = client.get('/api/personnel/users?is_active=true')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # All users should be active
        for user in data['data']['users']:
            assert user['is_active'] is True

    def test_get_users_with_skills_filter(self, client, auth, sample_users, sample_skills):
        """Test users list filtered by skills."""
        auth.login()

        # Get first skill ID from database to avoid DetachedInstanceError
        from models import Skill
        skill = Skill.query.first()
        if not skill:
            pytest.skip("No skills found in database")

        response = client.get(f'/api/personnel/users?skills={skill.id}')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # Users should have the specified skill
        for user in data['data']['users']:
            skill_ids = [s['id'] for s in user['skills']]
            if skill_ids:  # Only check if user has skills
                assert skill.id in skill_ids

    def test_get_users_with_ordering(self, client, auth, sample_users):
        """Test users list with custom ordering."""
        auth.login()

        # Test ascending order by first name
        response = client.get('/api/personnel/users?order_by=first_name&order_dir=asc')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # Check that results are ordered
        if len(data['data']['users']) > 1:
            first_names = [user['first_name'] for user in data['data']['users']]
            assert first_names == sorted(first_names)

    def test_get_users_unauthorized(self, client, auth):
        """Test users list access without authentication."""
        # Ensure user is logged out
        auth.logout()

        response = client.get('/api/personnel/users')
        assert response.status_code == 401

    def test_get_users_forbidden(self, client, auth):
        """Test users list access without proper permissions."""
        # Login as user without VIEW_PERSONNEL_DATA permission
        auth.login(username='employee', password='password')

        response = client.get('/api/personnel/users')
        assert response.status_code == 403

    def test_get_user_by_id_success(self, client, auth, sample_users):
        """Test successful retrieval of specific user."""
        auth.login()

        # Get first user ID from database to avoid DetachedInstanceError
        from models import User
        user = User.query.first()
        if not user:
            pytest.skip("No users found in database")

        response = client.get(f'/api/personnel/users/{user.id}')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'user' in data['data']

        user_data = data['data']['user']
        assert user_data['id'] == user.id
        assert user_data['username'] == user.username
        assert user_data['email'] == user.email

        # Check detailed fields
        detailed_fields = [
            'bio', 'dark_mode', 'created_at', 'skills', 'projects', 'profile'
        ]
        for field in detailed_fields:
            assert field in user_data

    def test_get_user_by_id_not_found(self, client, auth):
        """Test retrieval of non-existent user."""
        auth.login()

        response = client.get('/api/personnel/users/99999')
        assert response.status_code == 404

    def test_get_user_by_id_unauthorized(self, client, auth, sample_users):
        """Test user retrieval without authentication."""
        # Ensure user is logged out
        auth.logout()

        # Get first user ID from database to avoid DetachedInstanceError
        from models import User
        user = User.query.first()
        if not user:
            pytest.skip("No users found in database")

        response = client.get(f'/api/personnel/users/{user.id}')
        assert response.status_code == 401


class TestPersonnelDepartmentsAPI:
    """Test cases for /api/personnel/departments endpoints."""

    def test_get_departments_success(self, client, auth, sample_departments):
        """Test successful retrieval of departments list."""
        auth.login()

        response = client.get('/api/personnel/departments')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'departments' in data['data']
        assert len(data['data']['departments']) > 0

        # Check department structure
        dept = data['data']['departments'][0]
        required_fields = [
            'id', 'name', 'description', 'manager_id', 'user_count', 'users'
        ]
        for field in required_fields:
            assert field in dept

    def test_get_departments_with_manager_info(self, client, auth, sample_departments, sample_users):
        """Test departments list includes manager information."""
        auth.login()

        # Set a manager for a department
        dept = sample_departments[0]
        manager = sample_users[0]
        dept.manager_id = manager.id
        db.session.commit()

        response = client.get('/api/personnel/departments')
        assert response.status_code == 200

        data = json.loads(response.data)
        dept_data = next(d for d in data['data']['departments'] if d['id'] == dept.id)

        assert dept_data['manager_id'] == manager.id
        assert dept_data['manager'] is not None
        assert dept_data['manager']['id'] == manager.id
        assert dept_data['manager']['full_name'] == manager.full_name

    def test_get_departments_with_users(self, client, auth, sample_departments, sample_users):
        """Test departments list includes user information."""
        auth.login()

        # Assign users to department
        dept = sample_departments[0]
        user = sample_users[0]
        user.department_id = dept.id
        db.session.commit()

        response = client.get('/api/personnel/departments')
        assert response.status_code == 200

        data = json.loads(response.data)
        dept_data = next(d for d in data['data']['departments'] if d['id'] == dept.id)

        assert dept_data['user_count'] >= 1
        assert len(dept_data['users']) >= 1

        user_data = next(u for u in dept_data['users'] if u['id'] == user.id)
        assert user_data['full_name'] == user.full_name
        assert user_data['position'] == user.position

    def test_get_departments_unauthorized(self, client, auth):
        """Test departments list access without authentication."""
        # Ensure user is logged out
        auth.logout()

        response = client.get('/api/personnel/departments')
        assert response.status_code == 401


class TestPersonnelSkillsAPI:
    """Test cases for /api/personnel/skills endpoints."""

    def test_get_skills_success(self, client, auth, sample_skills):
        """Test successful retrieval of skills list."""
        auth.login()

        response = client.get('/api/personnel/skills')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'skills' in data['data']
        assert 'categories' in data['data']
        assert len(data['data']['skills']) > 0

        # Check skill structure
        skill = data['data']['skills'][0]
        required_fields = [
            'id', 'name', 'category', 'description', 'user_count', 'users'
        ]
        for field in required_fields:
            assert field in skill

    def test_get_skills_with_category_filter(self, client, auth, sample_skills):
        """Test skills list filtered by category."""
        auth.login()

        # Get first skill's category
        skill = sample_skills[0]
        category = skill.category

        response = client.get(f'/api/personnel/skills?category={category}')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # All skills should belong to the specified category
        for skill_data in data['data']['skills']:
            assert skill_data['category'] == category

    def test_get_skills_with_search(self, client, auth, sample_skills):
        """Test skills list with search parameter."""
        auth.login()

        # Search for a specific skill
        skill = sample_skills[0]
        search_term = skill.name[:3]  # First 3 characters

        response = client.get(f'/api/personnel/skills?search={search_term}')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # Check that search results contain the search term
        for skill_data in data['data']['skills']:
            skill_text = f"{skill_data['name']} {skill_data['description'] or ''}".lower()
            assert search_term.lower() in skill_text

    def test_get_skills_categories(self, client, auth, sample_skills):
        """Test that skills endpoint returns available categories."""
        auth.login()

        response = client.get('/api/personnel/skills')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'categories' in data['data']
        assert isinstance(data['data']['categories'], list)

        # Categories should match skills categories
        skill_categories = set(skill.category for skill in sample_skills if skill.category)
        returned_categories = set(data['data']['categories'])
        assert skill_categories.issubset(returned_categories)

    def test_get_skills_unauthorized(self, client, auth):
        """Test skills list access without authentication."""
        # Ensure user is logged out
        auth.logout()

        response = client.get('/api/personnel/skills')
        assert response.status_code == 401

    def test_get_skills_forbidden(self, client, auth):
        """Test skills list access without proper permissions."""
        # Login as user without VIEW_PERSONNEL_DATA permission
        auth.login(username='employee', password='password')

        response = client.get('/api/personnel/skills')
        assert response.status_code == 403
