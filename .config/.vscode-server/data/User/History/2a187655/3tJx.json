{"openapi": "3.0.0", "info": {"title": "DatPortal API", "description": "API RESTful per DatPortal - Gestione Progetti, Task, Risorse e KPI", "version": "1.0.0", "contact": {"name": "<PERSON>t<PERSON><PERSON><PERSON>", "url": "https://datvinci.com"}}, "servers": [{"url": "/api", "description": "<PERSON> di s<PERSON><PERSON><PERSON>"}], "tags": [{"name": "projects", "description": "Operazioni sui progetti"}, {"name": "tasks", "description": "Operazioni sui task"}, {"name": "resources", "description": "Operazioni sulle risorse di progetto"}, {"name": "dependencies", "description": "Operazioni sulle dipendenze tra task"}, {"name": "kpis", "description": "Operazioni sui KPI generali"}, {"name": "project-kpis", "description": "Operazioni sui KPI di progetto"}, {"name": "personnel", "description": "Operazioni su personale, dipartimenti e competenze"}, {"name": "dashboard", "description": "Operazioni per dashboard, statistiche e attività recenti"}], "paths": {"/projects/": {"get": {"tags": ["projects"], "summary": "<PERSON><PERSON>ene la lista dei progetti", "description": "Ottiene la lista dei progetti con supporto per filtri e paginazione.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "status", "in": "query", "description": "Filtra per stato del progetto", "schema": {"type": "string", "enum": ["planning", "active", "completed", "on-hold"]}}, {"name": "client_id", "in": "query", "description": "Filtra per ID cliente", "schema": {"type": "integer"}}, {"name": "search", "in": "query", "description": "Cerca nei nomi e nelle descrizioni dei progetti", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista di progetti", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"projects": {"type": "array", "items": {"$ref": "#/components/schemas/Project"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "post": {"tags": ["projects"], "summary": "Crea un nuovo progetto", "description": "Crea un nuovo progetto con i dati forniti.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Nome del progetto"}, "description": {"type": "string", "description": "Descrizione del progetto"}, "client_id": {"type": "integer", "description": "ID del cliente associato"}, "start_date": {"type": "string", "format": "date", "description": "Data di inizio (YYYY-MM-DD)"}, "end_date": {"type": "string", "format": "date", "description": "Data di fine (YYYY-MM-DD)"}, "status": {"type": "string", "enum": ["planning", "active", "completed", "on-hold"], "default": "planning", "description": "Stato del progetto"}, "budget": {"type": "number", "description": "Budget del progetto"}, "team_members": {"type": "array", "items": {"type": "integer"}, "description": "Lista di ID utenti da assegnare al progetto"}}}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> creato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"project": {"$ref": "#/components/schemas/Project"}}}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> creato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Nome del progetto obbligatorio"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/tasks/": {"get": {"tags": ["tasks"], "summary": "<PERSON><PERSON><PERSON> la lista dei task", "description": "Ottiene la lista dei task con supporto per filtri e paginazione.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "project_id", "in": "query", "description": "Filtra per ID progetto", "schema": {"type": "integer"}}, {"name": "status", "in": "query", "description": "Filtra per stato del task", "schema": {"type": "string", "enum": ["todo", "in-progress", "review", "done"]}}, {"name": "priority", "in": "query", "description": "Filtra per priorità del task", "schema": {"type": "string", "enum": ["low", "medium", "high", "urgent"]}}, {"name": "assignee_id", "in": "query", "description": "Filtra per ID dell'assegnatario", "schema": {"type": "integer"}}, {"name": "search", "in": "query", "description": "Cerca nei nomi e nelle descrizioni dei task", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista di task", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"$ref": "#/components/schemas/Task"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "post": {"tags": ["tasks"], "summary": "Crea un nuovo task", "description": "Crea un nuovo task con i dati forniti.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name", "project_id"], "properties": {"name": {"type": "string", "description": "Nome del task"}, "description": {"type": "string", "description": "Descrizione del task"}, "project_id": {"type": "integer", "description": "ID del progetto associato"}, "assignee_id": {"type": "integer", "description": "ID dell'utente assegnato al task"}, "status": {"type": "string", "enum": ["todo", "in-progress", "review", "done"], "default": "todo", "description": "Stato del task"}, "priority": {"type": "string", "enum": ["low", "medium", "high", "urgent"], "default": "medium", "description": "Priorità del task"}, "due_date": {"type": "string", "format": "date", "description": "Data di scadenza (YYYY-MM-DD)"}}}}}}, "responses": {"201": {"description": "Task creato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"task": {"$ref": "#/components/schemas/Task"}}}, "message": {"type": "string", "example": "Task creato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Nome del task e ID progetto obbligatori"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/resources/": {"get": {"tags": ["resources"], "summary": "Ottiene la lista delle risorse dei progetti", "description": "Ottiene la lista delle risorse dei progetti con supporto per filtri e paginazione.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "project_id", "in": "query", "description": "Filtra per ID progetto", "schema": {"type": "integer"}}, {"name": "user_id", "in": "query", "description": "Filtra per ID utente", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Lista di risorse", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"resources": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectResource"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "post": {"tags": ["resources"], "summary": "Assegna una nuova risorsa a un progetto", "description": "Assegna una nuova risorsa a un progetto.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["project_id", "user_id"], "properties": {"project_id": {"type": "integer", "description": "ID del progetto"}, "user_id": {"type": "integer", "description": "ID dell'utente da assegnare"}, "allocation_percentage": {"type": "integer", "description": "Percentuale di allocazione (1-100)", "default": 100}, "role": {"type": "string", "description": "Ruolo dell'utente nel progetto"}}}}}}, "responses": {"201": {"description": "Risorsa assegnata con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"resource": {"$ref": "#/components/schemas/ProjectResource"}}}, "message": {"type": "string", "example": "Risorsa assegnata con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "ID progetto e ID utente obbligatori"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/task-dependencies/": {"get": {"tags": ["task-dependencies"], "summary": "O<PERSON>ene la lista delle dipendenze tra task", "description": "Ottiene la lista delle dipendenze tra task con supporto per filtri e paginazione.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "task_id", "in": "query", "description": "Filtra per ID del task", "schema": {"type": "integer"}}, {"name": "depends_on_id", "in": "query", "description": "Filtra per ID del task dipendente", "schema": {"type": "integer"}}, {"name": "project_id", "in": "query", "description": "Filtra per ID del progetto", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Lista di dipendenze tra task", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"dependencies": {"type": "array", "items": {"$ref": "#/components/schemas/TaskDependency"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "post": {"tags": ["task-dependencies"], "summary": "Crea una nuova dipendenza tra task", "description": "Crea una nuova dipendenza tra task.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["task_id", "depends_on_id"], "properties": {"task_id": {"type": "integer", "description": "ID del task che dipende"}, "depends_on_id": {"type": "integer", "description": "ID del task da cui dipende"}}}}}}, "responses": {"201": {"description": "Dipendenza creata con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"dependency": {"$ref": "#/components/schemas/TaskDependency"}}}, "message": {"type": "string", "example": "Dipendenza creata con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "ID task e ID task dipendente obbligatori"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/kpis/": {"get": {"tags": ["kpis"], "summary": "Ottiene la lista dei KPI", "description": "Ottiene la lista dei KPI con supporto per filtri e paginazione.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "category", "in": "query", "description": "Filtra per categoria KPI", "schema": {"type": "string"}}, {"name": "frequency", "in": "query", "description": "Filtra per frequenza KPI", "schema": {"type": "string", "enum": ["daily", "weekly", "monthly", "quarterly", "annually"]}}, {"name": "search", "in": "query", "description": "Cerca nei nomi e descrizioni dei KPI", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista di KPI", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"kpis": {"type": "array", "items": {"$ref": "#/components/schemas/KPI"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "post": {"tags": ["kpis"], "summary": "Crea un nuovo KPI", "description": "Crea un nuovo KPI con i dati forniti.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Nome del KPI"}, "description": {"type": "string", "description": "Descrizione del KPI"}, "category": {"type": "string", "description": "Categoria del KPI"}, "target_value": {"type": "number", "description": "Valore target del KPI"}, "current_value": {"type": "number", "description": "Valore attuale del KPI", "default": 0}, "unit": {"type": "string", "description": "Unità di misura"}, "frequency": {"type": "string", "enum": ["daily", "weekly", "monthly", "quarterly", "annually"], "description": "Frequenza di misurazione"}}}}}}, "responses": {"201": {"description": "KPI creato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"kpi": {"$ref": "#/components/schemas/KPI"}}}, "message": {"type": "string", "example": "KPI creato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Nome del KPI obbligatorio"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/kpis/{kpi_id}": {"get": {"tags": ["kpis"], "summary": "<PERSON><PERSON><PERSON> i dettagli di un KPI", "description": "<PERSON><PERSON><PERSON> i dettagli di un KPI specifico.", "parameters": [{"name": "kpi_id", "in": "path", "required": true, "description": "ID del KPI", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettagli del KPI", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"kpi": {"$ref": "#/components/schemas/KPI"}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "put": {"tags": ["kpis"], "summary": "Aggiorna un KPI esistente", "description": "Aggiorna un KPI esistente.", "parameters": [{"name": "kpi_id", "in": "path", "required": true, "description": "ID del KPI da aggiornare", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "Nome del KPI"}, "description": {"type": "string", "description": "Descrizione del KPI"}, "category": {"type": "string", "description": "Categoria del KPI"}, "target_value": {"type": "number", "description": "Valore target del KPI"}, "current_value": {"type": "number", "description": "Valore attuale del KPI"}, "unit": {"type": "string", "description": "Unità di misura"}, "frequency": {"type": "string", "enum": ["daily", "weekly", "monthly", "quarterly", "annually"], "description": "Frequenza di misurazione"}}}}}}, "responses": {"200": {"description": "KPI aggiornato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"kpi": {"$ref": "#/components/schemas/KPI"}}}, "message": {"type": "string", "example": "KPI aggiornato con successo"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}, "delete": {"tags": ["kpis"], "summary": "Elimina un KPI", "description": "Elimina un KPI.", "parameters": [{"name": "kpi_id", "in": "path", "required": true, "description": "ID del KPI da eliminare", "schema": {"type": "integer"}}], "responses": {"200": {"description": "KPI eliminato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "KPI eliminato con successo"}}}}}}, "400": {"description": "KPI in uso, impossibile eliminare", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Impossibile eliminare KPI: è utilizzato in progetti"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/project-kpis/": {"get": {"tags": ["project-kpis"], "summary": "Ottiene la lista dei KPI di progetto", "description": "Ottiene la lista dei KPI di progetto con supporto per filtri e paginazione.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "project_id", "in": "query", "description": "Filtra per ID progetto", "schema": {"type": "integer"}}, {"name": "kpi_id", "in": "query", "description": "Filtra per ID KPI", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Lista di KPI di progetto", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"project_kpis": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectKPI"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "post": {"tags": ["project-kpis"], "summary": "Crea un nuovo KPI di progetto", "description": "Crea un nuovo KPI di progetto.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["project_id", "kpi_id"], "properties": {"project_id": {"type": "integer", "description": "ID del progetto"}, "kpi_id": {"type": "integer", "description": "ID del KPI"}, "target_value": {"type": "number", "description": "Valore target del KPI"}, "current_value": {"type": "number", "description": "Valore attuale del KPI", "default": 0}}}}}}, "responses": {"201": {"description": "KPI di progetto creato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"project_kpi": {"$ref": "#/components/schemas/ProjectKPI"}}}, "message": {"type": "string", "example": "KPI di progetto creato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "ID progetto e ID KPI obbligatori"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/project-kpis/{project_kpi_id}": {"get": {"tags": ["project-kpis"], "summary": "<PERSON><PERSON><PERSON> i dettagli di un KPI di progetto", "description": "<PERSON><PERSON><PERSON> i dettagli di un KPI di progetto specifico.", "parameters": [{"name": "project_kpi_id", "in": "path", "required": true, "description": "ID del KPI di progetto", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettagli del KPI di progetto", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"project_kpi": {"$ref": "#/components/schemas/ProjectKPI"}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "put": {"tags": ["project-kpis"], "summary": "Aggiorna un KPI di progetto esistente", "description": "Aggiorna un KPI di progetto esistente.", "parameters": [{"name": "project_kpi_id", "in": "path", "required": true, "description": "ID del KPI di progetto da aggiornare", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"target_value": {"type": "number", "description": "Valore target del KPI"}, "current_value": {"type": "number", "description": "Valore attuale del KPI"}}}}}}, "responses": {"200": {"description": "KPI di progetto aggiornato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"project_kpi": {"$ref": "#/components/schemas/ProjectKPI"}}}, "message": {"type": "string", "example": "KPI di progetto aggiornato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Dati non validi"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}, "delete": {"tags": ["project-kpis"], "summary": "Elimina un KPI di progetto", "description": "Elimina un KPI di progetto.", "parameters": [{"name": "project_kpi_id", "in": "path", "required": true, "description": "ID del KPI di progetto da eliminare", "schema": {"type": "integer"}}], "responses": {"200": {"description": "KPI di progetto eliminato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "KPI di progetto eliminato con successo"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/task-dependencies/{dependency_id}": {"get": {"tags": ["task-dependencies"], "summary": "<PERSON><PERSON><PERSON> i dettagli di una dipendenza", "description": "<PERSON><PERSON><PERSON> i dettagli di una dipendenza specifica.", "parameters": [{"name": "dependency_id", "in": "path", "required": true, "description": "ID della dipendenza", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettagli della dipendenza", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"dependency": {"$ref": "#/components/schemas/TaskDependency"}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "delete": {"tags": ["task-dependencies"], "summary": "Elimina una dipendenza", "description": "Elimina una dipendenza tra task.", "parameters": [{"name": "dependency_id", "in": "path", "required": true, "description": "ID della dipendenza da eliminare", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dipendenza eliminata con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Dipendenza eliminata con successo"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/resources/{resource_id}": {"get": {"tags": ["resources"], "summary": "<PERSON><PERSON><PERSON> i dettagli di una risorsa", "description": "<PERSON><PERSON><PERSON> i dettagli di una risorsa specifica.", "parameters": [{"name": "resource_id", "in": "path", "required": true, "description": "ID della risorsa", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettagli della risorsa", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"resource": {"$ref": "#/components/schemas/ProjectResource"}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "put": {"tags": ["resources"], "summary": "Aggiorna un'assegnazione di risorsa esistente", "description": "Aggiorna un'assegnazione di risorsa esistente.", "parameters": [{"name": "resource_id", "in": "path", "required": true, "description": "ID della risorsa da aggiornare", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"allocation_percentage": {"type": "integer", "description": "Percentuale di allocazione (1-100)"}, "role": {"type": "string", "description": "Ruolo dell'utente nel progetto"}}}}}}, "responses": {"200": {"description": "Risorsa aggiornata con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"resource": {"$ref": "#/components/schemas/ProjectResource"}}}, "message": {"type": "string", "example": "Risorsa aggiornata con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Dati non validi"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}, "delete": {"tags": ["resources"], "summary": "Rimuove un'assegnazione di risorsa", "description": "Rimuove un'assegnazione di risorsa.", "parameters": [{"name": "resource_id", "in": "path", "required": true, "description": "ID della risorsa da rimuovere", "schema": {"type": "integer"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> rimossa con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> rimossa con successo"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/tasks/status": {"patch": {"tags": ["tasks"], "summary": "Aggiorna lo stato di più task contemporaneamente", "description": "Aggiorna lo stato di più task contemporaneamente.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["tasks"], "properties": {"tasks": {"type": "array", "items": {"type": "object", "required": ["id", "status"], "properties": {"id": {"type": "integer", "description": "ID del task"}, "status": {"type": "string", "enum": ["todo", "in-progress", "review", "done"], "description": "Nuovo stato del task"}}}}}}}}}, "responses": {"200": {"description": "Stato dei task aggiornato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"task_id": {"type": "integer", "example": 1}, "success": {"type": "boolean", "example": true}, "old_status": {"type": "string", "example": "todo"}, "new_status": {"type": "string", "example": "in-progress"}, "message": {"type": "string", "example": "Stato aggiornato da 'todo' a 'in-progress'"}}}}, "message": {"type": "string", "example": "Aggiornamento completato: 3 task aggiornati, 0 falliti"}}}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Dati non validi: è richiesta una lista di task"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/tasks/{task_id}": {"get": {"tags": ["tasks"], "summary": "<PERSON><PERSON><PERSON> i dettagli di un task", "description": "<PERSON><PERSON><PERSON> i dettagli di un task specifico.", "parameters": [{"name": "task_id", "in": "path", "required": true, "description": "ID del task", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettagli del task", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"task": {"$ref": "#/components/schemas/Task"}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "put": {"tags": ["tasks"], "summary": "Aggiorna un task esistente", "description": "Aggiorna un task esistente con i dati forniti.", "parameters": [{"name": "task_id", "in": "path", "required": true, "description": "ID del task da aggiornare", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "Nome del task"}, "description": {"type": "string", "description": "Descrizione del task"}, "project_id": {"type": "integer", "description": "ID del progetto associato"}, "assignee_id": {"type": "integer", "description": "ID dell'utente assegnato al task"}, "status": {"type": "string", "enum": ["todo", "in-progress", "review", "done"], "description": "Stato del task"}, "priority": {"type": "string", "enum": ["low", "medium", "high", "urgent"], "description": "Priorità del task"}, "due_date": {"type": "string", "format": "date", "description": "Data di scadenza (YYYY-MM-DD)"}}}}}}, "responses": {"200": {"description": "Task aggiornato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"task": {"$ref": "#/components/schemas/Task"}}}, "message": {"type": "string", "example": "Task aggiornato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Dati non validi"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}, "delete": {"tags": ["tasks"], "summary": "Elimina un task esistente", "description": "Elimina un task esistente.", "parameters": [{"name": "task_id", "in": "path", "required": true, "description": "ID del task da eliminare", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Task eliminato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Task eliminato con successo"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/projects/batch": {"post": {"tags": ["projects"], "summary": "Esegue operazioni batch sui progetti", "description": "Esegue operazioni batch sui progetti.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["operations"], "properties": {"operations": {"type": "array", "items": {"type": "object", "required": ["operation"], "properties": {"operation": {"type": "string", "enum": ["create", "update", "delete"], "description": "Tipo di operazione da eseguire"}, "project_id": {"type": "integer", "description": "ID del progetto (richiesto per update e delete)"}, "data": {"type": "object", "description": "<PERSON>ti del progetto (richiesto per create e update)"}}}}}}}}}, "responses": {"200": {"description": "Operazioni batch eseguite con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"operation": {"type": "string", "example": "create"}, "success": {"type": "boolean", "example": true}, "project_id": {"type": "integer", "example": 1}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> creato con successo"}}}}, "message": {"type": "string", "example": "Operazioni batch eseguite con successo"}}}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Dati non validi"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/projects/{project_id}": {"get": {"tags": ["projects"], "summary": "<PERSON><PERSON><PERSON> i dettagli di un progetto", "description": "<PERSON><PERSON><PERSON> i dettagli di un progetto specifico.", "parameters": [{"name": "project_id", "in": "path", "required": true, "description": "ID del progetto", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettagli del progetto", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"project": {"$ref": "#/components/schemas/Project"}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "put": {"tags": ["projects"], "summary": "Aggiorna un progetto esistente", "description": "Aggiorna un progetto esistente con i dati forniti.", "parameters": [{"name": "project_id", "in": "path", "required": true, "description": "ID del progetto da aggiornare", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "Nome del progetto"}, "description": {"type": "string", "description": "Descrizione del progetto"}, "client_id": {"type": "integer", "description": "ID del cliente associato"}, "start_date": {"type": "string", "format": "date", "description": "Data di inizio (YYYY-MM-DD)"}, "end_date": {"type": "string", "format": "date", "description": "Data di fine (YYYY-MM-DD)"}, "status": {"type": "string", "enum": ["planning", "active", "completed", "on-hold"], "description": "Stato del progetto"}, "budget": {"type": "number", "description": "Budget del progetto"}, "team_members": {"type": "array", "items": {"type": "integer"}, "description": "Lista di ID utenti da assegnare al progetto"}}}}}}, "responses": {"200": {"description": "Progetto aggiornato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"project": {"$ref": "#/components/schemas/Project"}}}, "message": {"type": "string", "example": "Progetto aggiornato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Dati non validi"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}, "delete": {"tags": ["projects"], "summary": "Elimina un progetto esistente", "description": "Elimina un progetto esistente.", "parameters": [{"name": "project_id", "in": "path", "required": true, "description": "ID del progetto da eliminare", "schema": {"type": "integer"}}], "responses": {"200": {"description": "<PERSON><PERSON>to eliminato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON>to eliminato con successo"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/personnel/users": {"get": {"tags": ["personnel"], "summary": "<PERSON><PERSON><PERSON> la lista degli utenti", "description": "Ottiene la lista degli utenti con supporto per filtri, paginazione e ricerca.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "search", "in": "query", "description": "Cerca in nome, cognome, username, email", "schema": {"type": "string"}}, {"name": "department_id", "in": "query", "description": "Filtra per ID dipartimento", "schema": {"type": "integer"}}, {"name": "role", "in": "query", "description": "Filtra per ruolo utente", "schema": {"type": "string", "enum": ["admin", "manager", "employee", "human_resources"]}}, {"name": "is_active", "in": "query", "description": "Filtra per stato attivo", "schema": {"type": "boolean"}}, {"name": "skills", "in": "query", "description": "Filtra per competenze (ID separati da virgola)", "schema": {"type": "string"}}, {"name": "order_by", "in": "query", "description": "Campo per ordinamento", "schema": {"type": "string", "default": "last_name"}}, {"name": "order_dir", "in": "query", "description": "Direzione ordinamento", "schema": {"type": "string", "enum": ["asc", "desc"], "default": "asc"}}], "responses": {"200": {"description": "Lista utenti", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/UserSummary"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "message": {"type": "string", "example": "Retrieved 25 users"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/personnel/users/{user_id}": {"get": {"tags": ["personnel"], "summary": "<PERSON><PERSON><PERSON> i dettagli di un utente", "description": "Ottiene informazioni dettagliate su un utente specifico.", "parameters": [{"name": "user_id", "in": "path", "required": true, "description": "ID dell'utente", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettagli utente", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/UserDetail"}}}, "message": {"type": "string", "example": "Retrieved user <PERSON>"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/personnel/departments": {"get": {"tags": ["personnel"], "summary": "Ottiene la lista dei dipartimenti", "description": "O<PERSON>ene la lista dei dipartimenti con dati per l'organigramma.", "responses": {"200": {"description": "Lista dipartimenti", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"departments": {"type": "array", "items": {"$ref": "#/components/schemas/Department"}}}}, "message": {"type": "string", "example": "Retrieved 5 departments"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}}, "/personnel/skills": {"get": {"tags": ["personnel"], "summary": "O<PERSON>ene la lista delle competenze", "description": "Ottiene la lista delle competenze con statistiche di utilizzo.", "parameters": [{"name": "category", "in": "query", "description": "Filtra per categoria competenza", "schema": {"type": "string"}}, {"name": "search", "in": "query", "description": "Cerca in nome e descrizione competenze", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista competenze", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"skills": {"type": "array", "items": {"$ref": "#/components/schemas/Skill"}}, "categories": {"type": "array", "items": {"type": "string"}, "description": "Lista delle categorie disponibili"}}}, "message": {"type": "string", "example": "Retrieved 42 skills"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/dashboard/stats": {"get": {"tags": ["dashboard"], "summary": "<PERSON><PERSON><PERSON> le statistiche della dashboard", "description": "Ottiene statistiche aggregate per la dashboard principale.", "responses": {"200": {"description": "Statistiche dashboard", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"projects": {"type": "object", "properties": {"active": {"type": "integer"}, "total": {"type": "integer"}}}, "tasks": {"type": "object", "properties": {"total": {"type": "integer"}, "pending": {"type": "integer"}, "completed": {"type": "integer"}, "overdue": {"type": "integer"}}}, "team": {"type": "object", "properties": {"users": {"type": "integer"}, "departments": {"type": "integer"}, "clients": {"type": "integer"}}}, "activities": {"type": "object", "properties": {"recent_timesheets": {"type": "integer"}, "unread_notifications": {"type": "integer"}}}}}, "message": {"type": "string", "example": "Dashboard statistics retrieved successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}}, "/dashboard/recent-activities": {"get": {"tags": ["dashboard"], "summary": "<PERSON><PERSON><PERSON> le attività recenti", "description": "Ottiene la lista delle attività recenti per la dashboard.", "parameters": [{"name": "limit", "in": "query", "description": "Numero massimo di attività da restituire", "schema": {"type": "integer", "default": 10, "maximum": 50}}], "responses": {"200": {"description": "Lista attività recenti", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"activities": {"type": "array", "items": {"$ref": "#/components/schemas/DashboardActivity"}}}}, "message": {"type": "string", "example": "Retrieved 10 recent activities"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}}, "/dashboard/upcoming-tasks": {"get": {"tags": ["dashboard"], "summary": "<PERSON><PERSON><PERSON> i task in scadenza", "description": "<PERSON><PERSON><PERSON> la lista dei task in scadenza per la dashboard.", "parameters": [{"name": "days", "in": "query", "description": "Numero di giorni da considerare per le scadenze", "schema": {"type": "integer", "default": 7}}, {"name": "limit", "in": "query", "description": "Numero massimo di task da restituire", "schema": {"type": "integer", "default": 10}}], "responses": {"200": {"description": "Lista task in scadenza", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"$ref": "#/components/schemas/UpcomingTask"}}}}, "message": {"type": "string", "example": "Retrieved 5 upcoming tasks"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}}, "/dashboard/kpis": {"get": {"tags": ["dashboard"], "summary": "Ottiene i KPI per la dashboard", "description": "<PERSON>ttiene i KPI principali per la dashboard.", "parameters": [{"name": "category", "in": "query", "description": "Filtra per categoria KPI", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "description": "Numero massimo di KPI da restituire", "schema": {"type": "integer", "default": 6, "maximum": 20}}], "responses": {"200": {"description": "Lista KPI dashboard", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"kpis": {"type": "array", "items": {"$ref": "#/components/schemas/DashboardKPI"}}}}, "message": {"type": "string", "example": "Retrieved 6 KPIs"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}}}, "components": {"responses": {"Unauthorized": {"description": "Autenticazione richiesta", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}, "example": {"success": false, "message": "Autenticazione richiesta"}}}}, "Forbidden": {"description": "<PERSON><PERSON><PERSON> negato", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}, "example": {"success": false, "message": "Non hai i permessi necessari per accedere a questa risorsa"}}}}, "NotFound": {"description": "Risorsa non trovata", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}, "example": {"success": false, "message": "Risorsa non trovata"}}}}}, "schemas": {"Project": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "ID univoco del progetto"}, "name": {"type": "string", "description": "Nome del progetto"}, "description": {"type": "string", "description": "Descrizione del progetto"}, "client_id": {"type": "integer", "format": "int64", "nullable": true, "description": "ID del cliente associato al progetto"}, "start_date": {"type": "string", "format": "date", "nullable": true, "description": "Data di inizio del progetto (YYYY-MM-DD)"}, "end_date": {"type": "string", "format": "date", "nullable": true, "description": "Data di fine del progetto (YYYY-MM-DD)"}, "status": {"type": "string", "enum": ["planning", "active", "completed", "on-hold"], "description": "Stato del progetto"}, "budget": {"type": "number", "format": "float", "nullable": true, "description": "Budget del progetto"}, "expenses": {"type": "number", "format": "float", "description": "Spese attuali del progetto"}, "created_at": {"type": "string", "format": "date-time", "description": "Data e ora di creazione del progetto"}, "updated_at": {"type": "string", "format": "date-time", "description": "Data e ora dell'ultimo aggiornamento del progetto"}}, "required": ["name"]}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "<PERSON><PERSON><PERSON> corrente"}, "per_page": {"type": "integer", "description": "Elementi per pagina"}, "total": {"type": "integer", "description": "Numero totale di elementi"}, "pages": {"type": "integer", "description": "Numero totale di pagine"}, "has_next": {"type": "boolean", "description": "Indica se esiste una pagina successiva"}, "has_prev": {"type": "boolean", "description": "Indica se esiste una pagina precedente"}}}, "KPI": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID del KPI"}, "name": {"type": "string", "description": "Nome del KPI"}, "description": {"type": "string", "description": "Descrizione del KPI"}, "category": {"type": "string", "description": "Categoria del KPI"}, "target_value": {"type": "number", "description": "Valore target del KPI"}, "current_value": {"type": "number", "description": "Valore attuale del KPI"}, "unit": {"type": "string", "description": "Unità di misura"}, "frequency": {"type": "string", "enum": ["daily", "weekly", "monthly", "quarterly", "annually"], "description": "Frequenza di misurazione"}, "progress": {"type": "number", "description": "Percentuale di completamento del KPI"}, "created_at": {"type": "string", "format": "date-time", "description": "Data di creazione"}, "updated_at": {"type": "string", "format": "date-time", "description": "Data di ultimo aggiornamento"}}}, "ProjectKPI": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID del KPI di progetto"}, "project_id": {"type": "integer", "description": "ID del progetto"}, "kpi_id": {"type": "integer", "description": "ID del KPI"}, "target_value": {"type": "number", "description": "Valore target del KPI"}, "current_value": {"type": "number", "description": "Valore attuale del KPI"}, "project_name": {"type": "string", "description": "Nome del progetto"}, "kpi_name": {"type": "string", "description": "Nome del KPI"}, "kpi_unit": {"type": "string", "description": "Unità di misura del KPI"}, "progress": {"type": "number", "description": "Percentuale di completamento del KPI"}}}, "TaskDependency": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID della dipendenza"}, "task_id": {"type": "integer", "description": "ID del task che dipende"}, "depends_on_id": {"type": "integer", "description": "ID del task da cui dipende"}, "task_name": {"type": "string", "description": "Nome del task che dipende"}, "depends_on_name": {"type": "string", "description": "Nome del task da cui dipende"}}}, "ProjectResource": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID della risorsa"}, "project_id": {"type": "integer", "description": "ID del progetto"}, "user_id": {"type": "integer", "description": "ID dell'utente"}, "allocation_percentage": {"type": "integer", "description": "Percentuale di allocazione (1-100)"}, "role": {"type": "string", "description": "Ruolo dell'utente nel progetto"}, "project_name": {"type": "string", "description": "Nome del progetto"}, "user_name": {"type": "string", "description": "Nome dell'utente"}}}, "Task": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "ID univoco del task"}, "name": {"type": "string", "description": "Nome del task"}, "description": {"type": "string", "nullable": true, "description": "Descrizione del task"}, "project_id": {"type": "integer", "format": "int64", "description": "ID del progetto a cui appartiene il task"}, "assignee_id": {"type": "integer", "format": "int64", "nullable": true, "description": "ID dell'utente assegnato al task"}, "status": {"type": "string", "enum": ["todo", "in-progress", "review", "done"], "description": "Stato del task"}, "priority": {"type": "string", "enum": ["low", "medium", "high", "urgent"], "description": "Priorità del task"}, "due_date": {"type": "string", "format": "date", "nullable": true, "description": "Data di scadenza del task (YYYY-MM-DD)"}, "created_at": {"type": "string", "format": "date-time", "description": "Data e ora di creazione del task"}, "updated_at": {"type": "string", "format": "date-time", "description": "Data e ora dell'ultimo aggiornamento del task"}}, "required": ["name", "project_id"]}, "Error": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}}}, "UserSummary": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID dell'utente"}, "username": {"type": "string", "description": "Nome utente"}, "email": {"type": "string", "description": "Email dell'utente"}, "first_name": {"type": "string", "description": "Nome"}, "last_name": {"type": "string", "description": "Cognome"}, "full_name": {"type": "string", "description": "Nome completo"}, "role": {"type": "string", "enum": ["admin", "manager", "employee", "human_resources"], "description": "Ruolo dell'utente"}, "department_id": {"type": "integer", "nullable": true, "description": "ID del dipartimento"}, "department_name": {"type": "string", "nullable": true, "description": "Nome del dipartimento"}, "position": {"type": "string", "nullable": true, "description": "Posizione lavorativa"}, "hire_date": {"type": "string", "format": "date", "nullable": true, "description": "Data di assunzione"}, "phone": {"type": "string", "nullable": true, "description": "Numero di telefono"}, "profile_image": {"type": "string", "nullable": true, "description": "URL immagine profilo"}, "is_active": {"type": "boolean", "description": "Stato attivo dell'utente"}, "last_login": {"type": "string", "format": "date-time", "nullable": true, "description": "Ultimo accesso"}, "skills": {"type": "array", "items": {"$ref": "#/components/schemas/UserSkill"}, "description": "Competenze dell'utente"}, "profile_completion": {"type": "number", "description": "Percentuale completamento profilo"}}}, "UserDetail": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID dell'utente"}, "username": {"type": "string", "description": "Nome utente"}, "email": {"type": "string", "description": "Email dell'utente"}, "first_name": {"type": "string", "description": "Nome"}, "last_name": {"type": "string", "description": "Cognome"}, "full_name": {"type": "string", "description": "Nome completo"}, "role": {"type": "string", "enum": ["admin", "manager", "employee", "human_resources"], "description": "Ruolo dell'utente"}, "department_id": {"type": "integer", "nullable": true, "description": "ID del dipartimento"}, "department": {"$ref": "#/components/schemas/Department", "nullable": true, "description": "Dettagli del dipartimento"}, "position": {"type": "string", "nullable": true, "description": "Posizione lavorativa"}, "hire_date": {"type": "string", "format": "date", "nullable": true, "description": "Data di assunzione"}, "phone": {"type": "string", "nullable": true, "description": "Numero di telefono"}, "profile_image": {"type": "string", "nullable": true, "description": "URL immagine profilo"}, "bio": {"type": "string", "nullable": true, "description": "Biografia dell'utente"}, "is_active": {"type": "boolean", "description": "Stato attivo dell'utente"}, "dark_mode": {"type": "boolean", "description": "Preferenza modalità scura"}, "created_at": {"type": "string", "format": "date-time", "description": "Data di creazione"}, "last_login": {"type": "string", "format": "date-time", "nullable": true, "description": "Ultimo accesso"}, "skills": {"type": "array", "items": {"$ref": "#/components/schemas/UserSkillDetail"}, "description": "Competenze dettagliate dell'utente"}, "projects": {"type": "array", "items": {"$ref": "#/components/schemas/UserProject"}, "description": "Progetti dell'utente"}, "profile": {"$ref": "#/components/schemas/UserProfile", "nullable": true, "description": "Profilo esteso dell'utente"}}}, "Department": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID del dipartimento"}, "name": {"type": "string", "description": "Nome del dipartimento"}, "description": {"type": "string", "nullable": true, "description": "Descrizione del dipartimento"}, "manager_id": {"type": "integer", "nullable": true, "description": "ID del manager del dipartimento"}, "manager": {"$ref": "#/components/schemas/UserSummary", "nullable": true, "description": "Manager del dipartimento"}, "user_count": {"type": "integer", "description": "Numero di utenti nel dipartimento"}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/UserSummary"}, "description": "Utenti del dipartimento"}, "created_at": {"type": "string", "format": "date-time", "description": "Data di creazione"}}}, "Skill": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID della competenza"}, "name": {"type": "string", "description": "Nome della competenza"}, "category": {"type": "string", "description": "Categoria della competenza"}, "description": {"type": "string", "nullable": true, "description": "Descrizione della competenza"}, "user_count": {"type": "integer", "description": "Numero di utenti con questa competenza"}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/UserSkill"}, "description": "Utenti con questa competenza"}}}, "UserSkill": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID della competenza"}, "name": {"type": "string", "description": "Nome della competenza"}, "category": {"type": "string", "description": "Categoria della competenza"}, "proficiency_level": {"type": "integer", "minimum": 1, "maximum": 5, "description": "Livello di competenza (1-5)"}, "years_experience": {"type": "integer", "nullable": true, "description": "Anni di esperienza"}}}, "UserSkillDetail": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID della competenza"}, "name": {"type": "string", "description": "Nome della competenza"}, "category": {"type": "string", "description": "Categoria della competenza"}, "description": {"type": "string", "nullable": true, "description": "Descrizione della competenza"}, "proficiency_level": {"type": "integer", "minimum": 1, "maximum": 5, "description": "Livello di competenza (1-5)"}, "years_experience": {"type": "integer", "nullable": true, "description": "Anni di esperienza"}, "certified": {"type": "boolean", "description": "Certificazione ottenuta"}, "last_used": {"type": "string", "format": "date", "nullable": true, "description": "Data certificazione"}}}, "UserProject": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID del progetto"}, "name": {"type": "string", "description": "Nome del progetto"}, "status": {"type": "string", "enum": ["planning", "active", "completed", "on-hold"], "description": "Stato del progetto"}, "role": {"type": "string", "description": "Ruolo dell'utente nel progetto"}}}, "UserProfile": {"type": "object", "properties": {"emergency_contact_name": {"type": "string", "nullable": true, "description": "Nome contatto di emergenza"}, "emergency_contact_phone": {"type": "string", "nullable": true, "description": "Telefono contatto di emergenza"}, "address": {"type": "string", "nullable": true, "description": "<PERSON><PERSON><PERSON><PERSON>"}, "profile_completion": {"type": "number", "description": "Percentuale completamento profilo"}, "notes": {"type": "string", "nullable": true, "description": "Note (solo per admin/HR)"}}}, "DashboardActivity": {"type": "object", "properties": {"type": {"type": "string", "enum": ["task", "timesheet", "event"], "description": "Tipo di attività"}, "id": {"type": "integer", "description": "ID dell'attività"}, "title": {"type": "string", "description": "Titolo dell'attività"}, "description": {"type": "string", "description": "Descrizione dell'attività"}, "timestamp": {"type": "string", "format": "date-time", "description": "Timestamp dell'attività"}, "link": {"type": "string", "description": "Link per visualiz<PERSON>e l'attività"}}}, "UpcomingTask": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID del task"}, "name": {"type": "string", "description": "Nome del task"}, "description": {"type": "string", "nullable": true, "description": "Descrizione del task"}, "project_id": {"type": "integer", "description": "ID del progetto"}, "project_name": {"type": "string", "description": "Nome del progetto"}, "assignee_id": {"type": "integer", "nullable": true, "description": "ID dell'assegnatario"}, "assignee_name": {"type": "string", "nullable": true, "description": "Nome dell'assegnatario"}, "status": {"type": "string", "enum": ["todo", "in-progress", "review", "done"], "description": "Stato del task"}, "priority": {"type": "string", "enum": ["low", "medium", "high", "urgent"], "description": "Priorità del task"}, "due_date": {"type": "string", "format": "date", "description": "Data di scadenza"}, "days_until_due": {"type": "integer", "description": "<PERSON><PERSON><PERSON> alla scadenza"}, "estimated_hours": {"type": "number", "nullable": true, "description": "Ore stimate"}, "is_overdue": {"type": "boolean", "description": "Indica se il task è in ritardo"}, "link": {"type": "string", "description": "Link per visualizzare il task"}}}, "DashboardKPI": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID del KPI"}, "name": {"type": "string", "description": "Nome del KPI"}, "category": {"type": "string", "description": "Categoria del KPI"}, "description": {"type": "string", "nullable": true, "description": "Descrizione del KPI"}, "current_value": {"type": "number", "description": "<PERSON><PERSON> at<PERSON>"}, "target_value": {"type": "number", "description": "Valore target"}, "unit": {"type": "string", "nullable": true, "description": "Unità di misura"}, "trend": {"type": "string", "enum": ["up", "down", "stable"], "nullable": true, "description": "Trend del KPI"}, "last_updated": {"type": "string", "format": "date-time", "nullable": true, "description": "Ultimo aggiornamento"}, "performance_percentage": {"type": "number", "description": "Percentuale di performance rispetto al target"}}}}, "parameters": {"pageParam": {"name": "page", "in": "query", "description": "Numero di pagina", "schema": {"type": "integer", "default": 1, "minimum": 1}}, "perPageParam": {"name": "per_page", "in": "query", "description": "Elementi per pagina", "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 100}}}, "securitySchemes": {"cookieAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "cookie", "name": "session"}}}, "security": [{"cookieAuth": []}]}