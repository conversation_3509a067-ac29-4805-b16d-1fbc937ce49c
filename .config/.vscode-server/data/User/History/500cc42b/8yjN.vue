<template>
  <div
    id="app"
    class="min-h-screen bg-brand-bg-secondary dark:bg-gray-900 text-brand-text-primary transition-brand"
    :class="{ 'dark': appStore.darkMode }"
  >
    <!-- Loading Overlay -->
    <LoadingOverlay v-if="appStore.isLoading" :message="appStore.loadingMessage" />

    <!-- Private Layout (Authenticated) -->
    <div v-if="isPrivateRoute" class="flex h-screen overflow-hidden">
      <!-- Sidebar -->
      <Sidebar />

      <!-- Main Content Area -->
      <div class="flex flex-col flex-1 overflow-y-auto">
        <!-- Top Navigation -->
        <TopNavigation />

        <!-- Page Content -->
        <main class="flex-grow p-4 md:p-6 lg:p-8" id="main-content">
          <!-- Breadcrumbs -->
          <Breadcrumbs v-if="appStore.breadcrumbs.length > 0" :items="appStore.breadcrumbs" />

          <!-- Router View with Transitions -->
          <router-view v-slot="{ Component, route }">
            <transition
              :name="getTransitionName(route)"
              mode="out-in"
              @enter="onPageEnter"
              @leave="onPageLeave"
            >
              <component :is="Component" :key="route.path" />
            </transition>
          </router-view>
        </main>

        <!-- Footer -->
        <AppFooter />
      </div>
    </div>

    <!-- Public Layout (Not Authenticated) -->
    <div v-else class="min-h-screen bg-brand-bg-primary">
      <!-- Router View for Public Pages -->
      <router-view v-slot="{ Component, route }">
        <transition
          :name="getTransitionName(route)"
          mode="out-in"
          @enter="onPageEnter"
          @leave="onPageLeave"
        >
          <component :is="Component" :key="route.path" />
        </transition>
      </router-view>
    </div>

    <!-- Global Notifications -->
    <NotificationContainer />

    <!-- Global Modals -->
    <ModalContainer />

    <!-- Offline Indicator -->
    <OfflineIndicator v-if="!appStore.isOnline" />
  </div>
</template>

<script setup>
import { onMounted, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'
import { useAppStore } from '../stores/app.js'
import { useBrandStore } from '../stores/brand.js'

// Components
import Sidebar from './layout/Sidebar.vue'
import TopNavigation from './layout/TopNavigation.vue'
import AppFooter from './layout/AppFooter.vue'
import Breadcrumbs from './layout/Breadcrumbs.vue'
import LoadingOverlay from './ui/LoadingOverlay.vue'
import NotificationContainer from './ui/NotificationContainer.vue'
import ModalContainer from './ui/ModalContainer.vue'
import OfflineIndicator from './ui/OfflineIndicator.vue'

// Stores
const authStore = useAuthStore()
const appStore = useAppStore()
const brandStore = useBrandStore()
const router = useRouter()
const route = useRoute()

// Computed
const isPrivateRoute = computed(() => {
  return route.meta?.layout === 'private' && authStore.isAuthenticated
})

// Page transition logic
function getTransitionName(route) {
  // Different transitions based on route depth or type
  if (route.meta?.transition) {
    return route.meta.transition
  }

  // Public pages use fade, private pages use slide
  if (route.meta?.layout === 'public') {
    return 'page-fade'
  }

  // Default transitions
  const depth = route.path.split('/').length
  if (depth <= 2) {
    return 'page-slide'
  } else {
    return 'page-fade'
  }
}

function onPageEnter() {
  // Page enter animation complete
  nextTick(() => {
    // Scroll to top
    const mainContent = document.getElementById('main-content')
    if (mainContent) {
      mainContent.scrollTop = 0
    }
  })
}

function onPageLeave() {
  // Page leave animation starting
}

// Watch route changes to update app state
watch(route, (newRoute) => {
  // Update page info
  appStore.setCurrentPage(
    newRoute.name,
    newRoute.meta?.title || 'DatPortal',
    newRoute.meta?.breadcrumbs || []
  )

  // Update last activity
  authStore.updateLastActivity()
}, { immediate: true })

// Session check interval
let sessionCheckInterval = null

function startSessionCheck() {
  // Check session every 5 minutes
  sessionCheckInterval = setInterval(async () => {
    if (authStore.isAuthenticated) {
      const isValid = await authStore.checkSession()
      if (!isValid) {
        appStore.showNotification(
          'Sessione scaduta. Effettua nuovamente il login.',
          'warning',
          0
        )
        setTimeout(() => {
          authStore.logout()
        }, 3000)
      }
    }
  }, 5 * 60 * 1000) // 5 minutes
}

function stopSessionCheck() {
  if (sessionCheckInterval) {
    clearInterval(sessionCheckInterval)
    sessionCheckInterval = null
  }
}

// Lifecycle
onMounted(async () => {
  try {
    // Initialize brand system
    await brandStore.loadBrandConfig()

    // Load user data if authenticated
    if (window.APP_CONFIG.isAuthenticated) {
      await authStore.loadCurrentUser()
      startSessionCheck()
    } else {
      // Redirect to login if not authenticated
      window.location.href = '/auth/login'
      return
    }

    // Mark app as initialized
    appStore.setInitialized(true)

    // Show welcome notification
    if (authStore.userFullName) {
      appStore.showNotification(
        `Benvenuto, ${authStore.userFullName}!`,
        'success',
        3000
      )
    }

  } catch (error) {
    console.error('App initialization failed:', error)
    appStore.showNotification(
      'Errore durante l\'inizializzazione dell\'applicazione',
      'error',
      0
    )
  }
})

// Cleanup on unmount
onUnmounted(() => {
  stopSessionCheck()
})

// Handle browser back/forward
window.addEventListener('popstate', () => {
  authStore.updateLastActivity()
})

// Handle visibility change (tab focus/blur)
document.addEventListener('visibilitychange', () => {
  if (!document.hidden && authStore.isAuthenticated) {
    // Tab became visible, check session
    authStore.checkSession()
    authStore.updateLastActivity()
  }
})
</script>

<style scoped>
/* Page Transitions */
.page-slide-enter-active,
.page-slide-leave-active {
  transition: all var(--brand-transition-normal);
}

.page-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.page-fade-enter-active,
.page-fade-leave-active {
  transition: opacity var(--brand-transition-fast);
}

.page-fade-enter-from,
.page-fade-leave-to {
  opacity: 0;
}

/* Loading state */
.page-loading {
  pointer-events: none;
}

.page-loading * {
  cursor: wait !important;
}

/* Smooth scrolling */
#main-content {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
#main-content::-webkit-scrollbar {
  width: 6px;
}

#main-content::-webkit-scrollbar-track {
  background: var(--brand-bg-tertiary);
}

#main-content::-webkit-scrollbar-thumb {
  background: var(--brand-border-secondary);
  border-radius: var(--brand-radius-full);
}

#main-content::-webkit-scrollbar-thumb:hover {
  background: var(--brand-primary-400);
}

/* Focus management */
.router-link-active {
  @apply text-brand-primary-600 dark:text-brand-primary-400;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .page-slide-enter-active,
  .page-slide-leave-active,
  .page-fade-enter-active,
  .page-fade-leave-active {
    transition: none;
  }

  .animate-spin {
    animation: none;
  }
}

/* Print styles */
@media print {
  .sidebar,
  .top-navigation,
  .app-footer,
  .notification-container,
  .modal-container {
    display: none !important;
  }

  #main-content {
    margin: 0 !important;
    padding: 0 !important;
  }
}
</style>
