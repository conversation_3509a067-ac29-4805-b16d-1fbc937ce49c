/**
 * Vue.js 3 Main Application Entry Point
 * DatPortal SPA with Brand Management System
 */

import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'

// Import stores
import { useBrandStore } from './stores/brand.js'
import { useAuthStore } from './stores/auth.js'
import { useAppStore } from './stores/app.js'

// Import components
import App from './components/App.vue'

// Import routes
import { routes } from './router/index.js'

// === ROUTER SETUP ===
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Router guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  const appStore = useAppStore()

  // Show loading
  appStore.setLoading(true)

  // Check authentication for protected routes
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    // Redirect to login
    window.location.href = '/auth/login'
    return
  }

  // Check permissions for authenticated routes
  if (to.meta.permissions && authStore.isAuthenticated && !authStore.hasPermission(to.meta.permissions)) {
    // Redirect to dashboard if no permissions
    next({ name: 'dashboard' })
    return
  }

  next()
})

router.afterEach(() => {
  const appStore = useAppStore()
  // Hide loading after a short delay to prevent flashing
  setTimeout(() => {
    appStore.setLoading(false)
  }, 100)
})

// === PINIA SETUP ===
const pinia = createPinia()

// === VUE APP SETUP ===
const app = createApp(App)

// Use plugins
app.use(pinia)
app.use(router)

// Global properties
app.config.globalProperties.$brand = useBrandStore()
app.config.globalProperties.$auth = useAuthStore()
app.config.globalProperties.$app = useAppStore()

// Global error handler
app.config.errorHandler = (error, instance, info) => {
  console.error('Vue Error:', error)
  console.error('Component:', instance)
  console.error('Info:', info)

  // Send to error reporting service if available
  if (window.errorReporting) {
    window.errorReporting.captureException(error, {
      extra: { info, component: instance?.$options.name }
    })
  }
}

// Global warning handler (development only)
if (window.APP_CONFIG.debug) {
  app.config.warnHandler = (msg, instance, trace) => {
    console.warn('Vue Warning:', msg)
    console.warn('Trace:', trace)
  }
}

// === GLOBAL DIRECTIVES ===

// v-focus directive for auto-focusing elements
app.directive('focus', {
  mounted(el) {
    el.focus()
  }
})

// v-click-outside directive
app.directive('click-outside', {
  beforeMount(el, binding) {
    el.clickOutsideEvent = function(event) {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value(event)
      }
    }
    document.addEventListener('click', el.clickOutsideEvent)
  },
  unmounted(el) {
    document.removeEventListener('click', el.clickOutsideEvent)
  }
})

// v-tooltip directive (simple tooltip)
app.directive('tooltip', {
  beforeMount(el, binding) {
    el.setAttribute('title', binding.value)
    el.style.position = 'relative'
  },
  updated(el, binding) {
    el.setAttribute('title', binding.value)
  }
})

// === GLOBAL MIXINS ===

// Global mixin for common functionality
app.mixin({
  computed: {
    $isMobile() {
      return window.innerWidth < 768
    },
    $isTablet() {
      return window.innerWidth >= 768 && window.innerWidth < 1024
    },
    $isDesktop() {
      return window.innerWidth >= 1024
    }
  },
  methods: {
    // Format date utility
    $formatDate(date, options = {}) {
      if (!date) return ''
      const defaultOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        ...options
      }
      return new Date(date).toLocaleDateString('it-IT', defaultOptions)
    },

    // Format currency utility
    $formatCurrency(amount, currency = 'EUR') {
      if (amount === null || amount === undefined) return ''
      return new Intl.NumberFormat('it-IT', {
        style: 'currency',
        currency: currency
      }).format(amount)
    },

    // Debounce utility
    $debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    },

    // Copy to clipboard utility
    async $copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        this.$app.showNotification('Copiato negli appunti', 'success')
        return true
      } catch (error) {
        console.error('Failed to copy to clipboard:', error)
        this.$app.showNotification('Errore durante la copia', 'error')
        return false
      }
    },

    // Download file utility
    $downloadFile(url, filename) {
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
})

// === APP INITIALIZATION ===

async function initializeApp() {
  try {
    // Initialize stores
    const brandStore = useBrandStore()
    const authStore = useAuthStore()
    const appStore = useAppStore()

    // Load brand configuration
    await brandStore.loadBrandConfig()

    // Load user data if authenticated
    if (window.APP_CONFIG.isAuthenticated) {
      await authStore.loadCurrentUser()
    }

    // Set initial app state
    appStore.setInitialized(true)

    console.log('🎨 DatPortal Vue.js SPA initialized successfully')
    console.log('📱 Brand system loaded')
    console.log('🔐 Authentication state:', authStore.isAuthenticated)

  } catch (error) {
    console.error('Failed to initialize app:', error)

    // Show error to user
    const appStore = useAppStore()
    appStore.showNotification(
      'Errore durante l\'inizializzazione dell\'applicazione',
      'error'
    )
  }
}

// === MOUNT APP ===

// Wait for DOM to be ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', async () => {
    app.mount('#app')
    await initializeApp()
  })
} else {
  app.mount('#app')
  initializeApp()
}

// === DEVELOPMENT HELPERS ===

if (window.APP_CONFIG.debug) {
  // Make stores available globally for debugging
  window.stores = {
    brand: useBrandStore(),
    auth: useAuthStore(),
    app: useAppStore()
  }

  // Make Vue app available globally
  window.vueApp = app

  console.log('🔧 Development mode: stores and app available on window object')
}

// === HOT MODULE REPLACEMENT (for development) ===

if (import.meta.hot) {
  import.meta.hot.accept()

  // Handle HMR for stores
  import.meta.hot.accept('./stores/brand.js', (newModule) => {
    console.log('🔄 Hot reloading brand store')
  })

  import.meta.hot.accept('./stores/auth.js', (newModule) => {
    console.log('🔄 Hot reloading auth store')
  })

  import.meta.hot.accept('./stores/app.js', (newModule) => {
    console.log('🔄 Hot reloading app store')
  })
}

export default app
