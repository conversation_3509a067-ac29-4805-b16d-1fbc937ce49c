from flask import Blueprint, render_template

landing_bp = Blueprint('landing', __name__)

@landing_bp.route('/')
def home():
    """Home page - serve Vue.js SPA"""
    return render_template('spa.html')

@landing_bp.route('/services')
@landing_bp.route('/services/<path:path>')
def services():
    """Services pages - serve Vue.js SPA"""
    return render_template('spa.html')

@landing_bp.route('/about')
def about():
    """About page - serve Vue.js SPA"""
    return render_template('spa.html')

@landing_bp.route('/contact')
def contact():
    """Contact page - serve Vue.js SPA"""
    return render_template('spa.html')

@landing_bp.route('/privacy')
def privacy():
    """Privacy page - serve Vue.js SPA"""
    return render_template('spa.html')
