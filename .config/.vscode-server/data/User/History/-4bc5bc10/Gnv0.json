{"remote.defaultExtensionsIfInstalledLocally": ["GitHub.copilot", "GitHub.copilot-chat", "GitHub.vscode-pull-request-github"], "github.copilot.enable": {"*": false}, "augment.chat.userGuidelines": "check documentation and product status in /specs or /tasks o /scripts\nkeep updated the plan, but ask before changing status of tasks.\n\nTU NON PUOI DIRE PAROLACCE!", "security.promptForRemoteFileProtocolHandling": false}