{"version": 1, "resource": "vscode-remote://<EMAIL>:22/home/<USER>/workspace/blueprints/api/personnel.py", "entries": [{"id": "TdwR.py", "source": "Workspace Edit", "timestamp": 1748453218143}, {"id": "Q6JK.py", "source": "Workspace Edit", "timestamp": 1748453257494}, {"id": "kALM.py", "source": "Workspace Edit", "timestamp": 1748453424573}, {"id": "EZV8.py", "source": "Workspace Edit", "timestamp": 1748453438552}, {"id": "Ruad.py", "source": "Workspace Edit", "timestamp": 1748453450863}, {"id": "BGVs.py", "source": "Workspace Edit", "timestamp": 1748453462907}, {"id": "HNQ3.py", "source": "Workspace Edit", "timestamp": 1748453476306}, {"id": "hMXC.py", "source": "Workspace Edit", "timestamp": 1748453519409}, {"id": "A09Z.py", "source": "Workspace Edit", "timestamp": 1748453533997}, {"id": "nBZM.py", "source": "Workspace Edit", "timestamp": 1748453546696}, {"id": "JXAC.py", "source": "Workspace Edit", "timestamp": 1748453559223}]}