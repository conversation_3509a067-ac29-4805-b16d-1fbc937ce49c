from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, session
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta
import secrets
import time

from app import db
from models import User
from utils.token_utils import generate_reset_token, verify_reset_token
from utils.email_utils import send_password_reset_email

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = True if request.form.get('remember') else False

        user = User.query.filter_by(username=username).first()

        if not user or not user.check_password(password):
            flash('Verifica nome utente e password e riprova.', 'error')
            return redirect(url_for('auth.login'))

        if not user.is_active:
            flash('Il tuo account è disattivato. Contatta l\'amministratore.', 'error')
            return redirect(url_for('auth.login'))

        # Login successful, update last login time
        user.last_login = datetime.utcnow()
        db.session.commit()

        login_user(user, remember=remember)
        session['login_time'] = time.time()
        session['last_activity'] = time.time()

        # Redirect to the page user was trying to access or dashboard
        next_page = request.args.get('next')
        if next_page:
            return redirect(next_page)
        return redirect(url_for('dashboard.index'))

    return render_template('spa.html')

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')

        # Validate input
        if not username or not email or not password or not confirm_password:
            flash('Tutti i campi sono obbligatori', 'error')
            return redirect(url_for('auth.register'))

        if password != confirm_password:
            flash('Le password non corrispondono', 'error')
            return redirect(url_for('auth.register'))

        # Check if username or email already exists
        if User.query.filter_by(username=username).first():
            flash('Username già in uso', 'error')
            return redirect(url_for('auth.register'))

        if User.query.filter_by(email=email).first():
            flash('Email già registrata', 'error')
            return redirect(url_for('auth.register'))

        # Create new user - inactive by default until approved
        new_user = User(
            username=username,
            email=email,
            first_name=first_name,
            last_name=last_name,
            is_active=False  # Admin needs to activate
        )
        new_user.set_password(password)

        try:
            db.session.add(new_user)
            db.session.commit()
            flash('Account creato con successo! Un amministratore attiverà il tuo account a breve.', 'success')
            return redirect(url_for('auth.login'))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Registration error: {str(e)}")
            flash('Si è verificato un errore durante la registrazione. Riprova più tardi.', 'error')
            return redirect(url_for('auth.register'))

    return render_template('spa.html')

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash('Disconnessione effettuata con successo', 'success')
    return redirect(url_for('landing.home'))

@auth_bp.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    if request.method == 'POST':
        email = request.form.get('email')
        user = User.query.filter_by(email=email).first()
        if user:
            token = generate_reset_token()
            expiry_time = datetime.utcnow() + timedelta(seconds=current_app.config.get('PASSWORD_RESET_TOKEN_EXPIRATION_SECONDS', 3600))
            user.reset_token = token
            user.reset_token_expiry = expiry_time
            db.session.commit()

            send_password_reset_email(user.email, user.username, token)
            flash('Se l\'indirizzo email è nel nostro database, riceverai un link per il reset della password.', 'info')
        else:
            flash('Se l\'indirizzo email è nel nostro database, riceverai un link per il reset della password.', 'info')
        return redirect(url_for('auth.login'))
    return render_template('spa.html')

@auth_bp.route('/reset-password/<token>', methods=['GET', 'POST'])
def reset_password_with_token(token):
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))

    user = verify_reset_token(token)

    if not user:
        flash('Il link di reset password non è valido o è scaduto.', 'error')
        return redirect(url_for('auth.login'))

    if request.method == 'POST':
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        if not password or not confirm_password:
            flash('Entrambi i campi password sono obbligatori.', 'error')
            return render_template('spa.html', token=token)

        if password != confirm_password:
            flash('Le password non corrispondono.', 'error')
            return render_template('auth/reset_password_form.html', token=token)

        # Qui potresti aggiungere validatori di complessità password

        user.set_password(password)
        user.reset_token = None
        user.reset_token_expiry = None
        db.session.commit()

        flash('La tua password è stata resettata con successo. Ora puoi effettuare il login.', 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/reset_password_form.html', token=token)

@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    if request.method == 'POST':
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        email = request.form.get('email')
        phone = request.form.get('phone')
        bio = request.form.get('bio')
        dark_mode = True if request.form.get('dark_mode') else False
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        # Validate email change
        if email != current_user.email and User.query.filter_by(email=email).first():
            flash('Email già in uso', 'error')
            return redirect(url_for('auth.profile'))

        # Update user information
        current_user.first_name = first_name
        current_user.last_name = last_name
        current_user.email = email
        current_user.phone = phone
        current_user.bio = bio
        current_user.dark_mode = dark_mode

        # Handle password change if requested
        if current_password and new_password:
            if not current_user.check_password(current_password):
                flash('Password attuale non corretta', 'error')
                return redirect(url_for('auth.profile'))

            if new_password != confirm_password:
                flash('Le nuove password non corrispondono', 'error')
                return redirect(url_for('auth.profile'))

            current_user.set_password(new_password)
            flash('Password aggiornata con successo', 'success')

        try:
            db.session.commit()
            flash('Profilo aggiornato con successo', 'success')
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Profile update error: {str(e)}")
            flash('Si è verificato un errore durante l\'aggiornamento del profilo', 'error')

        return redirect(url_for('auth.profile'))

    return render_template('personnel/profile.html', user=current_user)
