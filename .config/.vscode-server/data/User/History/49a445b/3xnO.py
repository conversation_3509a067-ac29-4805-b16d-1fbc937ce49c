"""
Test per le API dei KPI.
"""
import pytest
import json
from models import KPI
from extensions import db

def test_get_kpis_unauthorized(client):
    """Test per l'endpoint GET /api/kpis/ senza autenticazione."""
    response = client.get('/api/kpis/')

    # Deve richiedere autenticazione
    assert response.status_code in [302, 401]

def test_get_kpis_empty_list(client, auth):
    """Test per l'endpoint GET /api/kpis/ con lista vuota."""
    # Login
    auth.login()

    # Richiesta all'API
    response = client.get('/api/kpis/')

    # Verifica risposta
    assert response.status_code == 200
    data = json.loads(response.data)

    # Verifica struttura risposta
    assert data['success'] is True
    assert 'data' in data
    assert 'kpis' in data['data']
    assert data['data']['kpis'] == []
    assert 'pagination' in data['data']

def test_get_kpis_with_data(client, auth, test_kpi):
    """Test per l'endpoint GET /api/kpis/ con dati."""
    # Login
    auth.login()

    # Richiesta all'API
    response = client.get('/api/kpis/')

    # Verifica risposta
    assert response.status_code == 200
    data = json.loads(response.data)

    # Verifica struttura risposta
    assert data['success'] is True
    assert 'data' in data
    assert 'kpis' in data['data']
    assert len(data['data']['kpis']) == 1

    # Verifica dati KPI
    kpi = data['data']['kpis'][0]
    assert kpi['id'] == test_kpi  # test_kpi ora è un ID
    assert kpi['name'] == 'Test KPI'
    assert kpi['description'] == 'A test KPI for API testing'
    assert kpi['category'] == 'Test'
    assert kpi['target_value'] == 100.0
    assert kpi['current_value'] == 50.0
    assert kpi['unit'] == '%'
    assert kpi['frequency'] == 'monthly'
    assert kpi['progress'] == 50.0  # 50/100 * 100

def test_get_kpis_with_filters(client, auth, app):
    """Test per l'endpoint GET /api/kpis/ con filtri."""
    # Login
    auth.login()

    # Crea KPI di test con categorie diverse (senza usare fixture test_kpi)
    with app.app_context():
        # Prima pulisci eventuali KPI esistenti per questo test
        KPI.query.delete()
        db.session.commit()

        kpi1 = KPI(name='KPI Finanziario', category='Finanziario', frequency='monthly')
        kpi2 = KPI(name='KPI Operativo', category='Operativo', frequency='weekly')
        kpi3 = KPI(name='Fatturato Mensile', category='Finanziario', frequency='monthly')

        db.session.add_all([kpi1, kpi2, kpi3])
        db.session.commit()

    # Test filtro per categoria
    response = client.get('/api/kpis/?category=Finanziario')
    assert response.status_code == 200
    data = json.loads(response.data)
    assert len(data['data']['kpis']) == 2

    # Test filtro per frequenza
    response = client.get('/api/kpis/?frequency=monthly')
    assert response.status_code == 200
    data = json.loads(response.data)
    assert len(data['data']['kpis']) == 2

    # Test ricerca
    response = client.get('/api/kpis/?search=fatturato')
    assert response.status_code == 200
    data = json.loads(response.data)
    assert len(data['data']['kpis']) == 1
    assert data['data']['kpis'][0]['name'] == 'Fatturato Mensile'

def test_get_kpi_detail_unauthorized(client, test_kpi):
    """Test per l'endpoint GET /api/kpis/<id> senza autenticazione."""
    response = client.get(f'/api/kpis/{test_kpi}')  # test_kpi è ora un ID

    # L'endpoint GET potrebbe essere accessibile senza autenticazione o richiedere login
    assert response.status_code in [200, 302, 401]

def test_get_kpi_detail_success(client, auth, test_kpi):
    """Test per l'endpoint GET /api/kpis/<id> con successo."""
    # Login
    auth.login()

    # Richiesta all'API
    response = client.get(f'/api/kpis/{test_kpi}')  # test_kpi è ora un ID

    # Verifica risposta
    assert response.status_code == 200
    data = json.loads(response.data)

    # Verifica struttura risposta
    assert data['success'] is True
    assert 'data' in data
    assert 'kpi' in data['data']

    # Verifica dati KPI
    kpi = data['data']['kpi']
    assert kpi['id'] == test_kpi  # test_kpi è ora un ID
    assert kpi['name'] == 'Test KPI'
    assert kpi['description'] == 'A test KPI for API testing'

def test_get_kpi_not_found(client, auth):
    """Test per l'endpoint GET /api/kpis/<id> con ID non esistente."""
    # Login
    auth.login()

    # Richiesta all'API con ID non esistente
    response = client.get('/api/kpis/99999')

    # Verifica risposta
    assert response.status_code == 404

def test_create_kpi_unauthorized(client):
    """Test per l'endpoint POST /api/kpis/ senza autenticazione."""
    kpi_data = {
        'name': 'Test KPI',
        'description': 'KPI di test',
        'category': 'Test'
    }

    response = client.post(
        '/api/kpis/',
        data=json.dumps(kpi_data),
        content_type='application/json'
    )

    # Deve richiedere autenticazione o permessi, ma può anche restituire 400 per validazione
    assert response.status_code in [302, 400, 401, 403]

def test_create_kpi_success(client, admin_auth, app):
    """Test per l'endpoint POST /api/kpis/ con successo."""
    # Login come admin (serve permesso EDIT_PROJECT)
    admin_auth.login()

    # Dati per il nuovo KPI
    kpi_data = {
        'name': 'Nuovo KPI Test',
        'description': 'KPI di test per i test automatici',
        'category': 'Test',
        'target_value': 100,
        'current_value': 50,
        'unit': '%',
        'frequency': 'monthly'
    }

    # Richiesta all'API
    response = client.post(
        '/api/kpis/',
        data=json.dumps(kpi_data),
        content_type='application/json'
    )

    # Verifica risposta
    assert response.status_code == 201
    data = json.loads(response.data)

    # Verifica struttura risposta
    assert data['success'] is True
    assert 'data' in data
    assert 'kpi' in data['data']
    assert 'message' in data

    # Verifica dati KPI
    kpi = data['data']['kpi']
    assert kpi['name'] == kpi_data['name']
    assert kpi['description'] == kpi_data['description']
    assert kpi['category'] == kpi_data['category']
    assert kpi['target_value'] == kpi_data['target_value']
    assert kpi['current_value'] == kpi_data['current_value']
    assert kpi['unit'] == kpi_data['unit']
    assert kpi['frequency'] == kpi_data['frequency']

    # Verifica che sia stato salvato nel database
    with app.app_context():
        saved_kpi = KPI.query.filter_by(name=kpi_data['name']).first()
        assert saved_kpi is not None
        assert saved_kpi.name == kpi_data['name']

def test_create_kpi_missing_name(client, admin_auth):
    """Test per l'endpoint POST /api/kpis/ senza nome."""
    # Login come admin
    admin_auth.login()

    # Dati senza nome
    kpi_data = {
        'description': 'KPI senza nome',
        'category': 'Test'
    }

    # Richiesta all'API
    response = client.post(
        '/api/kpis/',
        data=json.dumps(kpi_data),
        content_type='application/json'
    )

    # Verifica che la risposta sia 400 (Bad Request)
    assert response.status_code == 400
    data = json.loads(response.data)
    assert data['success'] is False
    assert 'nome' in data['message'].lower()

def test_create_kpi_duplicate_name(client, admin_auth, test_kpi):
    """Test per l'endpoint POST /api/kpis/ con nome duplicato."""
    # Login come admin
    admin_auth.login()

    # Dati con nome già esistente
    kpi_data = {
        'name': 'Test KPI',  # Nome già esistente (dalla fixture test_kpi)
        'description': 'KPI duplicato',
        'category': 'Test'
    }

    # Richiesta all'API
    response = client.post(
        '/api/kpis/',
        data=json.dumps(kpi_data),
        content_type='application/json'
    )

    # Verifica che la risposta sia 400 (Bad Request)
    assert response.status_code == 400
    data = json.loads(response.data)
    assert data['success'] is False
    assert 'esiste già' in data['message'].lower()

def test_update_kpi_success(client, admin_auth, test_kpi, app):
    """Test per l'endpoint PUT /api/kpis/<id> con successo."""
    # Login come admin
    admin_auth.login()

    # Dati per l'aggiornamento
    kpi_data = {
        'name': 'KPI Aggiornato',
        'description': 'Descrizione aggiornata',
        'current_value': 75,
        'category': 'Aggiornato'
    }

    # Richiesta all'API
    response = client.put(
        f'/api/kpis/{test_kpi}',  # test_kpi è ora un ID
        data=json.dumps(kpi_data),
        content_type='application/json'
    )

    # Verifica risposta
    assert response.status_code == 200
    data = json.loads(response.data)

    # Verifica struttura risposta
    assert data['success'] is True
    assert 'data' in data
    assert 'kpi' in data['data']
    assert 'message' in data

    # Verifica dati KPI aggiornati
    kpi = data['data']['kpi']
    assert kpi['name'] == kpi_data['name']
    assert kpi['description'] == kpi_data['description']
    assert kpi['current_value'] == kpi_data['current_value']
    assert kpi['category'] == kpi_data['category']

    # Verifica che sia stato aggiornato nel database
    with app.app_context():
        updated_kpi = KPI.query.get(test_kpi)  # test_kpi è ora un ID
        assert updated_kpi.name == kpi_data['name']
        assert updated_kpi.description == kpi_data['description']

def test_update_kpi_not_found(client, admin_auth):
    """Test per l'endpoint PUT /api/kpis/<id> con ID non esistente."""
    # Login come admin
    admin_auth.login()

    # Dati per l'aggiornamento
    kpi_data = {
        'name': 'KPI Inesistente',
        'description': 'Descrizione'
    }

    # Richiesta all'API con ID non esistente
    response = client.put(
        '/api/kpis/99999',
        data=json.dumps(kpi_data),
        content_type='application/json'
    )

    # Verifica risposta
    assert response.status_code == 404

def test_delete_kpi_success(client, admin_auth, app):
    """Test per l'endpoint DELETE /api/kpis/<id> con successo."""
    # Login come admin
    admin_auth.login()

    # Crea un KPI che può essere eliminato
    with app.app_context():
        kpi_to_delete = KPI(
            name='KPI da Eliminare',
            description='Questo KPI sarà eliminato',
            category='Test'
        )
        db.session.add(kpi_to_delete)
        db.session.commit()
        kpi_id = kpi_to_delete.id

    # Richiesta all'API
    response = client.delete(f'/api/kpis/{kpi_id}')

    # Verifica risposta
    assert response.status_code == 200
    data = json.loads(response.data)

    # Verifica struttura risposta
    assert data['success'] is True
    assert 'message' in data
    assert 'eliminato' in data['message'].lower()

    # Verifica che sia stato eliminato dal database
    with app.app_context():
        deleted_kpi = KPI.query.get(kpi_id)
        assert deleted_kpi is None

def test_delete_kpi_in_use(client, admin_auth, test_project_kpi, test_kpi):
    """Test per l'endpoint DELETE /api/kpis/<id> con KPI in uso."""
    # Login come admin
    admin_auth.login()

    # Richiesta all'API per eliminare un KPI che è in uso in un progetto
    response = client.delete(f'/api/kpis/{test_kpi}')  # test_kpi è l'ID del KPI usato nel ProjectKPI

    # Verifica che la risposta sia 400 (Bad Request)
    assert response.status_code == 400
    data = json.loads(response.data)

    # Verifica struttura risposta di errore
    assert data['success'] is False
    assert 'message' in data
    assert 'utilizzato' in data['message'].lower()

def test_delete_kpi_not_found(client, admin_auth):
    """Test per l'endpoint DELETE /api/kpis/<id> con ID non esistente."""
    # Login come admin
    admin_auth.login()

    # Richiesta all'API con ID non esistente
    response = client.delete('/api/kpis/99999')

    # Verifica risposta
    assert response.status_code == 404

def test_kpi_pagination(client, auth, app):
    """Test per la paginazione degli endpoint KPI."""
    # Login
    auth.login()

    # Crea più KPI per testare la paginazione
    with app.app_context():
        kpis = []
        for i in range(15):
            kpi = KPI(
                name=f'KPI Paginazione {i}',
                description=f'KPI numero {i}',
                category='Test'
            )
            kpis.append(kpi)

        db.session.add_all(kpis)
        db.session.commit()

    # Test con parametri di paginazione
    response = client.get('/api/kpis/?page=1&per_page=5')

    # Verifica risposta
    assert response.status_code == 200
    data = json.loads(response.data)

    # Verifica presenza paginazione
    assert 'pagination' in data['data']
    pagination = data['data']['pagination']
    assert 'page' in pagination
    assert 'per_page' in pagination
    assert 'total' in pagination
    assert 'pages' in pagination

    # Verifica che ci siano esattamente 5 KPI nella prima pagina
    assert len(data['data']['kpis']) == 5
    assert pagination['page'] == 1
    assert pagination['per_page'] == 5

def test_kpi_frequency_validation(client, admin_auth):
    """Test per la validazione dell'enum frequency."""
    # Login come admin
    admin_auth.login()

    # Test con frequenze valide
    valid_frequencies = ['daily', 'weekly', 'monthly', 'quarterly', 'annually']

    for i, frequency in enumerate(valid_frequencies):
        kpi_data = {
            'name': f'KPI {frequency} {i}',  # Nome unico
            'frequency': frequency
        }

        response = client.post(
            '/api/kpis/',
            data=json.dumps(kpi_data),
            content_type='application/json'
        )

        # Verifica che la risposta sia 201 (Created)
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['data']['kpi']['frequency'] == frequency

def test_kpi_progress_calculation(client, auth, app):
    """Test per il calcolo del progress dei KPI."""
    # Login
    auth.login()

    # Crea un KPI con valori specifici per testare il calcolo del progress
    with app.app_context():
        kpi = KPI(
            name='KPI Progress Test',
            target_value=200.0,
            current_value=100.0
        )
        db.session.add(kpi)
        db.session.commit()
        kpi_id = kpi.id

    # Richiesta all'API
    response = client.get(f'/api/kpis/{kpi_id}')

    # Verifica risposta
    assert response.status_code == 200
    data = json.loads(response.data)

    # Verifica calcolo progress (100/200 * 100 = 50%)
    kpi_data = data['data']['kpi']
    assert kpi_data['progress'] == 50.0
