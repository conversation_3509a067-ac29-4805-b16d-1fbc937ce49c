<template>
  <div class="min-h-screen bg-white dark:bg-gray-900">
    <!-- Navigation -->
    <PublicNavigation />

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-brand-primary-600 to-brand-secondary-600 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-6">
            {{ tenantStore.getPageContent('home', 'hero', 'title') }}
          </h1>
          <p class="text-xl md:text-2xl mb-8 text-brand-primary-100">
            {{ tenantStore.getPageContent('home', 'hero', 'subtitle') }}
          </p>
          <div class="space-x-4">
            <router-link
              to="/services"
              class="inline-block bg-white text-brand-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              {{ tenantStore.getPageContent('home', 'hero', 'cta_primary') }}
            </router-link>
            <router-link
              to="/contact"
              class="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-brand-primary-600 transition-colors"
            >
              {{ tenantStore.getPageContent('home', 'hero', 'cta_secondary') }}
            </router-link>
          </div>
        </div>
      </div>
    </section>

    <!-- Services Preview -->
    <section class="py-16 bg-gray-50 dark:bg-gray-800">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-brand-text-primary mb-4">
            {{ tenantStore.getPageContent('home', 'services_section', 'title') }}
          </h2>
          <p class="text-lg text-brand-text-secondary">
            {{ tenantStore.getPageContent('home', 'services_section', 'subtitle') }}
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div
            v-for="service in featuredServices"
            :key="service.id"
            class="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow"
          >
            <div class="text-brand-primary-500 mb-4">
              <i :class="service.icon" class="text-3xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-brand-text-primary mb-2">
              {{ service.name }}
            </h3>
            <p class="text-brand-text-secondary mb-4">
              {{ service.description }}
            </p>
            <router-link
              :to="`/services/${service.id}`"
              class="text-brand-primary-600 hover:text-brand-primary-700 font-medium"
            >
              Scopri di più →
            </router-link>
          </div>
        </div>
      </div>
    </section>

    <!-- News Section -->
    <section class="py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-brand-text-primary mb-4">
            Ultime notizie
          </h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <article
            v-for="news in recentNews"
            :key="news.id"
            class="bg-white dark:bg-gray-700 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
          >
            <div class="p-6">
              <div class="text-sm text-brand-text-tertiary mb-2">
                {{ formatDate(news.created_at) }}
              </div>
              <h3 class="text-xl font-semibold text-brand-text-primary mb-3">
                {{ news.title }}
              </h3>
              <p class="text-brand-text-secondary">
                {{ news.excerpt }}
              </p>
            </div>
          </article>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="bg-brand-primary-600 text-white py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-4">
          Pronto a innovare la tua azienda?
        </h2>
        <p class="text-xl mb-8 text-brand-primary-100">
          Contattaci per una consulenza gratuita
        </p>
        <router-link
          to="/contact"
          class="inline-block bg-white text-brand-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
        >
          Inizia ora
        </router-link>
      </div>
    </section>

    <!-- Footer -->
    <PublicFooter />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import PublicNavigation from '../../components/public/PublicNavigation.vue'
import PublicFooter from '../../components/public/PublicFooter.vue'

// State
const featuredServices = ref([])
const recentNews = ref([])
const isLoading = ref(false)

// Methods
async function loadData() {
  isLoading.value = true
  try {
    // Load featured services
    const servicesResponse = await fetch('/api/public/services/featured')
    if (servicesResponse.ok) {
      const servicesData = await servicesResponse.json()
      featuredServices.value = servicesData.data || []
    }

    // Load recent news
    const newsResponse = await fetch('/api/public/news/recent')
    if (newsResponse.ok) {
      const newsData = await newsResponse.json()
      recentNews.value = newsData.data || []
    }
  } catch (error) {
    console.error('Failed to load home data:', error)
  } finally {
    isLoading.value = false
  }
}

function formatDate(date) {
  return new Date(date).toLocaleDateString('it-IT', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>

<style scoped>
/* Hero gradient animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Card hover effects */
.hover\\:shadow-lg:hover {
  transform: translateY(-2px);
}

/* Smooth transitions */
.transition-shadow,
.transition-colors {
  transition-duration: 0.3s;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .text-4xl.md\\:text-6xl {
    font-size: 2.5rem;
  }

  .text-xl.md\\:text-2xl {
    font-size: 1.25rem;
  }
}
</style>
