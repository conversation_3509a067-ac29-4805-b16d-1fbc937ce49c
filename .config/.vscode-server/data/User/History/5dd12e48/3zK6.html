<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DatPortal</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <!-- CSS -->
    <link href="{{ url_for('static', filename='css/brand-variables.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/tailwind.css') }}" rel="stylesheet">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Chart.js for charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Vue.js 3 Production Build -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>

    <!-- Vue Router 4 -->
    <script src="https://unpkg.com/vue-router@4/dist/vue-router.global.prod.js"></script>

    <!-- Pinia for state management -->
    <script src="https://unpkg.com/pinia@2/dist/pinia.iife.prod.js"></script>

    <!-- Axios for HTTP requests -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

    <!-- Meta tags for SEO -->
    <meta name="description" content="DatPortal - Sistema di gestione progetti, task e risorse">
    <meta name="keywords" content="progetti, task, gestione, risorse, KPI, dashboard">
    <meta name="author" content="DatVinci">

    <!-- Open Graph meta tags -->
    <meta property="og:title" content="DatPortal">
    <meta property="og:description" content="Sistema di gestione progetti, task e risorse">
    <meta property="og:type" content="website">

    <!-- CSRF Token for API requests -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <!-- Vue.js App Container -->
    <div id="app">
        <!-- Loading spinner while Vue.js loads -->
        <div class="min-h-screen flex items-center justify-center">
            <div class="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
    </div>

    <!-- Global Configuration for Vue.js -->
    <script>
        // Global app configuration
        window.APP_CONFIG = {
            apiUrl: '/api',
            baseUrl: '{{ request.url_root }}',
            csrfToken: '{{ csrf_token() }}',
            user: {{ current_user.to_dict()|tojson if current_user.is_authenticated else 'null' }},
            isAuthenticated: {{ 'true' if current_user.is_authenticated else 'false' }},
            version: '1.0.0',
            environment: '{{ config.ENV }}',
            debug: {{ 'true' if config.DEBUG else 'false' }}
        };

        // Configure Axios defaults
        if (typeof axios !== 'undefined') {
            axios.defaults.baseURL = window.APP_CONFIG.apiUrl;
            axios.defaults.headers.common['X-CSRFToken'] = window.APP_CONFIG.csrfToken;
            axios.defaults.headers.common['Content-Type'] = 'application/json';

            // Add request interceptor for authentication
            axios.interceptors.request.use(function (config) {
                // Add timestamp to prevent caching
                config.params = config.params || {};
                config.params._t = Date.now();
                return config;
            });

            // Add response interceptor for error handling
            axios.interceptors.response.use(
                function (response) {
                    return response;
                },
                function (error) {
                    if (error.response && error.response.status === 401) {
                        // Redirect to login if unauthorized
                        window.location.href = '/auth/login';
                    }
                    return Promise.reject(error);
                }
            );
        }

        // Global error handler
        window.addEventListener('error', function(event) {
            console.error('Global error:', event.error);
            // You can send errors to a logging service here
        });

        // Global unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
            // You can send errors to a logging service here
        });
    </script>

    <!-- Vue.js Application Entry Point -->
    <script src="{{ url_for('static', filename='js/vue/main.js') }}"></script>
</body>
</html>
